'use client';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { Icon } from '@iconify/react';

const Breadcrumbs = () => {
  const pathname = usePathname();
  
  // Remove leading slash and split path into segments
  const pathSegments = pathname.split('/').filter(segment => segment);
  
  // Generate breadcrumb items
  const breadcrumbs = pathSegments.map((segment, index) => {
    // Create the path for this breadcrumb
    const path = '/' + pathSegments.slice(0, index + 1).join('/');
    
    // Format the segment text (capitalize and replace hyphens with spaces)
    const formattedText = segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    return {
      text: formattedText,
      path,
      isLast: index === pathSegments.length - 1
    };
  });

  if (breadcrumbs.length === 0) return null;

  return (
    <nav className="flex items-center text-sm text-gray-600 mb-4">
      <Link 
        href="/dashboard" 
        className="flex items-center hover:text-yellow-600 transition-colors"
      >
        <Icon icon="lucide:home" className="w-4 h-4" />
      </Link>
      
      {breadcrumbs.map((breadcrumb, index) => (
        <div key={breadcrumb.path} className="flex items-center">
          <Icon 
            icon="lucide:chevron-right" 
            className="w-4 h-4 mx-1 text-gray-400" 
          />
          
          {breadcrumb.isLast ? (
            <span className="font-medium text-gray-600">
              {breadcrumb.text}
            </span>
          ) : (
            <Link
              href={breadcrumb.path}
              className="hover:text-yellow-600 transition-colors"
            >
              {breadcrumb.text}
            </Link>
          )}
        </div>
      ))}
    </nav>
  );
};

export default Breadcrumbs;