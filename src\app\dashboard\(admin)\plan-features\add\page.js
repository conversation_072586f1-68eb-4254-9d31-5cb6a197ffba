'use client';
import React from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import api from '@/lib/api';
import FormInput from '@/components/form/FormInput';
import FormRadio from '@/components/form/FormRadio';
import { Icon } from '@iconify/react';

const validationSchema = Yup.object().shape({
  name: Yup.string().required('Name is required'),
  isActive: Yup.boolean().required('Active status is required'),
});

const activeStatusOptions = [
  { value: true, label: 'Active' },
  { value: false, label: 'Inactive' },
];

const AddPlanFeature = () => {
  const router = useRouter();
  const queryClient = useQueryClient();

  const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
    try {
      // For creating a new feature, we'll include all fields
      // If the API rejects any fields, we'll handle the error
      const payload = {
        name: values.name,
        type: 'custom', // Default type
        description: values.name, // Use name as description
        isActive: values.isActive
      };

      await api.post('/plan-features', payload);
      router.push('/dashboard/plan-features');
      queryClient.invalidateQueries(['plan-features']);
    } catch (error) {
      console.log(error);
      if (error?.response?.data?.validationErrors) {
        const validationErrors = error.response.data.validationErrors;
        Object.keys(validationErrors).forEach((field) => {
          setFieldError(`${field}`, validationErrors[field][0]);
        });

        // If the API rejects isActive or type, try again with only name
        if (validationErrors.isActive || validationErrors.type) {
          try {
            const simplePayload = {
              name: values.name,
              description: values.name
            };

            await api.post('/plan-features', simplePayload);
            router.push('/dashboard/plan-features');
            queryClient.invalidateQueries(['plan-features']);
            return;
          } catch (retryError) {
            console.log('Retry error:', retryError);
          }
        }
      } else if (error?.response?.data?.message) {
        alert(error.response.data.message);
      } else {
        alert('Failed to create plan feature. Please try again.');
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center mb-6">
        <button
          onClick={() => router.push('/dashboard/plan-features')}
          className="mr-2 text-gray-600"
        >
          <Icon icon="material-symbols:arrow-back" className="w-5 h-5" />
        </button>
        <h1 className="text-xl font-semibold">Create Feature</h1>
      </div>

      <Formik
        initialValues={{
          name: '',
          isActive: true,
        }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, values, handleChange, setFieldValue }) => (
          <Form className="bg-white p-6 rounded-lg shadow-sm">
            <div className="grid grid-cols-1 gap-6 max-w-2xl">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Feature Name<span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="name"
                  value={values.name}
                  onChange={handleChange}
                  placeholder="HEC Play"
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-yellow-500 focus:border-yellow-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Active Status<span className="text-red-500">*</span>
                </label>
                <div className="flex space-x-4">
                  {activeStatusOptions.map((option) => (
                    <label key={option.value.toString()} className="inline-flex items-center">
                      <input
                        type="radio"
                        name="isActive"
                        value={option.value.toString()}
                        checked={values.isActive === option.value}
                        onChange={() => setFieldValue('isActive', option.value)}
                        className="h-4 w-4 text-yellow-500 focus:ring-yellow-500 border-gray-300"
                      />
                      <span className="ml-2 text-gray-700">{option.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div className="flex justify-end space-x-4 pt-4">
                <button
                  type="button"
                  onClick={() => router.push('/dashboard/plan-features')}
                  className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-6 py-2 bg-yellow-400 text-black rounded-md hover:bg-yellow-500 disabled:opacity-50"
                >
                  {isSubmitting ? 'Adding...' : 'Add Feature'}
                </button>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default AddPlanFeature;
