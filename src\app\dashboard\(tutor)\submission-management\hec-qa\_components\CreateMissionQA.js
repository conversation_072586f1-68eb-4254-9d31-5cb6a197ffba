'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import api from '@/lib/api';

const CreateMissionQA = () => {
  const router = useRouter();
  
  // State for form data
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: 'weekly',
    startDate: '',
    endDate: '',
    questions: []
  });
  
  // State for available questions
  const [availableQuestions, setAvailableQuestions] = useState([]);
  
  // State for selected questions
  const [selectedQuestions, setSelectedQuestions] = useState([]);
  
  // State for loading
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  
  // Fetch available questions
  useEffect(() => {
    const fetchQuestions = async () => {
      setIsLoading(true);
      try {
        // This would be replaced with an actual API call
        // const response = await api.get('/tutor/qa/questions');
        
        // Mock data for development
        const mockData = Array(30).fill(null).map((_, index) => ({
          id: `question-${index + 1}`,
          question: `What is the meaning of ${index + 1}?`,
          category: index % 3 === 0 ? 'Grammar' : index % 3 === 1 ? 'Vocabulary' : 'Comprehension',
          difficulty: index % 4 === 0 ? 'Easy' : index % 4 === 1 ? 'Medium' : index % 4 === 2 ? 'Hard' : 'Expert'
        }));
        
        setAvailableQuestions(mockData);
      } catch (error) {
        console.error('Error fetching questions:', error);
        toast.error('Failed to load questions');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchQuestions();
  }, []);
  
  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };
  
  // Handle question selection
  const handleQuestionSelect = (question) => {
    // Check if question is already selected
    const isSelected = selectedQuestions.some(q => q.id === question.id);
    
    if (isSelected) {
      // Remove question from selected questions
      setSelectedQuestions(selectedQuestions.filter(q => q.id !== question.id));
    } else {
      // Add question to selected questions
      setSelectedQuestions([...selectedQuestions, question]);
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form
    if (!formData.title.trim()) {
      toast.error('Please enter a title');
      return;
    }
    
    if (!formData.description.trim()) {
      toast.error('Please enter a description');
      return;
    }
    
    if (!formData.startDate) {
      toast.error('Please select a start date');
      return;
    }
    
    if (!formData.endDate) {
      toast.error('Please select an end date');
      return;
    }
    
    if (new Date(formData.startDate) >= new Date(formData.endDate)) {
      toast.error('End date must be after start date');
      return;
    }
    
    if (selectedQuestions.length === 0) {
      toast.error('Please select at least one question');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Prepare data for submission
      const submissionData = {
        ...formData,
        questions: selectedQuestions.map(q => q.id)
      };
      
      // This would be replaced with an actual API call
      // await api.post('/tutor/qa/missions', submissionData);
      
      // Mock success
      setTimeout(() => {
        toast.success('Mission created successfully');
        // Reset form
        setFormData({
          title: '',
          description: '',
          type: 'weekly',
          startDate: '',
          endDate: '',
          questions: []
        });
        setSelectedQuestions([]);
      }, 1000);
    } catch (error) {
      console.error('Error creating mission:', error);
      toast.error('Failed to create mission');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Format date for input
  const formatDateForInput = (date) => {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };
  
  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Create Mission Q&A</h1>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Title */}
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
            Title
          </label>
          <input
            type="text"
            id="title"
            name="title"
            value={formData.title}
            onChange={handleInputChange}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:outline-none"
            placeholder="Enter mission title"
            required
          />
        </div>
        
        {/* Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            rows={3}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:outline-none"
            placeholder="Enter mission description"
            required
          />
        </div>
        
        {/* Type, Start Date, End Date */}
        <div className="grid grid-cols-3 gap-4">
          <div>
            <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
              Type
            </label>
            <select
              id="type"
              name="type"
              value={formData.type}
              onChange={handleInputChange}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:outline-none"
              required
            >
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
              Start Date
            </label>
            <input
              type="date"
              id="startDate"
              name="startDate"
              value={formData.startDate}
              onChange={handleInputChange}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:outline-none"
              required
            />
          </div>
          
          <div>
            <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
              End Date
            </label>
            <input
              type="date"
              id="endDate"
              name="endDate"
              value={formData.endDate}
              onChange={handleInputChange}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:outline-none"
              required
            />
          </div>
        </div>
        
        {/* Question Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Select Questions ({selectedQuestions.length} selected)
          </label>
          
          {isLoading ? (
            <div className="flex justify-center items-center h-40">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-500"></div>
            </div>
          ) : (
            <div className="border border-gray-300 rounded-lg p-4 max-h-80 overflow-y-auto">
              <div className="space-y-2">
                {availableQuestions.map((question) => (
                  <div
                    key={question.id}
                    className={`p-3 rounded-lg cursor-pointer transition-colors ${
                      selectedQuestions.some(q => q.id === question.id)
                        ? 'bg-yellow-100 border border-yellow-300'
                        : 'bg-gray-50 hover:bg-gray-100 border border-gray-200'
                    }`}
                    onClick={() => handleQuestionSelect(question)}
                  >
                    <div className="flex justify-between items-center">
                      <div className="flex-1">
                        <p className="font-medium">{question.question}</p>
                        <div className="flex items-center mt-1 text-sm text-gray-500">
                          <span className="mr-3">{question.category}</span>
                          <span>{question.difficulty}</span>
                        </div>
                      </div>
                      <div className="ml-4">
                        <input
                          type="checkbox"
                          checked={selectedQuestions.some(q => q.id === question.id)}
                          onChange={() => {}}
                          className="h-5 w-5 text-yellow-500 focus:ring-yellow-400 rounded"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
        
        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isSubmitting || isLoading}
            className={`px-6 py-3 rounded-lg ${
              isSubmitting || isLoading
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-yellow-500 text-white hover:bg-yellow-600'
            }`}
          >
            {isSubmitting ? 'Creating...' : 'Create Mission'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default CreateMissionQA;
