'use client';

import React, { useState, useEffect } from 'react';
import api from '@/lib/api';
import NewTablePage from '@/components/form/NewTablePage';
import { useRouter } from 'next/navigation';
import Image from 'next/image';

const MissionEssayList = () => {
  const router = useRouter();
  // State variables
  const [missions, setMissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchField, setSearchField] = useState('title'); // Can be 'title' or 'week'
  const [sortField, setSortField] = useState('createdAt');
  const [sortDirection, setSortDirection] = useState('ASC');
  const [activeTab, setActiveTab] = useState(0); // 0 for Weekly, 1 for Monthly
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  // Add new state for selected mission data
  const [selectedMission, setSelectedMission] = useState(null);
  const [fetchingMission, setFetchingMission] = useState(false);
  // Add state for week or month number - initially empty
  const [weekOrMonth, setWeekOrMonth] = useState('');
  // Add state for view modal
  const [showViewModal, setShowViewModal] = useState(false);
  const [viewMissionData, setViewMissionData] = useState(null);

  // Tabs content
  const tabs = [
    { name: 'Weekly Mission', frequency: 'weekly' },
    { name: 'Monthly Mission', frequency: 'monthly' }
  ];

  // Generate week options (1-7) for dropdown
  const weekOptions = Array.from({ length:48 }, (_, i) => ({
    label: `Week ${i + 1}`,
    value: (i + 1).toString(),
  }));

  // Generate month options (1-12) for dropdown
  const monthOptions = Array.from({ length: 12 }, (_, i) => ({
    label: `Month ${i + 1}`,
    value: (i + 1).toString(),
  }));


  // Dynamic name filter options based on active tab
  const getNameFilterOptions = () => {
    if (activeTab === 0) { // Weekly tab
      return [
        { label: 'All', value: '' }, // Add "All" option
        { label: 'Title', value: 'title' },
        { label: 'Week', value: 'week' }, // UI label is 'Week' but will use 'weekOrMonth' parameter
      ];
    } else { // Monthly tab
      return [
        { label: 'All', value: '' }, // Add "All" option
        { label: 'Title', value: 'title' },
        { label: 'Month', value: 'month' }, // UI label is 'Month' but will use 'weekOrMonth' parameter
      ];
    }
  };

  // Table columns definition
  const columns = activeTab === 0 
    ? [
        { label: 'WEEK', field: 'week' },
        { label: 'MISSION TITLE', field: 'title'},
        { label: 'WORD LIMIT', field: 'wordLimit' },
        { label: 'DEADLINE', field: 'deadline' },
      ]
    : [
        { label: 'MONTH', field: 'month' },
        { label: 'MISSION TITLE', field: 'title'},
        { label: 'WORD LIMIT', field: 'wordLimit' },
        { label: 'DEADLINE', field: 'deadline' },
      ];

  // Function to fetch a single mission by ID
  const fetchMissionById = async (missionId) => {
    try {
      setFetchingMission(true);
      
      // API call to get the specific mission
      const response = await api.get(`/admin-essay/${missionId}`);
      
      if (response && response.success) {
        console.log('Fetched mission details:', response.data);
        setSelectedMission(response.data);
        return response.data;
      } else {
        console.error('Failed to fetch mission details:', response?.message || 'Unknown error');
        return null;
      }
    } catch (error) {
      console.error('Error fetching mission details:', error);
      return null;
    } finally {
      setFetchingMission(false);
    }
  };

  // Handle edit action - navigate to the createMissionEssay page with the mission data
  const handleEditMission = async (mission) => {
    // Extract the actual mission ID from the composite ID
    const missionId = mission.missionId;
    
    console.log('Fetching mission details for editing:', missionId);
    const missionDetails = await fetchMissionById(missionId);
    
    if (missionDetails) {
      // Store both mission details and ID in session storage
      sessionStorage.setItem('editMissionData', JSON.stringify(missionDetails));
      sessionStorage.setItem('editMissionId', missionId);
      
      // Navigate to editor without any ID in the URL
      router.push(`/dashboard/module-management/hec-essay/mission-essay-editor?edit=true`);
    }
  };

  // Handle view action - show mission details in modal
  const handleViewMission = async (mission) => {
    // Extract the actual mission ID from the composite ID
    const missionId = mission.missionId;
    
    console.log('Fetching mission details for viewing:', missionId);
    const missionDetails = await fetchMissionById(missionId);
    
    if (missionDetails) {
      // Find the specific task from the mission details
      const taskId = mission.taskId;
      const task = missionDetails.tasks.find(t => t.id === taskId);
      
      if (task) {
        // Prepare data for the view modal
        setViewMissionData({
          title: mission.title,
          timeFrequency: mission.timeFrequency === 'weekly' ? 'Weekly' : 'Monthly',
          selectedPeriod: mission.timeFrequency === 'weekly' 
            ? `Week ${task.metaData?.week || ''}` 
            : `Month ${task.metaData?.month || ''}`,
          deadline: `${task.deadline || 0} ${task.deadline === 1 ? 'day' : 'days'}`,
          wordLimitMax: task.wordLimitMaximum || 0,
          wordLimitMin: task.wordLimitMinimum || 0,
          instruction: task.instructions || '',
          description: task.description || '',
          // Format the deadline date if available
          deadlineDate: task.deadlineDate ? new Date(task.deadlineDate).toLocaleDateString('en-US', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
          }) : '30th January, 2024' // Fallback date from the Figma design
        });
        
        // Show the modal
        setShowViewModal(true);
      }
    }
  };

  // Table actions definition with updated edit handler
  const actions = [
    {
      icon: 'heroicons-outline:pencil',
      className: 'text-gray-600 hover:text-blue-600',
      onClick: handleEditMission
    },
    {
      icon: 'heroicons-outline:eye',
      className: 'text-blue-600 hover:text-blue-700',
      onClick: handleViewMission
    }
  ];

  // Close view modal
  const handleCloseModal = () => {
    setShowViewModal(false);
    setViewMissionData(null);
  };

  // Fetch data from API
  const fetchMissions = async () => {
    try {
      setLoading(true);
      
      // Base params required by the API
      const params = {
        page: currentPage,
        limit: rowsPerPage,
        timeFrequency: tabs[activeTab].frequency,
        sortDirection: sortDirection
      };
      
      // Set sortBy based on the searchField for proper column sorting
      if (searchField) {
        params.sortBy = searchField;
      }
      
      // Add search parameters based on searchField type
      if (searchField === 'title' && searchTerm) {
        params.title = searchTerm;
      } 
      else if ((searchField === 'week' || searchField === 'month') && weekOrMonth) {
        // Always use 'weekOrMonth' as the parameter name for both weekly and monthly searches
        params.weekOrMonth = weekOrMonth;
      }
      
      // Log the params being sent to the API for debugging
      console.log('API request params:', params);
      const response = await api.get('/admin-essay/missions', { params });

      if (response && response.success) {
        // Format mission data for table display - expanding tasks into individual rows
        const missionItems = response.data.items || [];
        
        // Sort missions by sequenceNumber if necessary
        const sortedMissions = [...missionItems].sort((a, b) => 
          (a.sequenceNumber || 0) - (b.sequenceNumber || 0)
        );
        
        let formattedMissions = [];
        
        sortedMissions.forEach((mission) => {
          // Skip missions with no tasks
          if (!mission.tasks || mission.tasks.length === 0) {
            return;
          }
          
          // Create a row for each task in the mission
          mission.tasks.forEach(task => {
            // Ensure week and month numbers are properly displayed
            const weekValue = task.metaData?.week !== undefined ? parseInt(task.metaData.week) : null;
            const monthValue = task.metaData?.month !== undefined ? parseInt(task.metaData.month) : null;
            
            formattedMissions.push({
              id: `${mission.id}-${task.id}`, // Composite ID for uniqueness
              missionId: mission.id, // Keep reference to parent mission
              taskId: task.id,
              sequenceNumber: mission.sequenceNumber || 0,
              week: weekValue !== null ? `Week ${weekValue}` : '',  
              month: monthValue !== null ? `Month ${monthValue}` : '',
              title: task.title || 'Untitled Mission',
              wordLimit: `${task.wordLimitMinimum} - ${task.wordLimitMaximum}`,
              deadline: `${task.deadline} ${task.deadline === 1 ? 'day' : 'days'}`,
              description: task.description,
              instructions: task.instructions,
              isActive: task.isActive,
              timeFrequency: mission.timeFrequency
            });
          });
        });
        
        setMissions(formattedMissions);
        // Use correct pagination values from the API response
        setTotalItems(response.data.totalItems || 0);
        setTotalPages(response.data.totalPages || 1);
      } else {
        console.error('Failed to fetch missions:', response?.message || 'Unknown error');
      }
    } catch (error) {
      console.error('Error fetching missions:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMissions();
  }, [activeTab, currentPage, rowsPerPage, sortDirection, searchTerm, searchField, weekOrMonth]);

  // Reset page and search when changing tabs
  useEffect(() => {
    setCurrentPage(1);
    setSearchTerm('');
    setWeekOrMonth('');
    setSearchField(''); // Reset to empty to show all missions
  }, [activeTab]);

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle search
  const handleSearch = (value) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page on new search
    
    // Clear weekOrMonth when searching by title
    if (searchField === 'title') {
      setWeekOrMonth('');
    }
  };

  // Handle week/month selection
  const handleWeekOrMonthChange = (value) => {
    setWeekOrMonth(value);
    setCurrentPage(1); // Reset to first page on filter change
    
    // Clear text search when using week/month filter
    if (searchField === 'week' || searchField === 'month') {
      setSearchTerm('');
      // Log for debugging the request format
      console.log('Setting weekOrMonth parameter to:', value);
    }
  };

  // Handle name filter change
  const handleNameFilterChange = (field) => {
    setSearchField(field);
    setCurrentPage(1); // Reset to first page on filter change
    
    // Clear search values when changing filter type
    setSearchTerm('');
    setWeekOrMonth('');
    
    // Update sortBy to match the searchField for proper column sorting if field is not empty
    if (field) {
      setSortField(field);
    }
  };

  // Handle sort change
  const handleSort = (field) => {
    if (field === sortField) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');
    } else {
      // New field, default to ascending
      setSortField(field);
      setSortDirection('ASC');
    }
    setCurrentPage(1); // Reset to first page on sort change
  };

  // Tab switching handler
  const handleTabChange = (index) => {
    setActiveTab(index);
  };

  // Create a new mission handler
  const handleCreateMission = () => {
    router.push('/dashboard/module-management/hecEssay/createMissionEssay');
  };

  // ViewModal Component with fixed image rendering
  const ViewModal = ({ isOpen, onClose, missionData }) => {
    if (!isOpen || !missionData) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white shadow-lg mx-4 overflow-hidden [width:970px] [height:550px] [top:305px] [left:475px] [border-radius:20px] absolute">
          {/* Modal Header with wooden sign design */}
          <div className="relative">
            <div className="bg-[#FFF9FB] pt-6 pb-8 px-6 border-b border-amber-200">
              <div className="flex justify-center mb-2">
                <div className="relative w-full max-w-md">
                  {/* Wooden sign background - Fixed image path */}
                  <Image
                    src={'/assets/images/all-img/missionessay-bg.png'}
                    alt="Mission Essay"
                    width={600}
                    height={200}
                    className="w-full h-auto"
                    priority
                  />
                </div>
              </div>
              
              {/* Close button */}
              <button 
                onClick={onClose} 
                className="absolute top-4 right-4 w-8 h-8 flex items-center justify-center bg-yellow-500 hover:bg-yellow-600 text-white rounded-full"
              >
                <Image
                  src={'/assets/images/all-img/cross-bg.png'}
                  alt="Mission Essay"
                  width={600}
                  height={200}
                  className="w-full h-auto"
                  priority
                />
              </button>
            </div>
          </div>
          
          {/* Modal Body - Mission Details */}
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
              <div className="flex items-center space-x-2">
                <p className="text-black text-md font-bold">Mission Essay Title:</p>
                <p className="text-gray-900">{missionData.title}</p>
              </div>
              
              <div className="flex items-center space-x-2">
                <p className="text-black text-md font-bold">Time Frequency:</p>
                <p className="text-gray-900">{missionData.timeFrequency}</p>
              </div>
              
              <div className="flex items-center space-x-2">
                <p className="text-black text-md font-bold">Selected {missionData.timeFrequency === 'Weekly' ? 'Week' : 'Month'}:</p>
                <p className="text-gray-900">{missionData.selectedPeriod}</p>
              </div>
              
              <div className="flex items-center space-x-2">
                <p className="text-black text-md font-bold">Word Limit (Maximum):</p>
                <p className="text-gray-900">{missionData.wordLimitMax}</p>
              </div>
              
              <div className="flex items-center space-x-2">
                <p className="text-black text-md font-bold">Mission Deadline:</p>
                <p className="text-gray-900">{missionData.deadlineDate}</p>
              </div>
              
              <div className="flex items-center space-x-2">
                <p className="text-black text-md font-bold">Word Limit (Minimum):</p>
                <p className="text-gray-900">{missionData.wordLimitMin}</p>
              </div>
              
              <div className="flex items-center space-x-2">
                <p className="text-black text-md font-bold">Instruction:</p>
                <p className="text-gray-900">{missionData.instruction}</p>
              </div>

              <div className="flex items-center space-x-2">
                <p className="text-black text-md font-bold">Description:</p>
                <p className="text-gray-900">{missionData.description}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Mission Essay List</h1>
        {/* <button
          onClick={handleCreateMission}
          className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-md"
        >
          Create Mission
        </button> */}
      </div>
      
      {/* Tab buttons */}
      <div className="flex border-b border-gray-200 mb-6">
        {tabs.map((tab, index) => (
          <button
            key={index}
            className={`py-3 px-6 font-medium text-sm focus:outline-none ${
              activeTab === index 
                ? 'text-black border-b-2 border-yellow-500 hover:bg-[#FEFCE8]' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => handleTabChange(index)}
          >
            {tab.name}
          </button>
        ))}
      </div>
      
      {/* Search and filter section */}
      <div className="flex flex-wrap gap-4 mb-6">
        {/* Name filter selector - fixed width */}
        <div className="w-48">
          <label className="block text-sm font-medium text-gray-700 mb-1">Search by</label>
          <select
            value={searchField}
            onChange={(e) => handleNameFilterChange(e.target.value)}
            className="w-full h-10 border border-gray-300 rounded-md px-3 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All</option>
            {getNameFilterOptions().filter(option => option.value !== '').map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
        
        {/* Dynamic search input based on selected search field */}
        {searchField === 'title' ? (
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">Search mission title</label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              placeholder="Enter mission title..."
              className="w-full h-10 border border-gray-300 rounded-md px-3 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        ) : searchField === 'week' ? (
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Select week number
            </label>
            <select
              value={weekOrMonth}
              onChange={(e) => handleWeekOrMonthChange(e.target.value)}
              className="w-full h-10 border border-gray-300 rounded-md px-3 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All weeks</option>
              {weekOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        ) : searchField === 'month' ? (
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Select month number
            </label>
            <select
              value={weekOrMonth}
              onChange={(e) => handleWeekOrMonthChange(e.target.value)}
              className="w-full h-10 border border-gray-300 rounded-md px-3 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All months</option>
              {monthOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>

              ))}
            </select>
          </div>
        ) : (
          // When "All" is selected, show nothing or a message
          <div className="flex-1 flex items-end">
           <p className="w-full h-10 border border-gray-300 rounded-md px-3 flex items-center focus:ring-blue-500 focus:border-blue-500">
  Showing all missions
</p>

          </div>
        )}
      </div>
      
      {/* Show loading indicator when fetching individual mission */}
      {fetchingMission && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg shadow-lg">
            <p className="text-gray-700">Loading mission data...</p>
          </div>
        </div>
      )}
      
      {/* View Mission Modal */}
      <ViewModal
        isOpen={showViewModal}
        onClose={handleCloseModal}
        missionData={viewMissionData}
      />
      
      {/* Table component */}
      <NewTablePage
        title=""
        createButton="Create Mission"
        showCreateButton={false}
        columns={columns}
        actions={actions}
        data={missions}
        loading={loading}
        currentPage={currentPage}
        totalPages={totalPages}
        changePage={handlePageChange}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        // Search and filter props
        showSearch={false} // We're handling search manually
        showNameFilter={false} // We're handling filters manually
        showSortFilter={false}
        onSort={handleSort}
        sortField={sortField}
        sortDirection={sortDirection}
        showCheckboxes={false}
      />
      
      {missions.length === 0 && !loading && (
        <div className="text-center py-8 text-gray-500">
          No missions found. {searchTerm || weekOrMonth ? 'Try adjusting your search criteria.' : ''}
        </div>
      )}
    </div>
  );
};

export default MissionEssayList;