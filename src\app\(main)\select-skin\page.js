'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import useDataFetch from '@/hooks/useDataFetch';
import { Icon } from '@iconify/react';
import { toast } from 'sonner';
import api from '@/lib/api';
import { useSelector } from 'react-redux';

const SkinSelector = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectingSkinId, setSelectingSkinId] = useState(null);
  const [selectedSkinId, setSelectedSkinId] = useState(null); // Track selected skin locally
  const itemsPerPage = 9;
  const router = useRouter();

  // Get user profile from Redux store
  const user = useSelector((state) => state.auth.user);

  // Initialize selectedSkinId with user's default skin when component mounts
  useEffect(() => {
    setSelectedSkinId(user?.defaultSkinId || null);
  }, [user]);

  const { data, isLoading, error } = useDataFetch({
    queryKey: ['diary-skins', currentPage],
    endPoint: `/diary/skins?page=${currentPage}&limit=${itemsPerPage}`,
  });

  // Calculate total pages when data changes
  useEffect(() => {
    if (data && data.totalCount) {
      setTotalPages(Math.ceil(data.totalCount / itemsPerPage));
    }
  }, [data]);

  // Handle skin selection
  const handleSkinSelect = async (skin) => {
    // Skip if already selected
    if (selectedSkinId === skin.id) return;
    
    // Set the selecting skin ID to show loading state
    setSelectingSkinId(skin.id);
    setSelectedSkinId(skin.id); // Immediately update local selection

    try {
      // Set this skin as the default skin via API
      await api.patch('/diary/default-skin', {
        skinId: skin.id,
      });
      
      // Only show toast if this was a new selection (not initial load)
      if (user?.defaultSkinId !== skin.id) {
        // toast.success('Skin selected successfully');
      }
    } catch (error) {
      console.error('Error setting default skin:', error);
      setSelectedSkinId(user?.defaultSkinId || null); // Revert on error
      toast.error('Failed to select skin');
    } finally {
      setSelectingSkinId(null);
    }
  };

  // Navigate to diary page
  const handleGoClick = () => {
    router.push('/diary');
  };

  // Handle create your own skin click
  const handleCreateOwnSkin = () => {
    router.push('/create-skin');
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages && !isLoading) {
      window.scrollTo(0, 0);
      setCurrentPage(newPage);
    }
  };

  return (
    <div className="min-h-screen bg-[#FFFDF5] py-12">
      <div className="container mx-auto px-4">
        <div className="w-full max-w-6xl mx-auto p-6 bg-white rounded-lg border border-yellow-200">
          {/* Header */}
          <div className="bg-yellow-100 -m-6 p-4 mb-6 flex items-center">
            <div className="w-6 h-6 rounded-full bg-yellow-500 flex items-center justify-center mr-2">
              <span className="text-white text-xs">✓</span>
            </div>
            <span className="text-gray-700 font-medium">Select Skin</span>
          </div>

          {/* Title and Go button */}
          <div className="flex justify-between items-center mb-8">
            <div className="ml-4">
              <h2 className="text-2xl font-bold text-gray-700">
                Choose your Skin
              </h2>
            </div>
            <button
              onClick={handleGoClick}
              disabled={!selectedSkinId}
              className={`px-6 py-2 rounded-xl text-white text-xl font-medium transition-all border-2 border-white ${
                selectedSkinId
                  ? 'bg-gradient-to-b from-yellow-400 via-yellow-500 to-amber-600 hover:from-yellow-500 hover:to-amber-700'
                  : 'bg-gray-400 cursor-not-allowed'
              }`}
            >
              Go to Diary
            </button>
          </div>

          {/* Loading/Error State */}
          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
            </div>
          ) : error ? (
            <div className="text-center text-red-500 py-20">
              Failed to load skins. Please try again later.
            </div>
          ) : (
            <>
              {/* Skins Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-8 px-2">
                <div
                  className="h-52 bg-yellow-100 flex flex-col items-center justify-center p-4 cursor-pointer"
                  onClick={handleCreateOwnSkin}
                >
                  <div className="w-12 h-12 rounded-full bg-yellow-500 flex items-center justify-center mb-2">
                    <span className="text-white text-2xl">+</span>
                  </div>
                  <h3 className="text-yellow-700 font-bold text-center">
                    Create Your
                    <br />
                    Own Skin
                  </h3>
                </div>
                {data && data.items && data.items.length > 0 ? (
                  data.items.map((skin) => (
                    <div
                      key={skin.id}
                      className={`relative border rounded-lg overflow-hidden ${
                        selectingSkinId === skin.id
                          ? 'cursor-wait'
                          : 'cursor-pointer hover:shadow-lg'
                      } transition-all duration-200 ${
                        selectedSkinId === skin.id
                          ? 'border-yellow-500 shadow-md border-2'
                          : 'border-gray-100'
                      }`}
                      onClick={() => handleSkinSelect(skin)}
                    >
                      {/* Selection indicator */}
                      {selectedSkinId === skin.id && (
                        <div className="absolute top-2 right-2 w-6 h-6 rounded-full bg-yellow-500 flex items-center justify-center z-10">
                          <span className="text-white text-xs">✓</span>
                        </div>
                      )}

                      {/* Loading indicator */}
                      {selectingSkinId === skin.id && (
                        <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center z-20">
                          <div className="bg-white bg-opacity-90 rounded-full p-3">
                            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-yellow-500"></div>
                          </div>
                        </div>
                      )}

                      <div className="h-52 flex items-center justify-center bg-white relative">
                        <div className="absolute inset-1 border-2 border-yellow-200 rounded-lg"></div>
                        <div className="relative z-10 w-full h-full flex items-center justify-center p-2">
                          <Image
                            src={
                              skin.previewImagePath ||
                              '/assets/images/placeholder.png'
                            }
                            alt={skin.name}
                            width={240}
                            height={180}
                            className="object-contain max-w-full max-h-full"
                          />
                          <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-80 p-2 text-center">
                            <p className="text-sm font-medium text-gray-800">
                              {skin.name}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="col-span-2 h-52 flex flex-col items-center justify-center bg-gray-50 rounded-lg border border-gray-200">
                    <p className="text-gray-500 mb-2">No skins available</p>
                    <p className="text-sm text-gray-400">
                      Create your own skin or check back later
                    </p>
                  </div>
                )}
              </div>

              {/* Pagination Controls */}
              {data && data.totalCount > itemsPerPage && (
                <div className="flex justify-center items-center mt-6 mb-4">
                  <button
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1 || isLoading}
                    className={`mx-1 p-2 rounded-md ${
                      currentPage === 1 || isLoading
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-yellow-600 hover:bg-yellow-100'
                    }`}
                    aria-label="First page"
                  >
                    <Icon icon="mdi:page-first" className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1 || isLoading}
                    className={`mx-1 p-2 rounded-md ${
                      currentPage === 1 || isLoading
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-yellow-600 hover:bg-yellow-100'
                    }`}
                    aria-label="Previous page"
                  >
                    <Icon icon="mdi:chevron-left" className="w-5 h-5" />
                  </button>

                  <div className="mx-2 text-sm flex items-center">
                    {isLoading ? (
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-yellow-500 mr-2"></div>
                        <span className="text-gray-500">Loading...</span>
                      </div>
                    ) : (
                      <>
                        <span className="font-medium text-yellow-600">
                          {currentPage}
                        </span>
                        <span className="text-gray-500"> of </span>
                        <span className="font-medium text-gray-700">
                          {totalPages}
                        </span>
                      </>
                    )}
                  </div>

                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages || isLoading}
                    className={`mx-1 p-2 rounded-md ${
                      currentPage === totalPages || isLoading
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-yellow-600 hover:bg-yellow-100'
                    }`}
                    aria-label="Next page"
                  >
                    <Icon icon="mdi:chevron-right" className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage === totalPages || isLoading}
                    className={`mx-1 p-2 rounded-md ${
                      currentPage === totalPages || isLoading
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-yellow-600 hover:bg-yellow-100'
                    }`}
                    aria-label="Last page"
                  >
                    <Icon icon="mdi:page-last" className="w-5 h-5" />
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default SkinSelector;