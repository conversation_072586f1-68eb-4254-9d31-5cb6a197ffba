'use client';

import React, { useState } from 'react';
import { useSearchParams } from 'next/navigation';
import api from '@/lib/api';
import Link from 'next/link';
import { Icon } from '@iconify/react';
import { Formik } from 'formik';
import FormInput from '@/components/form/FormInput';
import { Form } from 'formik';
import * as Yup from 'yup';

const passwordSchema = Yup.object().shape({
  newPassword: Yup.string()
    .required('Please enter a new password')
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special (@, $, !, %, *, ?, or &) character'
    ),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('newPassword'), null], 'Passwords must match')
    .required('Please confirm your new password'),
});

export default function LoginPage() {
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const [showPassword, setShowPassword] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  const handleResetPassword = async (
    values,
    { setSubmitting, setFieldError }
  ) => {
    try {
      setSubmitting(true);

      const response = await api.post('/auth/reset-password', {
        token: token,
        newPassword: values.newPassword,
      });
      setSuccessMessage(response?.message);
    } catch (error) {
      if (error?.response?.data?.validationErrors) {
        const validationErrors = error?.response?.data?.validationErrors;
        for (const key in validationErrors) {
          if (validationErrors.hasOwnProperty(key)) {
            setFieldError(key, validationErrors[key]);
          }
        }
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div>
      <div className="text-center space-y-2 mb-5">
        <h1 className="text-3xl font-bold text-gray-900">
          HELLO ENGLISH COACHING - HEC
        </h1>

        <p className="text-gray-600">Reset your account password.</p>

        {successMessage?.length > 0 ? (
          <div className="text-center text-green-500">
            <p className="">{successMessage}</p>
            <Link
              className="py-2 px-4 bg-yellow-500 hover:bg-yellow-600 text-white rounded-md inline-block mt-4"
              href="/login"
            >
              Go to Login
            </Link>
          </div>
        ) : (
          <Formik
            initialValues={{
              newPassword: '',
              confirmPassword: '',
            }}
            validationSchema={passwordSchema}
            onSubmit={handleResetPassword}
          >
            {({ isSubmitting, values }) => (
              <Form className="space-y-4 text-start max-w-md mx-auto">
                <p className="text-yellow-600 text-center text-sm">
                  Password must contain at least one uppercase letter, one
                  lowercase letter, one number, and one special (@, $, !, %, *,
                  ?, or &) character.
                </p>
                <div className="relative">
                  <FormInput
                    label="New Password"
                    name="newPassword"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter new password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-[38px] transform text-gray-500"
                  >
                    <Icon
                      icon={showPassword ? 'mdi:eye-off' : 'mdi:eye'}
                      width="20"
                      height="20"
                    />
                  </button>
                </div>

                <div className="relative">
                  <FormInput
                    label="Confirm Password"
                    name="confirmPassword"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Confirm password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-[38px] transform text-gray-500"
                  >
                    <Icon
                      icon={showPassword ? 'mdi:eye-off' : 'mdi:eye'}
                      width="20"
                      height="20"
                    />
                  </button>
                </div>

                <button
                  type="submit"
                  disabled={
                    isSubmitting ||
                    !values.newPassword ||
                    !values.confirmPassword
                  }
                  className={`w-full py-2 px-4 rounded-md transition-colors ${
                    isSubmitting ||
                    !values.newPassword ||
                    !values.confirmPassword
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-yellow-300 text-gray-800 hover:bg-yellow-400'
                  }`}
                >
                  {isSubmitting ? 'Submitting...' : 'Submit'}
                </button>
              </Form>
            )}
          </Formik>
        )}
      </div>
    </div>
  );
}
