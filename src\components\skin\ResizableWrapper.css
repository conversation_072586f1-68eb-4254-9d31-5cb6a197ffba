/* Custom styles for the Moveable component */

/* Make the resize handles more visible */
.moveable-control {
  width: 8px !important;
  height: 8px !important;
  margin-top: -4px !important;
  margin-left: -4px !important;
  background-color: #2563eb !important;
  border: 1px solid white !important;
  border-radius: 2px !important;
}

/* Style the border lines when selected */
.moveable-line {
  background-color: #2563eb !important;
  height: 1px !important;
}

/* Make sure the handles are properly positioned */
.moveable-direction.moveable-nw {
  top: 0 !important;
  left: 0 !important;
}

.moveable-direction.moveable-ne {
  top: 0 !important;
  right: 0 !important;
}

.moveable-direction.moveable-sw {
  bottom: 0 !important;
  left: 0 !important;
}

.moveable-direction.moveable-se {
  bottom: 0 !important;
  right: 0 !important;
}

.moveable-direction.moveable-n {
  top: 0 !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
}

.moveable-direction.moveable-s {
  bottom: 0 !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
}

.moveable-direction.moveable-w {
  left: 0 !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

.moveable-direction.moveable-e {
  right: 0 !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

/* Ensure the handles are above other elements */
.moveable-control-box {
  z-index: 1000 !important;
}

/* Delete button styles */
.delete-icon {
  position: absolute;
  top: -25px;
  right: -25px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #ff4d4f;
  color: white;
  border: 2px solid white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  z-index: 1001;
  transition: all 0.2s ease;
}

.delete-icon:hover {
  background: #ff7875;
  transform: scale(1.1);
}
