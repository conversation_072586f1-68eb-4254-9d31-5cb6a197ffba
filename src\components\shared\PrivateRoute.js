'use client';

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useSelector } from 'react-redux';
import Cookies from 'js-cookie';

const PrivateRoute = ({ children }) => {
  const router = useRouter();
  const pathname = usePathname();
  const token = Cookies.get('token');
  const isAuth = useSelector((state) => state.auth.isAuth);

  useEffect(() => {
    if (!token && !isAuth) {
      // Redirect to login with the current path as returnTo
      router.push(`/login?returnTo=${encodeURIComponent(pathname)}`);
    }
  }, [token, isAuth, router, pathname]);

  // Show nothing while checking authentication
  if (!token || !isAuth) {
    return null;
  }

  // Render children if authenticated
  return <>{children}</>;
};

export default PrivateRoute;
