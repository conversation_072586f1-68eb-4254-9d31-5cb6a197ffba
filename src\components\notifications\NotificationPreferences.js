import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Icon } from '@iconify/react';
import {
  fetchNotificationPreferences,
  updateNotificationPreferences,
  selectNotificationPreferences,
} from '@/store/features/notificationSlice';

// Notification type descriptions
const notificationTypes = {
  DIARY_COMMENT: 'New comment on a diary entry',
  DIARY_REVIEWED: 'Diary entry has been reviewed',
  TUTOR_ASSIGNED: 'New tutor assigned to student',
  TUTOR_GREETING: 'Student set greeting message',
  NEW_MESSAGE: 'New chat message received',
  PLAN_PURCHASED: 'Subscription plan purchased',
  ACCOUNT_VERIFIED: 'Account email verified',
  PASSWORD_RESET: 'Password reset completed',
  ESSAY_SUBMITTED: 'New essay submitted',
  ESSAY_REVIEWED: 'Essay has been reviewed',
  SYSTEM_ANNOUNCEMENT: 'System-wide announcement',
};

// Channel descriptions
const channels = {
  email: 'Email',
  push: 'Push',
  inApp: 'In-App',
};

const NotificationPreferences = () => {
  const dispatch = useDispatch();
  const preferences = useSelector(selectNotificationPreferences);
  const [localPreferences, setLocalPreferences] = useState(preferences);
  const [isSaving, setIsSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);

  // Fetch preferences on component mount
  useEffect(() => {
    dispatch(fetchNotificationPreferences());
  }, [dispatch]);

  // Update local state when preferences change
  useEffect(() => {
    setLocalPreferences(preferences);
  }, [preferences]);

  const handleToggle = (channel, type) => {
    setLocalPreferences((prev) => ({
      ...prev,
      [channel]: {
        ...prev[channel],
        [type]: !prev[channel]?.[type],
      },
    }));
  };

  const handleSavePreferences = async () => {
    setIsSaving(true);
    setSaveSuccess(false);

    try {
      await dispatch(updateNotificationPreferences(localPreferences)).unwrap();
      setSaveSuccess(true);
      
      // Reset success message after 3 seconds
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
    } catch (error) {
      console.error('Failed to save preferences:', error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800">Notification Preferences</h2>
        <button
          className={`px-4 py-2 rounded-md text-white font-medium ${
            isSaving
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-yellow-500 hover:bg-yellow-600'
          }`}
          onClick={handleSavePreferences}
          disabled={isSaving}
        >
          {isSaving ? 'Saving...' : 'Save Changes'}
        </button>
      </div>

      {saveSuccess && (
        <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-md flex items-center">
          <Icon icon="lucide:check-circle" className="w-5 h-5 mr-2" />
          Preferences saved successfully!
        </div>
      )}

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Notification Type
              </th>
              {Object.keys(channels).map((channel) => (
                <th
                  key={channel}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {channels[channel]}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {Object.keys(notificationTypes).map((type) => (
              <tr key={type} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  {notificationTypes[type]}
                </td>
                {Object.keys(channels).map((channel) => (
                  <td key={`${type}-${channel}`} className="px-6 py-4 whitespace-nowrap">
                    <label className="inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only"
                        checked={!!localPreferences[channel]?.[type]}
                        onChange={() => handleToggle(channel, type)}
                      />
                      <div
                        className={`relative w-10 h-5 rounded-full transition-colors ${
                          localPreferences[channel]?.[type]
                            ? 'bg-yellow-500'
                            : 'bg-gray-300'
                        }`}
                      >
                        <div
                          className={`absolute left-0.5 top-0.5 bg-white w-4 h-4 rounded-full transition-transform ${
                            localPreferences[channel]?.[type]
                              ? 'transform translate-x-5'
                              : ''
                          }`}
                        ></div>
                      </div>
                    </label>
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default NotificationPreferences;
