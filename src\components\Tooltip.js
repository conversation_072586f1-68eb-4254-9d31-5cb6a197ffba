'use client';

import React from "react";
import "tippy.js/dist/tippy.css";
import "tippy.js/themes/light.css";
import "tippy.js/themes/light-border.css";
import "tippy.js/animations/shift-away.css";
import "tippy.js/animations/scale-subtle.css";
import "tippy.js/animations/perspective-extreme.css";
import "tippy.js/animations/perspective-subtle.css";
import "tippy.js/animations/perspective.css";
import "tippy.js/animations/scale-extreme.css";
import "tippy.js/animations/scale-subtle.css";
import "tippy.js/animations/scale.css";
import "tippy.js/animations/shift-away-extreme.css";
import "tippy.js/animations/shift-away-subtle.css";
import "tippy.js/animations/shift-away.css";
import "tippy.js/animations/shift-toward-extreme.css";
import "tippy.js/animations/shift-toward-subtle.css";
import "tippy.js/animations/shift-toward.css";
import Tippy from "@tippyjs/react";
// Import custom tooltip styles
import "./Tooltip.css";

/**
 * Customizable tooltip component using Tippy.js
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - The element that triggers the tooltip
 * @param {React.ReactNode|string} props.content - Content to display in the tooltip
 * @param {string} [props.title] - Title for the default button (if no children provided)
 * @param {string} [props.className="btn btn-dark"] - CSS class for the default button
 * @param {string} [props.position="top"] - Position of the tooltip (top, right, bottom, left)
 * @param {string} [props.placement="top"] - Placement of the tooltip (alternative to position)
 * @param {boolean} [props.arrow=true] - Whether to show an arrow on the tooltip
 * @param {string} [props.color="dark"] - Color theme of the tooltip (dark, light, yellow, etc.)
 * @param {string} [props.theme="dark"] - Theme of the tooltip (alternative to color)
 * @param {string} [props.size="md"] - Size of the tooltip (sm, md, lg)
 * @param {string} [props.animation="shift-away"] - Animation style
 * @param {string} [props.trigger="mouseenter focus"] - Events that trigger the tooltip
 * @param {boolean} [props.interactive=false] - Whether tooltip is interactive
 * @param {boolean} [props.allowHTML=false] - Whether to allow HTML in tooltip content
 * @param {number} [props.maxWidth=300] - Maximum width of the tooltip
 * @param {number} [props.duration=200] - Duration of the animation
 */
const Tooltip = ({
  children,
  content = "content",
  title,
  className = "btn btn-dark",
  position,
  placement = "top",
  arrow = true,
  color,
  theme = "dark",
  size = "md",
  animation = "shift-away",
  trigger = "mouseenter focus",
  interactive = false,
  allowHTML = false,
  maxWidth = 300,
  duration = 200,
}) => {
  // Use position prop if provided, otherwise use placement
  const finalPlacement = position || placement;

  // Use color prop if provided, otherwise use theme
  let finalTheme = color || theme;

  // Add size as a theme class to apply size-specific CSS
  if (size) {
    finalTheme = `${finalTheme} ${size}`;
  }

  // Calculate maxWidth based on size
  const sizeToWidth = {
    sm: 200,
    md: 300,
    lg: 400
  };

  const finalMaxWidth = size ? sizeToWidth[size] || maxWidth : maxWidth;

  return (
    <div className="custom-tippy">
      <Tippy
        content={content}
        placement={finalPlacement}
        arrow={arrow}
        theme={finalTheme}
        animation={animation}
        trigger={trigger}
        interactive={interactive}
        allowHTML={allowHTML}
        maxWidth={finalMaxWidth}
        duration={duration}
      >
        {children ? children : <button className={className}>{title}</button>}
      </Tippy>
    </div>
  );
};

export default Tooltip;
