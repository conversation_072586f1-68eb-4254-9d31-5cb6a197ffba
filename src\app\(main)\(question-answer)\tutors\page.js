'use client';
import useDataFetch from '@/hooks/useDataFetch';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import Link from 'next/link';
import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

// Helper function to convert planFeatureType to a user-friendly label
const getPlanFeatureTypeLabel = (type) => {
  const labels = {
    'hec_user_diary': 'Diary',
    'hec_play': 'Play',
    'english_qa_writing': 'Q&A',
    'english_essay': 'Essay',
    'english_novel': 'Novel'
  };

  return labels[type] || '';
};

const MyTutors = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const planFeatureType = searchParams.get('planFeatureType');

  // Prepare params for API call
  const params = {};
  if (planFeatureType) {
    params.planFeatureType = planFeatureType;
  }

  const { data, isLoading } = useDataFetch({
    queryKey: ['student-tutors', planFeatureType],
    endPoint: '/student/tutors',
    params: params,
  });


  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0 my-10">
      <div className="bg-[#FFF9FB] shadow-lg border p-5 rounded-lg cursor-pointer">
        <h2 className="text-3xl text-[#723F11] font-semibold mb-6">
          {planFeatureType ? `My ${getPlanFeatureTypeLabel(planFeatureType)} Tutors` : 'My Tutors'}
        </h2>

        {isLoading ? (
          <div className="flex justify-center items-center py-10">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
          </div>
        ) : data?.length === 0 ? (
          <div className="text-center py-10">
            <p className="text-lg text-gray-600">
              {planFeatureType
                ? `No tutors found for ${getPlanFeatureTypeLabel(planFeatureType)} module.`
                : 'No tutors found.'}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 relative gap-8 xl:px-6 max-w-5xl mx-auto">
            {data?.map((item, idx) => (
            <div
              key={idx}
              onClick={() => router.push(`/tutors/profile/${item?.id}`)}
              className="bg-white rounded-lg shadow-lg overflow-hidden relative group"
            >
              <Image
                src={
                  item?.profilePicture || '/assets/images/all-img/avatar.png'
                }
                alt={item?.name}
                width={400}
                height={400}
                className="w-full h-auto object-cover bg-[#EDFDFD]"
              />

              <div className="hidden group-hover:block absolute top-4 right-4 flex z-10 items-center space-x-2">
                <Link target="_blank" href={`https://www.linkedin.com/in/${item?.linkedin}`} className="bg-yellow-100 block rounded-full py-1">
                  <Icon icon="devicon:linkedin" width="24" height="24" className='mx-auto' />
                </Link>
                <Link target="_blank" href={`https://www.facebook.com/${item?.facebook}`} className="bg-yellow-100 block mt-2 rounded-full p-1">
                  <Icon icon="logos:facebook" width="24" height="24" />
                </Link>
                <Link href={`/tutors/${item?.id}`} className="bg-yellow-100 block mt-2 rounded-full p-1">
                  <Icon icon="lucide:mail" width="24" height="24" />
                </Link>
                <Link href={`/tutors/${item?.id}`} className="bg-yellow-100 block mt-2 rounded-full p-1">
                  <Icon icon="lsicon:send-filled" width="26" height="26" />
                </Link>
              </div>

              <div className="bg-white p-4 text-center space-y-2 group-hover:bg-yellow-100">
                <h3 className="text-xl">{item?.name}</h3>
                <p className="text-sm text-gray-600">
                  {item?.bio || 'Not available'}
                </p>
              </div>
            </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default MyTutors;
