'use client';
import React, { useState } from 'react';
import Image from 'next/image';
import { Icon } from '@iconify/react';
import './ChatBox.css';

const ContactInfo = ({ contact }) => {
  const [selectedFile, setSelectedFile] = useState('Media');
  return (
    <div className="p-4 bg-white overflow-y-auto">
      <div className="flex flex-col items-center mb-6">
        <div className="w-24 h-24 rounded-full overflow-hidden mb-3">
          <Image
            src={contact?.profileImage || '/assets/images/all-img/avatar.png'}
            alt={contact?.name || 'Contact'}
            width={96}
            height={96}
            className="object-cover"
          />
        </div>
        <h2 className="text-xl font-semibold">
          {contact?.name || 'Contact Name'}
        </h2>
        <p className="text-gray-600">
          {contact?.email || '<EMAIL>'}
        </p>
      </div>

      <div className="mb-6">
        <h3 className="font-medium text-gray-800 mb-2">Media, Doc & Link</h3>
        <div className="flex gap-2 mb-2 bg-gray-100 p-2 rounded-full">
          {['Media', 'Doc', 'Link']?.map((file, index) => (
            <button
              key={index}
              onClick={() => setSelectedFile(file)}
              className={`px-3 py-1 rounded-full text-sm ${
                selectedFile === file ? 'bg-yellow-300' : 'bg-gray-100'
              }`}
            >
              {file}
            </button>
          ))}
        </div>

        <div className="grid grid-cols-1 gap-2">
          {selectedFile === 'Media' ? (
            (contact?.files || [1, 2, 3, 4, 5]).map((file, index) => (
              <div
                key={index}
                className="flex items-center p-2 bg-gray-50 rounded-md"
              >
                <div className="w-10 h-10 bg-gray-200 rounded-md flex items-center justify-center mr-3">
                  <Icon icon="material-symbols:image" width="20" height="20" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium">
                    {file.name || 'file-name.jpeg'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {file.date || '2/13/2023'}
                  </p>
                </div>
                <div className="text-xs text-gray-500">
                  {file.size || '3.5 MB'}
                </div>
                <button className="ml-2 text-gray-500">
                  <Icon
                    icon="material-symbols:download"
                    width="20"
                    height="20"
                  />
                </button>
              </div>
            ))
          ) : selectedFile === 'Doc' ? (
            <div>
              <p>No Docs available</p>
            </div>
          ) : (
            <div>
              <p>No links available</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ContactInfo;
