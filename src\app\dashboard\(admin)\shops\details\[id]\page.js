'use client';
import useDataFetch from '@/hooks/useDataFetch';
import { useParams, useRouter } from 'next/navigation';
import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import RegularGoBack from '@/components/shared/RegularGoBack';
import api from '@/lib/api';
import { IMAGE_BASE_URL } from '@/lib/config';

const ShopDetails = () => {
  const { id } = useParams();
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);

  const { data, isLoading } = useDataFetch({
    queryKey: ['shop-detailss', id],
    endPoint: `/admin/shop/items/${id}`,
  });

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      const response = await api.delete(`/admin/shop/items/${id}`);
      console.log(response);
      router.push('/dashboard/shops');
    } catch (error) {
      console.log(error);
    } finally {
      setIsDeleting(false);
    }
  };

  if (isLoading) {
    return <div className="flex items-center justify-center h-96">Loading...</div>;
  }

  console.log(data);

  return (
    <div className="shadow-md rounded-lg">
      <div className=" p-5 bg-gray-50">
        <h2 className="text-base font-medium">SHOP</h2>
      </div>

      <div className="bg-white rounded-lg p-6">
        <RegularGoBack className={'pb-5 max-w-32'} />

        <div className="flex items-center flex-wrap gap-8">
          {/* Left side - Image */}
          <div className="bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
            <Image
              src={data?.filePath ? `${data.filePath}` : '/assets/images/all-img/noImage.png'}
              alt={data?.title || 'Item image'}
              width={468}
              height={300}
              className="object-cover max-w-80"
            />
          </div>

          {/* Right side - Details */}
          <div className="flex-1">
            <h1 className="text-xl font-medium mb-1">{data?.title}</h1>
            <p className="text-gray-500 text-sm mb-3">{data?.categoryName}</p>

            <div className="flex items-center gap-2 mb-4 font-[500]">
              <Icon icon="mynaui:click" className="w-5 h-5 mb-1 text-gray-600" />
              <span className="text-sm">{data?.purchaseCount} Used</span>
            </div>

            <div className="mb-6">
              <span className="text-2xl font-semibold">{data?.finalPrice} ₩</span>
                <span className="ml-2 text-gray-400 line-through">
                  {data?.price} ₩
                </span>
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => router.push(`/dashboard/shops/edit/${id}`)}
                className="px-6 py-1 bg-[#FFE924] rounded-md flex items-center gap-2 hover:bg-[#FFE924]/90 transition-colors"
              >
                <Icon icon="solar:pen-2-linear" className="w-4 h-4" />
                Edit Item
              </button>
              <button onClick={handleDelete} className="px-6 py-1 bg-red-50 text-red-600 rounded-md flex items-center gap-2 hover:bg-red-100 transition-colors">
                <Icon icon="solar:trash-bin-trash-linear" className="w-4 h-4" />
                { isDeleting ? "Deleting..." : "Delete"}
              </button>
            </div>
          </div>
        </div>

        <div className="mt-8">
          <h3 className="font-medium mb-2">DESCRIPTION:</h3>
          <p className="text-gray-600">
            {data?.description || 'Writing is a powerful tool for enhancing creativity, as it encourages self-expression, critical thinking, and idea generation. Whether through storytelling, journaling, or brainstorming, writing helps individuals explore new perspectives and refine their thoughts'}
          </p>
        </div>

        <div className="mt-8 border rounded-lg">
          <h3 className="font-medium rounded-t-lg mb-4 py-3 p-5 border-b bg-gray-50">PHOTOS</h3>
          <div className="min-h-60 max-h-96 relative">
            <div className="rounded-lg">{console.log(data?.filePath)}
              <Image
                src={data?.filePath ? `${data.filePath}` : '/assets/images/all-img/noImage.png'}
                alt={data?.title || 'Item photo'}
                fill
                className="object-contain"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShopDetails;
