'use client';
import React, { useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { Icon } from '@iconify/react';
import RegularGoBack from '@/components/shared/RegularGoBack';

const PlanDetails = () => {
  const { id } = useParams();
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);

  const { data: plan, isLoading } = useDataFetch({
    queryKey: ['plan-details', id],
    endPoint: `/plans/${id}`,
  });

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this plan?')) {
      try {
        setIsDeleting(true);
        await api.delete(`/plans/${id}`);
        router.push('/dashboard/plans');
      } catch (error) {
        console.log(error);
        alert('Failed to delete plan. Please try again.');
      } finally {
        setIsDeleting(false);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  return (
    <div className="p-5">
      <RegularGoBack className={'pb-5 max-w-32'} title={'Subscription Plans'} />

      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-800">{plan?.name}</h1>
            <div className="flex gap-3">
              <button
                onClick={() => router.push(`/dashboard/plans/edit/${id}`)}
                className="px-6 py-1 bg-[#FFE924] rounded-md flex items-center gap-2 hover:bg-[#FFE924]/90 transition-colors"
              >
                <Icon icon="solar:pen-2-linear" className="w-4 h-4" />
                Edit Plan
              </button>
              <button
                onClick={handleDelete}
                className="px-6 py-1 bg-red-50 text-red-600 rounded-md flex items-center gap-2 hover:bg-red-100 transition-colors"
              >
                <Icon icon="solar:trash-bin-trash-linear" className="w-4 h-4" />
                {isDeleting ? "Deleting..." : "Delete"}
              </button>
            </div>
          </div>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h2 className="text-lg font-semibold mb-4 text-gray-700">Plan Information</h2>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-500">Plan Type</p>
                  <p className="font-medium">{plan?.type?.charAt(0).toUpperCase() + plan?.type?.slice(1)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Subscription Type</p>
                  <p className="font-medium">{plan?.subscriptionType?.charAt(0).toUpperCase() + plan?.subscriptionType?.slice(1)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Auto Renew</p>
                  <p className="font-medium">{plan?.autoRenew ? 'Yes' : 'No'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Price</p>
                  <p className="font-medium">₩{parseFloat(plan?.price).toFixed(2)}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Duration</p>
                  <p className="font-medium">{plan?.durationDays} days</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Status</p>
                  <p className={`font-medium ${plan?.isActive ? 'text-green-600' : 'text-red-600'}`}>
                    {plan?.isActive ? 'Active' : 'Inactive'}
                  </p>
                </div>
              </div>
            </div>

            <div>
              <h2 className="text-lg font-semibold mb-4 text-gray-700">Description</h2>
              <p className="text-gray-700 whitespace-pre-line">{plan?.description}</p>
            </div>
          </div>

          <div className="mt-8">
            <h2 className="text-lg font-semibold mb-4 text-gray-700">Plan Features</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {plan?.planFeatures?.map((feature, index) => (
                <div key={index} className="p-4 border rounded-lg bg-gray-50">
                  <div className="flex justify-between">
                    <h3 className="font-medium text-gray-800">{feature.name}</h3>
                    {feature.isActive !== undefined && (
                      <span className={`text-xs px-2 py-1 rounded ${feature.isActive ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
                        {feature.isActive ? 'Active' : 'Inactive'}
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{feature.description}</p>
                  {feature.type && <p className="text-xs text-gray-500 mt-2">Type: {feature.type}</p>}
                  <p className="text-xs text-gray-500 mt-1">ID: {feature.id}</p>
                </div>
              ))}
              {(!plan?.planFeatures || plan.planFeatures.length === 0) && (
                <div className="col-span-2 p-4 border rounded-lg bg-gray-50 text-center text-gray-500">
                  No plan features available
                </div>
              )}
            </div>
          </div>

          {plan?.legacyFeatures && plan.legacyFeatures.length > 0 && (
            <div className="mt-8">
              <h2 className="text-lg font-semibold mb-4 text-gray-700">Legacy Features</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {plan.legacyFeatures.map((feature, index) => (
                  <div key={index} className="p-4 border rounded-lg bg-gray-50">
                    <div className="flex justify-between">
                      <h3 className="font-medium text-gray-800">{feature.name}</h3>
                      <span className={`text-xs px-2 py-1 rounded ${feature.isActive ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
                        {feature.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">{feature.description}</p>
                    <p className="text-xs text-gray-500 mt-2">Type: {feature.type}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="mt-8">
            <h2 className="text-lg font-semibold mb-4 text-gray-700">Metadata</h2>
            <div className="bg-gray-50 p-4 rounded-lg">
              <pre className="text-sm text-gray-700 overflow-x-auto">
                {JSON.stringify(plan, null, 2)}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlanDetails;
