
import Image from 'next/image';
import { ButtonIcon } from './Button';

const DiaryCard2 = ({
  pandaCard = false,
  catCard = false,
  dogCard = false,
  cardCotent,
}) => {
  return (
    <div className="bg-[#FCF8EF] rounded-xl p-4 shadow-md">
      {pandaCard && (
        <div className="image-container mb-4 relative w-full h-full">
          <Image
            src="/assets/images/all-img/introduction/Frame 3.png"
            alt="Diary Image"
            width={350}
            height={350}
            className="mt-0 mx-auto my-auto "
          />
          <div className="text-[8px] absolute right-[30px] sm:right-[30px] xl:right-[50px] top-1/2 -translate-y-1/2 z-0 max-w-[45%] xl:max-w-[40%] space-y-2">
            <p className="font-bold text-gray-600 text-[8px] bg-opacity-70 rounded-md">
              March 24, 2025
            </p>
            <p className="text-gray-600 text-[8px] bg-opacity-70 rounded-md">
              I went to school by bus. Today was a fun day. I met my friends and
              went to the park. We played games and had a picnic.
            </p>
          </div>
        </div>
      )}
      {catCard && (
        <div className="image-container mb-4 relative w-full h-full">
          <Image
            src="/assets/images/all-img/introduction/Frame 1.png"
            alt="Diary Image"
            width={350}
            height={350}
            className="mt-0 mx-auto my-auto "
          />
          <div className="text-[8px] absolute left-1/2 -translate-x-1/2 bottom-[15%] lg:bottom-[12%] mb-5 z-0 max-w-[60%] text-gray-700 space-y-2">
            <p className="font-bold">March 24, 2025</p>
            <p className="">
              I went to school by bus. Today was a fun day. I met my friends and
              went to the park. We played games and had a picnic.
            </p>
          </div>
        </div>
      )}
      {dogCard && (
        <div className="image-container mb-4 relative w-full h-full">
          <Image
            src="/assets/images/all-img/introduction/Frame 2.png"
            alt="Diary Image"
            width={350}
            height={350}
            className="mt-0 mx-auto my-auto"
          />
          <div className="text-[9px] absolute left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2 mb-5 z-0 max-w-[65%] text-gray-700 space-y-2">
            <p className="font-bold">March 24, 2025</p>
            <p className="">
              I went to school by bus. Today was a fun day. I met my friends and
              went to the park. We played games and had a picnic.
            </p>
          </div>
        </div>
      )}
      <div className="flex items-center justify-between mt-3 gap-2">
        <div className="flex items-center gap-1">
          <Image
            src="/assets/images/all-img/avatar.png"
            alt="User"
            width={80}
            height={80}
            className="w-10 h-10 rounded-full border"
          />
          <span className="text-base">Name Name</span>
        </div>
        <button className="px-4 py-1 max-w-32 justify-center bg-yellow-100 hover:bg-yellow-200 rounded-full flex items-center border border-yellow-800 text-yellow-800 gap-1">
          Click Here
          <ButtonIcon
            icon={'tdesign:gesture-click-filled'}
            innerBtnCls="h-5"
            btnIconCls={'h-3 w-3'}
          />
        </button>
      </div>
    </div>
  );
};

export default DiaryCard2;
