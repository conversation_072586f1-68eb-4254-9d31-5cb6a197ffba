'use client';
import useDataFetch from '@/hooks/useDataFetch';
import { Icon } from '@iconify/react';
import React, { useMemo, useState } from 'react';
import ShopCategory from './_components/ShopCategory';
import { useRouter } from 'next/navigation';
import RegularGoBack from '@/components/shared/RegularGoBack';
import { useQueryClient } from '@tanstack/react-query';

const Shops = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchTimeout, setSearchTimeout] = useState(null);
  const router = useRouter();
  const queryClient = useQueryClient();

  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['shops', searchQuery],
    endPoint: `/admin/shop/items/grouped-by-category${searchQuery.length > 2 ? `?title=${searchQuery}` : ''}`,
  });
  const categorizedItems = data?.categories || [];


  const handleSearch = (e) => {
    const value = e.target.value;
    
    // Clear any existing timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }
    
    // Set a new timeout for the search query
    const timeout = setTimeout(() => {
      if(value.length > 2 || value.length === 0){
        setSearchQuery(value);
      }
      
      // If search is cleared, invalidate the query to refresh data
      if (value.length === 0) {
        queryClient.invalidateQueries(['shops']);
      }
    }, 500); // 500ms debounce delay
    
    setSearchTimeout(timeout);
  };

  return (
    <div className="shadow-md rounded-lg">
      <div className="flex justify-between items-center p-5 bg-gray-50">
        <h2 className="sm:text-xl font-[500]">Shop Management</h2>
      </div>

      <div className="p-5">
        <RegularGoBack className={'pb-5 max-w-32'} />

        <div className="flex gap-4 mb-6 bg-[#FEFCE8] p-5 rounded-lg shadow-[-4px_5px_8px_0px_#00000026]">
          <div className="flex-1 relative">
            <input
              type="text"
              placeholder="Search by name"
              onChange={handleSearch}
              className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-1 focus:ring-yellow-400"
            />
            <Icon
              icon="stash:search"
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5"
            />
          </div>

          <button 
            onClick={() => router.push('/dashboard/shops/add')} 
            className="bg-yellow-400 hover:bg-yellow-500 text-black px-4 py-2 rounded-lg flex items-center"
          >
            <Icon icon="solar:add-circle-linear" className="mr-2" />
            Add Item
          </button>
        </div>

        {/* Loading state */}
        {isLoading && (
          <div className="text-center py-8 text-gray-500">
            Loading...
          </div>
        )}

        {/* No results message */}
        {!isLoading && categorizedItems?.length === 0 && (
          <div className="text-center py-5 text-gray-500">
            {searchQuery.length > 2 ? `No items found matching "${searchQuery}"` : 'No items available'}
          </div>
        )}

        {/* Categories */}
        {categorizedItems?.map((category, idx) => (
          <ShopCategory
            key={idx}
            categoryName={category?.categoryName}
            items={category?.items}
            categoryId={category?.categoryId}
            refetch={refetch}
          />
        ))}
      </div>
    </div>
  );
};

export default Shops;
