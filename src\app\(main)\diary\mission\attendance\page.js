'use client';
import React, { useState, useRef, useCallback } from 'react';
import { Icon } from '@iconify/react';
import useDataFetch from '@/hooks/useDataFetch';
import { format, parse, isValid } from 'date-fns';
import BasicTablePage from '@/components/form/BasicTablePage';

const AttendancePage = () => {
  // State for date filters
  const [startDateStr, setStartDateStr] = useState('');
  const [endDateStr, setEndDateStr] = useState('');
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Refs for date pickers
  const startDateRef = useRef(null);
  const endDateRef = useRef(null);

  // Prepare query parameters for API call
  const prepareQueryParams = useCallback(() => {
    const params = {};

    // Add date filters if they are valid
    if (startDate) {
      params.startDate = format(startDate, 'yyyy-MM-dd');
    }

    if (endDate) {
      params.endDate = format(endDate, 'yyyy-MM-dd');
    }

    return params;
  }, [startDate, endDate]);

  // Fetch attendance data from API
  const {
    data: attendanceResponse,
    isLoading,
    error,
    refetch
  } = useDataFetch({
    queryKey: ['diary-attendance', startDate, endDate],
    endPoint: '/diary-attendance/student',
    params: prepareQueryParams()
  });

  // Extract data from response
  const allAttendanceData = attendanceResponse || [];
  const totalRecords = allAttendanceData.length || 0;
  const totalPages = Math.ceil(totalRecords / rowsPerPage) || 1;

  // Client-side pagination
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const attendanceData = allAttendanceData.slice(startIndex, endIndex);

  // Debug logs
  console.log('API Response:', attendanceResponse);
  console.log('Extracted Data:', allAttendanceData);
  console.log('Filtered Data for Display:', attendanceData);

  // Handle date changes
  const handleStartDateChange = (dateString) => {
    try {
      // Parse the date string to a Date object
      const parsedDate = parse(dateString, 'yyyy-MM-dd', new Date());

      if (isValid(parsedDate)) {
        setStartDate(parsedDate);
        setStartDateStr(format(parsedDate, 'dd/MM/yyyy'));
        setCurrentPage(1); // Reset to first page when date changes
      }
    } catch (error) {
      console.error('Invalid date format:', error);
    }
  };

  const handleEndDateChange = (dateString) => {
    try {
      // Parse the date string to a Date object
      const parsedDate = parse(dateString, 'yyyy-MM-dd', new Date());

      if (isValid(parsedDate)) {
        setEndDate(parsedDate);
        setEndDateStr(format(parsedDate, 'dd/MM/yyyy'));
        setCurrentPage(1); // Reset to first page when date changes
      }
    } catch (error) {
      console.error('Invalid date format:', error);
    }
  };

  // Apply filters and refresh data
  const applyFilters = () => {
    setCurrentPage(1);
    refetch();
  };

  // Format date for display
  const formatDisplayDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return format(date, 'MMMM d, yyyy');
    } catch (error) {
      return dateString;
    }
  };

  // Define columns for the table
  const columns = [
    {
      label: 'DATE',
      field: 'date'
    },
    {
      label: 'DIARY TITLE',
      field: 'title'
    },
    {
      label: 'STATUS',
      field: 'status'
    },
    {
      label: 'WORD COUNT',
      field: 'wordCount'
    },
    {
      label: 'CREATED AT',
      field: 'createdAt'
    }
  ];

  // Format data for the table
  const tableData = attendanceData.map((record, index) => {
    return {
      date: formatDisplayDate(record.entryDate),
      title: record.diaryTitle || 'No Title',
      status: (
        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${record.status === 'present' ? 'bg-[#E0F2E9] text-green-800' : 'bg-[#FADADD] text-red-800'}`}>
          <span className="mr-1 text-lg">{record.status === 'present' ? '•' : '•'}</span>
          {record.status === 'present' ? 'Present' : 'Absent'}
        </span>
      ),
      wordCount: record.wordCount || 0,
      createdAt: record.createdAt ? new Date(record.createdAt).toLocaleString() : 'N/A'
    };
  });

  // Custom header with date filters
  const tableHeaderExtra = (
    <div className="flex flex-wrap items-end gap-4 mb-4">
      <div className="flex-1 min-w-[200px]">
        <label className="block text-sm font-medium text-gray-700 mb-1">From Date</label>
        <div className="relative">
          <input
            type="text"
            className="w-full appearance-none bg-white border border-gray-300 rounded-md py-2 pl-3 pr-10 text-gray-700 leading-tight focus:outline-none"
            value={startDateStr}
            readOnly
            onClick={() => startDateRef.current.showPicker()}
            placeholder="Select start date"
          />
          <input
            type="date"
            className="absolute opacity-0 w-0 h-0"
            ref={startDateRef}
            onChange={(e) => handleStartDateChange(e.target.value)}
          />
          <div className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-500 cursor-pointer"
            onClick={() => startDateRef.current.showPicker()}>
            <Icon icon="mdi:calendar" className="h-4 w-4" />
          </div>
        </div>
      </div>

      <div className="flex-1 min-w-[200px]">
        <label className="block text-sm font-medium text-gray-700 mb-1">To Date</label>
        <div className="relative">
          <input
            type="text"
            className="w-full appearance-none bg-white border border-gray-300 rounded-md py-2 pl-3 pr-10 text-gray-700 leading-tight focus:outline-none"
            value={endDateStr}
            readOnly
            onClick={() => endDateRef.current.showPicker()}
            placeholder="Select end date"
          />
          <input
            type="date"
            className="absolute opacity-0 w-0 h-0"
            ref={endDateRef}
            onChange={(e) => handleEndDateChange(e.target.value)}
          />
          <div className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-500 cursor-pointer"
            onClick={() => endDateRef.current.showPicker()}>
            <Icon icon="mdi:calendar" className="h-4 w-4" />
          </div>
        </div>
      </div>

      <div>
        <button
          onClick={applyFilters}
          className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition-colors h-10"
        >
          Apply Filters
        </button>
      </div>
    </div>
  );

  return (
    <div className="container mx-auto p-4">
      <BasicTablePage
        title="Attendance Records"
        columns={columns}
        data={tableData}
        loading={isLoading}
        tableHeaderExtra={tableHeaderExtra}
        showCreateButton={false}
        currentPage={currentPage}
        changePage={setCurrentPage}
        totalItems={totalRecords}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
      />
    </div>
  );
};

export default AttendancePage;
