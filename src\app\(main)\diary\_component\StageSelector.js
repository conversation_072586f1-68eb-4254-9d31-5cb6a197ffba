'use client';

import { useState, useEffect, useCallback } from 'react';
import useDataFetch from '@/hooks/useDataFetch';
import { Icon } from '@iconify/react';

const StageSelector = ({ onStageChange, selectedTemplateId }) => {
  const [isOpen, setIsOpen] = useState(false);

  const { data, isLoading, error } = useDataFetch({
    queryKey: ['diary-settings'],
    endPoint: '/diary/settings',
  });

  // Define handleStageSelect first to avoid the "Cannot access before initialization" error
  const handleStageSelect = useCallback((stage) => {
    setIsOpen(false);
    if (onStageChange) onStageChange(stage);
  }, [onStageChange]);

  // Derive selected stage from props and data
  const selectedStage = selectedTemplateId
    ? data?.items?.find(item => item.id === selectedTemplateId)
    : data?.items?.[0];

  // When data loads or changes, ensure we have a selected stage
  useEffect(() => {
    if (data?.items?.length && !selectedTemplateId) {
      // If no stage is selected yet, select the first one
      handleStageSelect(data.items[0]);
    }
  }, [data, selectedTemplateId, handleStageSelect]);

  if (isLoading) return <div className="animate-pulse bg-gray-200 h-10 rounded-md w-32"></div>;
  if (error) return <div className="text-red-500 text-sm">Failed to load stages</div>;
  if (!data?.items?.length) return <div className="text-gray-500 text-sm">No stages available</div>;

  return (
    <div className="flex items-center gap-2">
      <div className="relative">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center gap-2 bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-2 rounded-md"
        >
          <span>Stage {selectedStage?.level || 1}</span>
          <Icon icon={isOpen ? "mdi:chevron-up" : "mdi:chevron-down"} />
        </button>

        {isOpen && (
          <div className="absolute top-full left-0 mt-1 bg-white shadow-lg rounded-md z-10 w-64">
            {data.items.map((stage) => (
              <div
                key={stage.id}
                className={`p-3 hover:bg-yellow-50 cursor-pointer ${
                  selectedStage?.id === stage.id ? 'bg-yellow-100' : ''
                }`}
                onClick={() => handleStageSelect(stage)}
              >
                <div className="font-medium">Stage {stage.level}: {stage.title}</div>
                <div className="text-sm text-gray-600">{stage.description}</div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="bg-yellow-100 text-yellow-800 px-3 py-2 rounded-md">
        {selectedStage?.wordLimit || 50} Words
      </div>
    </div>
  );
};

export default StageSelector;