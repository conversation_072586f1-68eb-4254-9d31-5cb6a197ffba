import DiaryCanvas from '@/app/dashboard/(tutor)/submission-management/hec-diary/review/_components/DiaryCanvas';
import Canvas from '@/components/skin/Canvas';
import SkinPreview from '@/components/skin/SkinPreview';
import React, { useEffect, useState, useRef } from 'react';

const DiaryPage = ({ entry }) => {
  // Format the date for better display
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  if (!entry) return null;

  return (
    <div className="h-full flex flex-col p-6">
      {/* Header with title and date in a row with justify-between */}
      <div className="flex justify-between items-center mb-4 pb-3 border-b border-gray-200">
        <h2 className="text-xl font-bold text-[#1E3A8A]">{entry.title}</h2>
        <p className="text-sm text-gray-500">{formatDate(entry.entryDate)}</p>
      </div>

      {/* Content area */}
      <div className="flex-grow overflow-auto items-center mt-20">
        <SkinPreview
          skin={entry.skin?.templateContent}
          contentData={{
            subject: entry.title,
            body: entry.content,
            date: entry.entryDate,
          }}
        />
      </div>

      {/* Footer with status */}
      <div className="mt-4 pt-2">
        <p className="text-xs text-gray-400">
          {entry.status === 'draft' ? 'Draft' : 'Published'}
          {entry.skin && ` • ${entry.skin.name} skin`}
        </p>
      </div>
    </div>
  );
};

export default DiaryPage;
