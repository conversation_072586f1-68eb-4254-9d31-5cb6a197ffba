import CommonSidebar from '@/components/dashboard/CommonSidebar';
import React from 'react';

const DiaryLayout = ({ children }) => {
  const data = [
    {
      name: "Today's Diary Goal",
      value: 'diaryGoal',
      path: '/dashboard/module-management/hec-diary/diary-goal',
    },

    {
        name: 'Diary List',
        value: 'diaryList',
        path: '/dashboard/module-management/hec-diary/diary-list',
    },
    {
        name: 'Award List',
        value: 'awardList',
        path: '/dashboard/module-management/hec-diary/award-list',
    },
    {
        name: 'Mission Diary',
        value: 'missionDiary',
        path: '/dashboard/module-management/hec-diary/mission-diary',
    },
    {
        name: 'Hall of Fame',
        value: 'hallOfFame',
        path: '/dashboard/module-management/hec-diary/hall-of-fame',
    },
  ];

  return (
    <div>
      <CommonSidebar children={children} data={data} title="HEC Diary" />
    </div>
  );
};

export default DiaryLayout;
