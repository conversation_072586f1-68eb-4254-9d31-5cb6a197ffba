'use client';
import useDataFetch from '@/hooks/useDataFetch';
import { useParams } from 'next/navigation';
import React from 'react';
import Image from 'next/image';
import RegularGoBack from '@/components/shared/RegularGoBack';

const AwardDetails = () => {
  const { id } = useParams();

  const { data: award, isLoading } = useDataFetch({
    queryKey: 'award-details',
    endPoint: `/awards/admin/${id}`,
  });

  return (
    <div>
        <RegularGoBack className={'pb-5 max-w-32'} title={'Awards'} />
      <div className="bg-gray-100 rounded-lg p-5 space-y-5">
        <div className="flex items-start justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold">{award?.name}</h1>
            <p className="text-gray-600 mt-2">{award?.description}</p>
          </div>
          {award?.imageUrl && (
            <div className="relative w-20 h-20 rounded-md overflow-hidden">
              <Image
                src={award?.imageUrl}
                alt={award?.name}
                fill
                className="object-cover"
              />
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 bg-white p-5 rounded">
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Module</h3>
              <p className="mt-1 capitalize">{award?.module}</p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-500">Frequency</h3>
              <p className="mt-1 capitalize">{award?.frequency}</p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-500">
                Reward Points
              </h3>
              <p className="mt-1">{award?.rewardPoints}</p>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Status</h3>
              <p className="mt-1">
                {/* <Badge variant={award.isActive ? 'success' : 'destructive'}> */}
                {award?.isActive ? 'Active' : 'Inactive'}
                {/* </Badge> */}
              </p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-500">Dates</h3>
              <p className="mt-1">
                {award?.startDate
                  ? new Date(award.startDate).toLocaleDateString()
                  : 'No start date'}{' '}
                -{' '}
                {award?.endDate
                  ? new Date(award.endDate).toLocaleDateString()
                  : 'No end date'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AwardDetails;
