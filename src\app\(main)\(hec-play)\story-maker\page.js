'use client';
import { ButtonIcon } from '@/components/Button';
import <PERSON><PERSON><PERSON><PERSON> from '@/components/EditorViewer';
import useDataFetch from '@/hooks/useDataFetch';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React from 'react';

const StoryMaker = () => {
  const router = useRouter();

  const { data, isLoading, fetch } = useDataFetch({
    queryKey: ['story-maker'],
    endPoint: '/play/story-maker/play/list',
  });

  const items = data?.games || [];

  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0 my-8">
      <div className="p-5 bg-[#FFF9FB] rounded-lg shadow-lg flex items-center justify-between">
        <h1 className="text-2xl text-yellow-800 font-bold">Story Maker</h1>
        <h1 className="text-3xl text-yellow-600 font-bold font-serif">
          HEC PLAY
        </h1>
      </div>

      <div className="grid grid-cols-1 gap-4 mt-6 px-6">
        {items.map((item) => (
          <div
            key={item.id}
            className="bg-[#FFF9FB] rounded-lg shadow-lg p-4 cursor-pointer transition-all duration-300 flex items-center gap-5 border border-gray-100 relative"
          >
            <span className="absolute z-10 top-0 right-0 bg-green-500 text-white text-xs px-3 py-1 rounded-bl-lg rounded-tr-lg">
              {item?.is_played ? 'Played' : 'New'}
            </span>
            <div className="relative">
              <Image
                src={item?.picture || '/assets/images/all-img/noImage.png'}
                alt={item.title}
                height={300}
                width={300}
                className="rounded-lg object-cover max-h-60"
              />
            </div>

            <div className="text-gray-600">
              <h4 className="font-medium text-yellow-700 text-2xl ">
                {item.title}
              </h4>
              <EditorViewer
                data={item?.description || item?.instruction || 'This is a dummy description'}
              />

              <button
                onClick={() => router.push(`/story-maker/${item.id}`)}
                className="flex items-center gap-2 border border-yellow-800 text-2xl text-yellow-900 bg-gradient-to-b from-[#DCA600] to-[#FFDE5B] px-10 py-1 rounded-full mt-3"
              >
                See{' '}
                <ButtonIcon
                  icon={'tabler:arrow-right'}
                  innerBtnCls={'h-8 w-8 '}
                  btnIconCls={'h-4 w-4'}
                />
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default StoryMaker;
