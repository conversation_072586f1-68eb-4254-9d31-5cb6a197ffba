'use client';
import { ButtonIcon } from '@/components/Button';
import GoBack from '@/components/shared/GoBack';
import Image from 'next/image';
import React, { useState } from 'react';

const HecAsk = () => {
  const [weeklyMission, setWeeklyMission] = useState(true);

  return (
    <div className="">
      <Image
        height={600}
        width={1400}
        src={'/assets/images/all-img/stars-bg.png'}
        className="absolute left-0 top-0 z-0 w-full"
        alt={'ask'}
      />

      <div className="max-w-7xl mx-auto px-5 xl:px-0 relative z-10">
        <GoBack title={'HEC Play'} linkClass="my-5 mb-8 w-full max-w-40" />

        <div className="bg-[#FFFDF5]  rounded-lg">
          <div className="flex items-center border-2 border-yellow-500 rounded-full bg-[#FEFCE8]">
            <div className="w-full flex-1 relative">
              <button
                onClick={() => setWeeklyMission(true)}
                className={`w-full flex-1 py-2.5 ${
                  weeklyMission && 'bg-yellow-500  rounded-l-full'
                }`}
              >
                Weekly Mission
              </button>
              {weeklyMission && (
                <div className="h-6 w-6 bg-yellow-500 rotate-45 absolute z-0 -bottom-3 left-1/2 -translate-x-1/2"></div>
              )}
            </div>

            <div className="w-full flex-1 relative">
              <button
                onClick={() => setWeeklyMission(false)}
                className={`w-full py-2.5 ${
                  !weeklyMission && 'bg-yellow-500 rounded-r-full'
                }`}
              >
                Monthly Mission
              </button>

              {!weeklyMission && (
                <div className="h-6 w-6 bg-yellow-500 rotate-45 absolute z-0 -bottom-3 left-1/2 -translate-x-1/2"></div>
              )}
            </div>
          </div>
        </div>

        <div className="rounded-xl shadow-lg p-5 bg-[#FFF9FB] mt-5">
          {weeklyMission ? (
            <div>
              <div className="flex items-start justify-between text-gray-600">
                <div>
                  <h1 className="text-2xl text-yellow-800 font-semibold">
                    Hello English Coaching Q & A
                  </h1>
                  <p>Instruction:</p>
                  <p>1. Write a short writtings on a dog.</p>
                </div>

                <h1 className="bg-gradient-to-b from-[#ECB306] to-[#AE6E33] bg-clip-text text-3xl font-extrabold text-transparent font-serif">
                  HEC Q & A
                </h1>
              </div>

              <button className='flex items-center gap-2 border border-yellow-800 text-3xl text-yellow-900 bg-gradient-to-b from-[#DCA600] to-[#FFDE5B] px-10 py-1 rounded-full mt-5'>GO <ButtonIcon icon={'tabler:arrow-right'} innerBtnCls={'h-8 w-8 '} btnIconCls={'h-4 w-4'} /></button>
            </div>
          ) : (
            <div className="">
              <h1 className="text-3xl font-bold">Monthly Mission</h1>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default HecAsk;
