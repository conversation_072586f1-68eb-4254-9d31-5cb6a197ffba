'use client';
import React, { useState, useEffect, useRef } from 'react';
import { FiSearch, FiChevronDown } from 'react-icons/fi';
import { Icon } from '@iconify/react';
import Button from '@/components/Button';
import Modal from '@/components/Modal';

const Topbar = ({
  title = "Today's Diary",
  onSearch,
  onSortChange,
  onBackgroundChange,
}) => {
  const [isSubjectDropdownOpen, setIsSubjectDropdownOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('Subject Search');
  const [isColorModalOpen, setIsColorModalOpen] = useState(false);
  const [selectedColor, setSelectedColor] = useState(null);
  const dropdownRef = useRef(null);

  // Sample subjects - replace with your actual subjects
  const subjects = [
    'All Subjects',
    'English',
    'Mathematics',
    'Science',
    'History',
    'Geography',
  ];

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsSubjectDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    if (onSearch) {
      onSearch(searchQuery, selectedSubject);
    }
  };

  const handleSubjectSelect = (subject) => {
    setSelectedSubject(subject);
    setIsSubjectDropdownOpen(false);
    if (onSearch) {
      onSearch(searchQuery, subject);
    }
  };

  const handleSortClick = () => {
    if (onSortChange) {
      onSortChange();
    }
  };

  const handleBackgroundChange = () => {
    setIsColorModalOpen(true);
  };

  const handleCloseColorModal = () => {
    setIsColorModalOpen(false);
  };

  const handleApplyColor = (color) => {
    setSelectedColor(color);
    if (onBackgroundChange) {
      onBackgroundChange(color);
    }
    setIsColorModalOpen(false);
  };

  // Color options for the modal
  const colorOptions = [
    // First row
    { id: 'white', color: '#FFFFFF', borderColor: '#00A67E' },
    { id: 'lightBlue', color: '#E6F7FF' },
    { id: 'lightPink', color: '#FFE6E6' },
    { id: 'yellow', color: '#FFEB3B' },
    { id: 'lightYellow', color: '#FFF9C4' },
    // Second row
    { id: 'red', color: '#D32F2F' },
    { id: 'darkBlue', color: '#1A2A3A' },
    { id: 'orange', color: '#B45309' },
  ];

  return (
    <div className="w-full bg-[#8B4513] text-white py-3 px-6 flex items-center justify-between shadow-md">
      {/* Left side - Title */}
      <div className="text-xl font-medium tracking-wide">{title}</div>

      {/* Right side - Search and buttons */}
      <div className="flex items-center space-x-2">
        {/* Subject dropdown and search input */}
        <div className="relative flex items-center" ref={dropdownRef}>
          <button
            className="bg-[#FFDE34] text-black px-3 py-2.5 rounded-l-md flex items-center justify-between min-w-[140px] topbar-dropdown"
            onClick={() => setIsSubjectDropdownOpen(!isSubjectDropdownOpen)}
          >
            <span className="text-sm font-medium truncate">
              {selectedSubject}
            </span>
            <FiChevronDown
              className={`ml-1 transition-transform duration-200 ${
                isSubjectDropdownOpen ? 'transform rotate-180' : ''
              }`}
              size={16}
            />
          </button>

          {isSubjectDropdownOpen && (
            <div className="absolute top-full left-0 mt-1 w-full bg-white rounded-md shadow-lg z-10 max-h-60 overflow-y-auto">
              {subjects.map((subject, index) => (
                <div
                  key={index}
                  className="px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
                  onClick={() => handleSubjectSelect(subject)}
                >
                  {subject}
                </div>
              ))}
            </div>
          )}

          <form onSubmit={handleSearchSubmit} className="flex">
            <input
              type="text"
              placeholder="Search by subject name"
              className="border-0 px-3 py-2 w-64 focus:outline-none text-black search-input"
              value={searchQuery}
              onChange={handleSearchChange}
            />
            <button
              type="submit"
              className="bg-white text-gray-600 px-2 py-2 rounded-r-md hover:text-gray-800 topbar-button"
            >
              <FiSearch size={18} />
            </button>
          </form>
        </div>

        {/* Sort By button */}
        <Button
          icon="mdi:sort"
          buttonText="Sort By"
          onClick={handleSortClick}
          className="px-3 py-1.5 w-auto"
        />

        {/* Change Background button */}
        <Button
          icon="mdi:palette-outline"
          buttonText="Change Background"
          onClick={handleBackgroundChange}
          className="px-3 py-1.5 w-auto"
        />
      </div>

      {/* Color Picker Modal */}
      <Modal
        isOpen={isColorModalOpen}
        onClose={handleCloseColorModal}
        position="center"
        title="Change Background Color"
        width="md"
      >
        <div className="py-4">
          {/* First row - 5 colors */}
          <div className="grid grid-cols-5 gap-6 mb-8">
            {colorOptions.slice(0, 5).map((option) => (
              <div
                key={option.id}
                className="flex justify-center"
              >
                <div className="w-16 h-16 bg-white rounded-md shadow-md flex items-center justify-center cursor-pointer">
                  <button
                    className={`w-10 h-10 rounded-full shadow-sm flex items-center justify-center ${selectedColor === option.color ? 'ring-2 ring-offset-2 ring-yellow-500' : ''}`}
                    style={{
                      backgroundColor: option.color,
                      border: option.borderColor ? `2px solid ${option.borderColor}` : 'none'
                    }}
                    onClick={() => setSelectedColor(option.color)}
                    aria-label={`Select ${option.id} color`}
                  >
                    {selectedColor === option.color && (
                      <span className={`${option.id === 'white' || option.id === 'lightYellow' || option.id === 'lightBlue' || option.id === 'lightPink' ? 'text-black' : 'text-white'} text-lg`}>✓</span>
                    )}
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Second row - 3 colors */}
          <div className="grid grid-cols-5 gap-6 mb-10">
            <div className="col-span-1"></div>
            {colorOptions.slice(5).map((option) => (
              <div
                key={option.id}
                className="flex justify-center"
              >
                <div className="w-16 h-16 bg-white rounded-md shadow-md flex items-center justify-center cursor-pointer">
                  <button
                    className={`w-10 h-10 rounded-full shadow-sm flex items-center justify-center ${selectedColor === option.color ? 'ring-2 ring-offset-2 ring-yellow-500' : ''}`}
                    style={{
                      backgroundColor: option.color,
                      border: option.borderColor ? `2px solid ${option.borderColor}` : 'none'
                    }}
                    onClick={() => setSelectedColor(option.color)}
                    aria-label={`Select ${option.id} color`}
                  >
                    {selectedColor === option.color && (
                      <span className="text-white text-lg">✓</span>
                    )}
                  </button>
                </div>
              </div>
            ))}
            <div className="col-span-1"></div>
          </div>

          <div className="flex justify-center space-x-6">
            <button
              className="px-10 py-3 bg-[#FFFEF0] text-[#8B4513] rounded-full border-2 border-[#8B4513] font-medium hover:bg-[#FFF9C4] transition-colors shadow-md"
              onClick={handleCloseColorModal}
            >
              Cancel
            </button>

            <button
              className="px-10 py-3 bg-gradient-to-b from-yellow-400 to-yellow-600 text-black rounded-full border-2 border-yellow-300 font-medium shadow-md hover:from-yellow-500 hover:to-yellow-700 transition-colors"
              onClick={() => handleApplyColor(selectedColor)}
              disabled={!selectedColor}
            >
              Apply
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Topbar;
