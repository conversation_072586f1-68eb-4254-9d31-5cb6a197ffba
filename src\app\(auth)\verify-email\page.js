'use client';

import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter, useSearchParams } from 'next/navigation';
import api from '@/lib/api';
import Link from 'next/link';
import { Icon } from '@iconify/react';
import { loginSuccess } from '@/store/features/authSlice';
import Cookies from 'js-cookie';

export default function LoginPage() {
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const [isLoading, setIsLoading] = useState(true);
  const [isSending, setIsSending] = useState(false);
  const [showBtn, setShowBtn] = useState(true);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    if (!token) {
      setIsLoading(false);
      setError('Invalid or missing token.');
      return;
    }

    (async () => {
      try {
        const response = await api.post('/auth/verify-email', { token });
        if (response?.data?.access_token) {
          setMessage(response.message);
          Cookies.set('token', response?.data?.access_token, {
            expires: 7,
            path: '/',
          });
          dispatch(
            loginSuccess({
              user: response?.data?.user,
              token: response?.data?.access_token,
            })
          );
          console.log(response);
          await new Promise((resolve) => setTimeout(resolve, 100));
          if (response?.data?.user?.activePlan === null) {
            router.push('/pricing-plans');
          } else {
            router.push('/diary');
          }

          // if (response?.data?.user?.activePlan === null && response?.data?.user?.selectedRole === 'student') {
          //   router.push('/pricing-plans');
          //   return;
          // } else {
          //   if(response?.data?.user?.selectedRole === 'student' && response?.data?.user?.activePlan){
          //     router.push('/diary');
          //   } else if(response?.data?.user?.selectedRole === 'tutor' || response?.data?.user?.selectedRole === 'admin') {
          //     router.push('/dashboard');
          //   }
          // }
        }
      } catch (error) {
        console.log(error);
        setError(error?.response?.data?.message || 'Something went wrong');
      } finally {
        setIsLoading(false);
      }
    })();
  }, [token]);

  const handleVerifyAgain = async() => {
    const email = localStorage.getItem('email');
    try{
      setIsSending(true);
      if (email) {
        const response = await api.post('/auth/resend-verification-email', { email });
        setMessage(response?.message);
        setShowBtn(true);
        console.log(response);
      }
    } catch(error){
      console.log(error);
    } finally{
      setIsSending(false);
    }
  };
  return (
    <div>
      <div className="text-center space-y-2 mb-5">
        {isLoading ? (
          <div className="w-full h-full flex justify-center items-center">
            <Icon
              icon="eos-icons:three-dots-loading"
              width="50"
              height="50"
              className="text-yellow-500"
            />
          </div>
        ) : message?.length > 0 ? (
          <div className="space-y-4">
            <Icon
              icon="icon-park-solid:success"
              width="50"
              height="50"
              className="mx-auto text-green-500"
            />
            <p className="text-green-500">{message}</p>
            {showBtn && <Link
              className="py-2 px-4 bg-yellow-500 hover:bg-yellow-600 text-white rounded-md inline-block"
              href="/"
            >
              Go to Home
            </Link>}
          </div>
        ) : (
          <div className="space-y-3">
            <Icon
              icon="material-symbols-light:error-outline-rounded"
              width="50"
              height="50"
              className="mx-auto text-red-500"
            />
            <p className="text-red-500">{error}</p>
            <button
              disabled={isSending}
              className={`py-2 px-4 bg-yellow-500 text-white rounded-md inline-block mt-4 ${
                isSending ? 'cursor-not-allowed opacity-70' : ''
              }`}
              onClick={() => handleVerifyAgain()}
            >
              {isSending ? 'Verifying...' : 'Verify Again'}
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
