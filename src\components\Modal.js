'use client';
import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { ButtonIcon } from '@/components/Button';

const Modal = ({
  isOpen,
  onClose,
  children,
  position = 'center',
  title = '',
  scrollable = true,
  showCloseButton = true,
  closeOnOverlayClick = true,
  width = 'md',
  className = '',
  maxHeight = '80vh',
}) => {
  const [mounted, setMounted] = useState(false);
  const modalRef = useRef(null);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (isOpen) document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  const handleOverlayClick = (e) => {
    if (closeOnOverlayClick && e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!mounted) return null;

  const widthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '4xl': 'max-w-4xl',
    full: 'max-w-full',
  };

  const positionClasses = {
    left: 'fixed inset-y-0 left-0 w-full sm:max-w-sm md:max-w-md flex flex-col',
    right: 'fixed inset-y-0 right-0 w-full sm:max-w-sm md:max-w-md flex flex-col',
    top: 'fixed inset-x-0 top-0 w-full',
    bottom: 'fixed inset-x-0 bottom-0 w-full',
    fullscreen: 'fixed inset-0 w-full h-full flex flex-col',
    center: `fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full ${widthClasses[width] || widthClasses.md}`,
  };

  const animationVariants = {
    center: {
      hidden: { opacity: 0, scale: 0.9, x: '-50%', y: '-50%' },
      visible: { opacity: 1, scale: 1, x: '-50%', y: '-50%' },
      exit: { opacity: 0, scale: 0.9, x: '-50%', y: '-50%' },
    },
    fullscreen: {
      hidden: { opacity: 0 },
      visible: { opacity: 1 },
      exit: { opacity: 0 },
    },
    left: {
      hidden: { x: '-100%' },
      visible: { x: 0 },
      exit: { x: '-100%' },
    },
    right: {
      hidden: { x: '100%' },
      visible: { x: 0 },
      exit: { x: '100%' },
    },
    top: {
      hidden: { y: '-100%' },
      visible: { y: 0 },
      exit: { y: '-100%' },
    },
    bottom: {
      hidden: { y: '100%' },
      visible: { y: 0 },
      exit: { y: '100%' },
    },
  };

  const variant = animationVariants[position] || animationVariants.center;

  return createPortal(
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className={`fixed inset-0 z-50 flex ${position === 'center' ? 'items-center justify-center' : ''} bg-black bg-opacity-50`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          onClick={handleOverlayClick}
        >
          <motion.div
            ref={modalRef}
            className={`bg-white shadow-xl overflow-hidden ${positionClasses[position]} ${className}`}
            variants={variant}
            initial="hidden"
            animate="visible"
            exit="exit"
            transition={{ duration: 0.3, ease: [0.25, 0.1, 0.25, 1.0] }} /* Using cubic-bezier for smoother motion */
            onClick={(e) => e.stopPropagation()}
          >
            {(title || showCloseButton) && (
              <div className="flex items-center justify-between p-4 border-b w-full">
                {title && <h2 className="text-xl font-semibold">{title}</h2>}
                {showCloseButton && (
                  <div className="flex items-center">
                    <ButtonIcon
                      icon="mdi:close"
                      innerBtnCls="h-8 w-8"
                      btnIconCls="h-5 w-5"
                      onClick={onClose}
                      aria-label="Close modal"
                      withBackground={false}
                    />
                  </div>
                )}
              </div>
            )}
            <div
              className={`${scrollable ? 'overflow-y-auto' : 'overflow-hidden'} flex-grow`}
              style={position === 'center' ? { maxHeight } : {}}
            >
              <div className="p-4">{children}</div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>,
    document.body
  );
};

export default Modal;
