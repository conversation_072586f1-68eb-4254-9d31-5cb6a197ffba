import api from '@/lib/api';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import React, { useState } from 'react';
import { toast } from 'sonner';

const UserCard = ({id}) => {
    const [selectedFile, setSelectedFile] = useState(null);
    const [preview, setPreview] = useState(null);
    const [isUploading, setIsUploading] = useState(false);
  return (
    <div className="flex items-center gap-4">
      <div className="relative">
        <Image
          src={preview || '/assets/images/all-img/avatar.png'}
          alt="User"
          width={150}
          height={150}
          className="rounded-full w-32 h-32 border object-cover object-top"
        />
        {!selectedFile ? (
          <label
            htmlFor="profileImage"
            className="absolute bottom-0 right-0 bg-white p-2 rounded-full shadow-md cursor-pointer hover:bg-gray-100"
          >
            <Icon icon="solar:camera-bold" className="w-5 h-5 text-gray-600" />
            <input
              type="file"
              id="profileImage"
              accept="image/*"
              className="hidden"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  setSelectedFile(file);
                  const reader = new FileReader();
                  reader.onloadend = () => {
                    setPreview(reader.result);
                  };
                  reader.readAsDataURL(file);
                }
              }}
            />
          </label>
        ) : (
          <div className="absolute bottom-0 right-0 flex gap-1">
            <button
              onClick={async () => {
                try {
                  setIsUploading(true);
                  const formData = new FormData();
                  formData.append('profileImage', selectedFile);

                  await api.post('/users/profile-image', formData, {
                    headers: {
                      'Content-Type': 'multipart/form-data',
                    },
                  });

                } catch (error) {
                  // Show error message
                  console.error('Error uploading image:', error);
                } finally {
                  setIsUploading(false);
                }
              }}
              disabled={isUploading}
              className="bg-green-500 p-2 rounded-full shadow-md cursor-pointer hover:bg-green-600 disabled:bg-green-300"
            >
              <Icon
                icon={
                  isUploading ? 'eos-icons:loading' : 'material-symbols:check'
                }
                className="w-5 h-5 text-white"
              />
            </button>
            <button
              onClick={() => {
                setSelectedFile(null);
                setPreview(null);
              }}
              className="bg-red-500 p-2 rounded-full shadow-md cursor-pointer hover:bg-red-600"
            >
              <Icon
                icon="material-symbols:close"
                className="w-5 h-5 text-white"
              />
            </button>
          </div>
        )}
      </div>
      <div>
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold">User Full Name</h2>
          <Icon
            icon="material-symbols:verified"
            className="text-yellow-500 w-5 h-5"
          />
        </div>
        <p className="text-gray-600">User Id: {id}</p>
        <span className="flex items-center gap-1 text-sm text-white bg-gray-700 py-1 px-3 rounded max-w-16 justify-center">
          <Icon icon="material-symbols:star" className="w-5 h-5 text-white" />
          <span>4.45</span>
        </span>
      </div>
    </div>
  );
};

export default UserCard;
