'use client';
import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { Icon } from '@iconify/react';
import '../../../../components/ChatBox/ChatBox.css';
import useDataFetch from '@/hooks/useDataFetch';
import { getSocket } from '@/lib/socket';

const ChatList = ({chats, activeChat, onSelectChat }) => {
  const [searchQuery, setSearchQuery] = useState('');



  // useEffect(() => {
  //   const socket = getSocket();

  //   socket.on('connect', () => {
  //     console.log('Connected to server', socket.id);
  //   });
  //   socket.emit('subscribe_conversation', {conversationId: activeChat?.conversationId});
  //   socket.on('subscribed_conversation', (data) => {
  //     console.trace('subscribed_conversation', data);
  //   });
  //   socket.on('new_message', (data) => {
  //     console.log('new_message', data);
  //     alert('Message received')
  //   });
  //   socket.on('typing', (data) => {
  //     console.log('typing', data);
  //   });

  //   socket.on('disconnect', (data) => {
  //     console.log('Disconnected from server', data);
  //   });

  //   return () => {
  //     socket.disconnect();
  //   };

  // }, [])

  return (
    <div className="h-full flex flex-col border-r">
      {/* Search Header */}
      <div className="p-4 border-b">
        <div className="relative">
          <input
            type="text"
            placeholder="Search conversations..."
            className="w-full p-2 pl-9 pr-4 border border-gray-300 rounded-lg focus:outline-none focus:border-yellow-400"
            // value={searchQuery}
            onChange={(e) => e.target.value?.length > 2 ? setSearchQuery(e.target.value) : setSearchQuery('')}
          />
          <Icon
            icon="material-symbols:search"
            className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"
            width="18"
            height="18"
          />
        </div>
      </div>

      {/* Chat List */}
      <div className="flex-1 overflow-y-auto scrollbar-hide">
        {chats?.length > 0 ? (
          <div className="divide-y divide-gray-100">
            {chats.map((chat) => (
              <div
                key={chat.id}
                className={`p-3 hover:bg-gray-50 cursor-pointer transition-colors ${
                  activeChat && activeChat.id === chat.id ? 'bg-yellow-50' : ''
                }`}
                onClick={() => onSelectChat(chat)}
              >
                <div className="flex items-start gap-3">
                  <div className="relative flex-shrink-0">
                    <Image
                      src={chat.avatar || '/assets/images/all-img/avatar.png'}
                      alt={chat.name}
                      width={48}
                      height={48}
                      className="rounded-full object-cover"
                    />
                    {chat.online && (
                      <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></span>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-start">
                      <h4 className="font-medium text-gray-900 truncate">{chat.name}</h4>
                      <span className="text-xs text-gray-500 whitespace-nowrap ml-2">
                        {chat.lastMessageTime && new Date(chat.lastMessageTime).toLocaleTimeString('en-US', {
                          hour: 'numeric',
                          minute: 'numeric',
                          hour12: true
                        })}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 truncate mt-0.5">
                      {chat.lastMessage}
                    </p>
                    <div className="flex items-center justify-between mt-1">
                      <span className="text-xs text-gray-500">
                        {chat.typing ? (
                          <span className="text-green-600 flex items-center">
                            <Icon icon="mdi:message-text" className="mr-1" width="12" height="12" />
                            typing...
                          </span>
                        ) : (
                          chat.status
                        )}
                      </span>
                      {chat.unreadCount > 0 && (
                        <span className="bg-yellow-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                          {chat.unreadCount}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full p-4 text-center">
            <Icon icon="mdi:chat-remove" className="text-gray-400 mb-2" width={40} height={40} />
            <p className="text-gray-500">No conversations found</p>
            <p className="text-sm text-gray-400 mt-1">Try a different search term</p>
          </div>
        )}
      </div>

      {/* New Chat Button */}
      {/* <div className="p-3 border-t">
        <button className="w-full py-2 bg-yellow-400 hover:bg-yellow-500 text-gray-800 rounded-lg flex items-center justify-center gap-2 transition-colors">
          <Icon icon="material-symbols:add" width="20" height="20" />
          New Conversation
        </button>
      </div> */}
    </div>
  );
};

export default ChatList;
