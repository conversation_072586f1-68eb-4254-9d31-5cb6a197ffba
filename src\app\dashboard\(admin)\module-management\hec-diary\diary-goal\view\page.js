'use client';
import React, { useRef, useEffect } from 'react';
import Image from 'next/image';

/**
 * ViewModal Component
 * 
 * A reusable modal component for displaying item details.
 * Styled to match the wooden sign design pattern.
 * 
 * @param {Object} props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to call when closing the modal
 * @param {Object} props.data - The data object to display
 * @param {string} props.title - Optional custom title (defaults to "Details")
 * @returns {JSX.Element|null} The ViewModal component or null if not shown
 */
const ViewModal = ({ isOpen, onClose, data, title = "Details" }) => {
  const modalRef = useRef(null);

  useEffect(() => {
    if (!isOpen) return;

    // Handle escape key press
    const handleEscapeKey = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    // Handle click outside
    const handleClickOutside = (e) => {
      if (modalRef.current && !modalRef.current.contains(e.target)) {
        onClose();
      }
    };

    // Add event listeners
    document.addEventListener('keydown', handleEscapeKey);
    document.addEventListener('mousedown', handleClickOutside);
    
    // Prevent body scrolling when modal is open
    document.body.style.overflow = 'hidden';

    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.removeEventListener('mousedown', handleClickOutside);
      // Restore body scrolling when modal is closed
      document.body.style.overflow = 'auto';
    };
  }, [isOpen, onClose]);

  if (!isOpen || !data) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div 
        ref={modalRef}
        className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden relative"
      >
        {/* Close button */}
        <div className="absolute top-4 right-4 z-10">
          <button
            type="button"
            onClick={onClose}
            className="focus:outline-none"
            aria-label="Close"
          >
            <Image 
              src="/assets/images/all-img/cross-bg.png" 
              alt="Close" 
              width={40} 
              height={40} 
              className="w-10 h-10" 
            />
          </button>
        </div>
        
        {/* Wooden Sign Header */}
        <div className="bg-[#FFF9FB] pt-6 pb-8 px-6 border-b border-amber-200">
          <div className="flex justify-center mb-2">
            <div className="relative w-full max-w-md">
              <Image
                src="/assets/images/all-img/diarygoal-bg.png"
                alt={title}
                width={600}
                height={200}
                className="w-full h-auto"
                priority
              />
            </div>
          </div>
        </div>
        
        {/* Content Section */}
        <div className="px-8 py-6 max-h-96 overflow-y-auto">
          <h3 className="text-2xl font-semibold mb-6 text-gray-800">{title}</h3>
          
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Title
                </label>
                <p className="text-sm text-gray-900">
                  {data.title || 'N/A'}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Level
                </label>
                <p className="text-sm text-gray-900">
                  {data.level || 'N/A'}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <div>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                    data.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {data.isActive ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Word Limit
                </label>
                <p className="text-sm text-gray-900">
                  {data.wordLimit || 'N/A'}
                </p>
              </div>
            </div>
            
            {/* Additional details if available */}
            {data.description && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <p className="text-sm text-gray-900">
                  {data.description}
                </p>
              </div>
            )}
            
            {data.createdAt && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Created At
                  </label>
                  <p className="text-sm text-gray-900">
                    {new Date(data.createdAt).toLocaleDateString()}
                  </p>
                </div>
                {data.updatedAt && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Updated At
                    </label>
                    <p className="text-sm text-gray-900">
                      {new Date(data.updatedAt).toLocaleDateString()}
                    </p>
                  </div>
                )}
              </div>
            )}
            
            {data.id && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ID
                </label>
                <p className="text-sm text-gray-900 font-mono">
                  {data.id}
                </p>
              </div>
            )}
          </div>
        </div>
        
        {/* Divider */}
        <div className="border-t border-gray-200 mx-8"></div>
        
        {/* Footer */}
        <div className="flex justify-end p-6 bg-gray-50">
          <button
            type="button"
            className="bg-amber-600 text-white px-6 py-2 rounded-md hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-amber-500 transition-colors"
            onClick={onClose}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ViewModal;