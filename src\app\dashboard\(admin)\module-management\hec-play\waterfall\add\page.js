'use client';
import React, { useState } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import api from '@/lib/api';
import FormInput from '@/components/form/FormInput';
import NumberInput from '@/components/form/NumberInput';
import { Icon } from '@iconify/react';
import RegularGoBack from '@/components/shared/RegularGoBack';

const CreateQuestionSet = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Validation schema
  const validationSchema = Yup.object().shape({
    title: Yup.string().required('Title is required'),
    total_score: Yup.number()
      .required('Total score is required')
      .positive('Total score must be positive')
      .integer('Total score must be a number'),
  });

  // Initial values
  const initialValues = {
    title: '',
    total_score: '',
  };

  // Handle form submission
  const handleSubmit = async (values, { resetForm }) => {
    setIsSubmitting(true);
    try {
      // Make API call to create question set
      const response = await api.post('/play/waterfall/admin/sets', { set: values});

      // Invalidate queries to refresh data
      queryClient.invalidateQueries(['waterfall']);

      // Redirect to the question set page or add question page
      router.push(`/dashboard/module-management/hec-play/waterfall`);

      // Reset form
      resetForm();
    } catch (error) {
      console.error('Error creating question set:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="">
      {/* <RegularGoBack title="Back to Question Sets" linkClass="mb-3" /> */}

      <div className="p-4 mb-3">
        <h2 className="text-xl font-semibold text-gray-900">
          Create Question Set
        </h2>
      </div>

      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {() => (
          <Form className="border bg-gray-50 p-6 rounded-lg shadow-md">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormInput
                label="Title"
                name="title"
                placeholder="Enter question set title"
                required
              />

              <NumberInput
                label="Total Score"
                name="total_score"
                placeholder="Enter total score"
                required
              />
            </div>

            <div className="flex justify-end space-x-4 pt-4">
              <button
                type="button"
                onClick={() =>
                  router.push('/dashboard/module-management/hec-play/waterfall')
                }
                className="px-4 py-2 bg-gray-300 hover:bg-gray-200 text-gray-800 rounded-md transition-colors"
              >
                Cancel
              </button>

              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 bg-yellow-400 hover:bg-yellow-500 text-black rounded-md transition-colors flex items-center"
              >
                {isSubmitting ? (
                  <>
                    <Icon icon="eos-icons:loading" className="mr-2" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Icon icon="material-symbols:save" className="mr-2" />
                    Create Set
                  </>
                )}
              </button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default CreateQuestionSet;
