import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  editData: null,
  showEditModal: false,
  deleteData: null,
  showDeleteModal: false,
  showModal: false,
  modalData: null,
};


const crudSlice = createSlice({
  name: 'crud',
  initialState,
  reducers: {
    setEditData: (state, action) => {
      state.editData = action.payload;
    },
    setShowEditModal: (state, action) => {
      state.showEditModal = action.payload;
    },
    setDeleteData: (state, action) => {
      state.deleteData = action.payload;
    },
    setShowDeleteModal: (state, action) => {
      state.showDeleteModal = action.payload;
    },
    setShowModal: (state, action) => {
      state.showModal = action.payload;
    },
    setModalData: (state, action) => {
      state.modalData = action.payload;
    },
  },
});

export const {
  setEditData,
  setShowEditModal,
  setDeleteData,
  setShowDeleteModal,
  setShowModal,
  setModalData,
} = crudSlice.actions;

export default crudSlice.reducer;
