import React from 'react';
import { Icon } from '@iconify/react';
import { motion } from 'framer-motion';
import { useSelector, useDispatch } from 'react-redux';
import { selectBlanks, updateBlankAnswer, removeBlank } from '@/store/features/hecPlaySlice';

const BlankAnswers = () => {
  const blanks = useSelector(selectBlanks);
  const dispatch = useDispatch();

  return (
    <motion.div
      className=" rounded-lg shadow-subtle p-0"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <h2 className="text-lg font-medium text-gray-800 mb-2">Answer</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
        {blanks.map((blank) => {
          const blankNumber = blank.id.split('-')[1];

          return (
            <motion.div
              key={blank.id}
              className="flex items-center space-x-3"
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.2 }}
            >
              <div className="bg-yellow-500 min-w-16 py-2 px-5 rounded text-center font-medium">
                Blank {blankNumber}
              </div>

              <div className="flex-1 flex items-center space-x-2">
                <input
                  type="text"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                  placeholder="Write answer"
                  value={blank.answer}
                  onChange={(e) => dispatch(updateBlankAnswer({ id: blank.id, answer: e.target.value }))}
                />

                <button
                  className="text-gray-400 hover:text-red-500 transition-colors p-2 rounded-full hover:bg-gray-100"
                  onClick={() => dispatch(removeBlank(blank.id))}
                  aria-label="Remove blank"
                >
                  <Icon icon="lucide:trash-2" width={18} height={18} />
                </button>
              </div>
            </motion.div>
          );
        })}
      </div>
    </motion.div>
  );
};

export default BlankAnswers;