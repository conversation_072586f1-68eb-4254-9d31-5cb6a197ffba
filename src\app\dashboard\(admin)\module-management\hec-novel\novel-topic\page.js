'use client';
import React from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import FormInput from '@/components/form/FormInput';
import api from '@/lib/api';

const CreateQuestions = () => {
  const queryClient = useQueryClient();

  // Define validation schema
  const validationSchema = Yup.object().shape({
    title: Yup.string().required('Novel Topic Title is required'),
    description: Yup.string().required('Description is required'),
    instruction: Yup.string().required('Instruction is required'),
    missionDeadline: Yup.string().required('Mission Deadline is required'),
    wordLimitMin: Yup.string().required('Word Limit (Minimum) is required'),
    wordLimitMax: Yup.string().required('Word Limit (Maximum) is required')
  });

  // Define initial values
  const initialValues = {
    title: '',
    description: '',
    instruction: '',
    missionDeadline: '',
    wordLimitMin: '',
    wordLimitMax: '',
    isActive: true
  };

  // Create mutation for API call
  const createQuestionMutation = useMutation({
    mutationFn: (questionData) => {
      return api.post('/admin/qa/create', questionData);
    },
    onSuccess: () => {
      // Invalidate and refetch questions list if needed
      queryClient.invalidateQueries({ queryKey: ['questions'] });
      toast.success('Question created successfully');
    },
    onError: (error) => {
      toast.error(error?.response?.data?.message || 'Failed to create question');
    }
  });

  // Handle form submission
  const handleSubmit = (values, { setSubmitting, resetForm }) => {
    // Convert string values to appropriate types
    const payload = {
      title: values.title,
      description: values.description,
      instruction: values.instruction,
      missionDeadline: values.missionDeadline,
      wordLimitMin: values.wordLimitMin,
      wordLimitMax: values.wordLimitMax,
      isActive: values.isActive
    };

    createQuestionMutation.mutate(payload, {
      onSettled: () => {
        setSubmitting(false);
        resetForm();
      }
    });
  };

  return (
    <div className='min-h-screen bg-white p-5'>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        validateOnChange={false}
        validateOnBlur={false}
      >
        {({ isSubmitting, errors, touched, resetForm }) => (
          <Form className="mx-auto bg-gray-100 p-6 rounded-lg shadow-md">
            <div>
              <div className="flex flex-wrap gap-4 mb-4">
                <div className="flex-1 min-w-[200px]">
                  <label htmlFor="title" className="block font-bold">
                    Novel Topic Title <span className="text-red-500">*</span>
                  </label>
                  <FormInput
                    type="text"
                    name="title"
                    id="title"
                    placeholder="Enter novel topic title"
                    className={`w-full mt-1 p-2 border ${
                      errors.title ? 'border-red-500' : 'border-gray-300'
                    } rounded`}
                  />
                  
                </div>

                <div className="flex-1 min-w-[200px]">
                  <label htmlFor="description" className="block font-bold">
                    Description <span className="text-red-500">*</span>
                  </label>
                  <FormInput
                    type="text"
                    name="description"
                    id="description"
                    placeholder="Enter description"
                    className={`w-full mt-1 p-2 border ${
                      errors.description ? 'border-red-500' : 'border-gray-300'
                    } rounded`}
                  />
                  
                </div>

                <div className="flex-1 min-w-[200px]">
                  <label htmlFor="instruction" className="block font-bold">
                    Instruction <span className="text-red-500">*</span>
                  </label>
                  <FormInput
                    type="text"
                    name="instruction"
                    id="instruction"
                    placeholder="Enter instruction"
                    className={`w-full mt-1 p-2 border ${
                      errors.instruction ? 'border-red-500' : 'border-gray-300'
                    } rounded`}
                  />
                
                </div>
              </div>

              <div className="flex flex-wrap gap-4 mb-4 mt-8">
                <div className="flex-1 min-w-[200px]">
                  <label htmlFor="missionDeadline" className="block font-bold">
                    Mission Deadline <span className="text-red-500">*</span>
                  </label>
                  <FormInput
                    type="date"
                    name="missionDeadline"
                    id="missionDeadline"
                    placeholder="Select deadline"
                    className={`w-full mt-1 p-2 border ${
                      errors.missionDeadline ? 'border-red-500' : 'border-gray-300'
                    } rounded`}
                  />
                 
                </div>

                <div className="flex-1 min-w-[200px]">
                  <label htmlFor="wordLimitMin" className="block font-bold">
                    Word Limit (Minimum) <span className="text-red-500">*</span>
                  </label>
                  <FormInput
                    type="number"
                    name="wordLimitMin"
                    id="wordLimitMin"
                    placeholder="Enter minimum word limit"
                    className={`w-full mt-1 p-2 border ${
                      errors.wordLimitMin ? 'border-red-500' : 'border-gray-300'
                    } rounded`}
                  />
                 
                </div>

                <div className="flex-1 min-w-[200px]">
                  <label htmlFor="wordLimitMax" className="block font-bold">
                    Word Limit (Maximum) <span className="text-red-500">*</span>
                  </label>
                  <FormInput
                    type="number"
                    name="wordLimitMax"
                    id="wordLimitMax"
                    placeholder="Enter maximum word limit"
                    className={`w-full mt-1 p-2 border ${
                      errors.wordLimitMax ? 'border-red-500' : 'border-gray-300'
                    } rounded`}
                  />
                 
                </div>
              </div>

              <div className="flex justify-end gap-4 mt-7">
                <button
                  type="button"
                  className="bg-gray-300 hover:bg-gray-400 text-black font-medium py-2 px-4 rounded"
                  onClick={() => resetForm()}
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-[#FFDE34] hover:bg-yellow-400 text-black font-medium py-2 px-4 rounded"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Creating...' : 'Create'}
                </button>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default CreateQuestions;