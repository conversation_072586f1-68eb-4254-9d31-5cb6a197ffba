'use client';
import React from 'react';
import WaterfallDragDrop from '../WaterfallDragDrop';

const QuestionCard = ({
  question,
  onCorrect,
  onNext,
  title,
  handleSubmit,
  isLastQuestion,
  currentQuestionIndex,
  totalQuestions
}) => {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* Question progress indicator */}
      <div className="bg-yellow-50 px-6 py-3 border-b border-yellow-100">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-yellow-800">
            Question {currentQuestionIndex + 1} of {totalQuestions}
          </span>
          <span className="text-sm text-yellow-700">
            {Math.round((currentQuestionIndex / totalQuestions) * 100)}% Complete
          </span>
        </div>
        <div className="w-full bg-yellow-200 rounded-full h-2 mt-2">
          <div
            className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(currentQuestionIndex / totalQuestions) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Question content */}
      <div className="">
        <WaterfallDragDrop
          key={`question-${currentQuestionIndex}`} // Add key to force re-render on question change
          question={question}
          title={title}
          onCorrect={onCorrect}
          handleSubmit={handleSubmit}
          onNext={onNext}
          isLastQuestion={isLastQuestion}
        />
      </div>
    </div>
  );
};

export default QuestionCard;
