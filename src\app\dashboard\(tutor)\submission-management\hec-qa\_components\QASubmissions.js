'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import NewTablePage from '@/components/form/NewTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import { toast } from 'sonner';
import api from '@/lib/api';

const QASubmissions = () => {
  const router = useRouter();
  
  // State variables
  const [submissions, setSubmissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchField, setSearchField] = useState('studentName');
  const [sortField, setSortField] = useState('submittedAt');
  const [sortDirection, setSortDirection] = useState('DESC');
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedSubmission, setSelectedSubmission] = useState(null);
  const [showSubmissionModal, setShowSubmissionModal] = useState(false);
  const [activeTab, setActiveTab] = useState(0); // 0 for Pending, 1 for Reviewed

  // Define tabs
  const tabs = [
    { name: 'Pending Review', value: 'pending' },
    { name: 'Reviewed', value: 'reviewed' }
  ];

  // Mock data for development - replace with actual API call
  useEffect(() => {
    const fetchSubmissions = async () => {
      setLoading(true);
      try {
        // This would be replaced with an actual API call
        // const response = await api.get('/tutor/qa-submissions', {
        //   params: {
        //     page: currentPage,
        //     limit: rowsPerPage,
        //     search: searchTerm,
        //     searchField,
        //     sortField,
        //     sortDirection,
        //     status: tabs[activeTab].value
        //   }
        // });
        
        // Mock data for development
        const mockData = Array(18).fill(null).map((_, index) => ({
          id: `submission-${index + 1}`,
          studentName: `Student ${index + 1}`,
          questionTitle: `Question ${index + 1}`,
          submittedAt: new Date(Date.now() - Math.random() * 10000000000).toISOString(),
          status: index % 3 === 0 ? 'reviewed' : 'pending',
          reviewedBy: index % 3 === 0 ? 'Tutor Name' : null,
          reviewedAt: index % 3 === 0 ? new Date(Date.now() - Math.random() * 1000000000).toISOString() : null,
          question: `This is the question ${index + 1} that the student is asking.`,
          answer: `This is the answer provided by Student ${index + 1} to the question.`
        }));
        
        // Filter based on active tab
        const filteredData = mockData.filter(item => 
          (activeTab === 0 && item.status === 'pending') || 
          (activeTab === 1 && item.status === 'reviewed')
        );
        
        setSubmissions(filteredData);
        setTotalItems(filteredData.length);
        setTotalPages(Math.ceil(filteredData.length / rowsPerPage));
      } catch (error) {
        console.error('Error fetching Q&A submissions:', error);
        toast.error('Failed to load Q&A submissions');
      } finally {
        setLoading(false);
      }
    };
    
    fetchSubmissions();
  }, [currentPage, rowsPerPage, searchTerm, searchField, sortField, sortDirection, activeTab]);

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle search
  const handleSearch = (term) => {
    setSearchTerm(term);
    setCurrentPage(1);
  };

  // Handle search field change
  const handleSearchFieldChange = (field) => {
    setSearchField(field);
    setCurrentPage(1);
  };

  // Handle sort
  const handleSort = (field, direction) => {
    setSortField(field);
    setSortDirection(direction);
  };

  // Handle tab change
  const handleTabChange = (index) => {
    setActiveTab(index);
    setCurrentPage(1);
  };

  // Handle view submission
  const handleViewSubmission = (submission) => {
    setSelectedSubmission(submission);
    setShowSubmissionModal(true);
  };

  // Define table columns
  const columns = [
    { field: '#', label: '#', sortable: false },
    { field: 'studentName', label: 'STUDENT NAME', sortable: true },
    { field: 'questionTitle', label: 'QUESTION', sortable: true },
    { 
      field: 'submittedAt', 
      label: 'SUBMITTED AT', 
      sortable: true,
      cellRenderer: (value) => new Date(value).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    },
    { 
      field: 'status', 
      label: 'STATUS', 
      sortable: true,
      cellRenderer: (value) => (
        value === 'reviewed' ? (
          <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-medium">
            Reviewed
          </span>
        ) : (
          <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-xs font-medium">
            Pending
          </span>
        )
      )
    }
  ];

  // Define actions for table rows
  const actions = [
    {
      label: 'View',
      onClick: handleViewSubmission
    }
  ];

  // Define search filter options
  const searchFilterOptions = [
    { label: 'Student Name', value: 'studentName' },
    { label: 'Question', value: 'questionTitle' }
  ];

  // Submission Modal Component
  const SubmissionModal = ({ isOpen, onClose, submission }) => {
    if (!isOpen || !submission) return null;
    
    const [feedback, setFeedback] = useState('');
    const [score, setScore] = useState(0);
    const [isSubmitting, setIsSubmitting] = useState(false);
    
    const handleSubmitReview = async () => {
      setIsSubmitting(true);
      try {
        // This would be replaced with an actual API call
        // await api.post(`/tutor/qa-submissions/${submission.id}/review`, {
        //   feedback,
        //   score
        // });
        
        // Mock success
        setTimeout(() => {
          toast.success('Review submitted successfully');
          onClose();
          // Refresh the submissions list
          const updatedSubmissions = submissions.map(item => 
            item.id === submission.id 
              ? { ...item, status: 'reviewed', reviewedBy: 'Current Tutor', reviewedAt: new Date().toISOString() }
              : item
          );
          setSubmissions(updatedSubmissions);
        }, 1000);
      } catch (error) {
        console.error('Error submitting review:', error);
        toast.error('Failed to submit review');
      } finally {
        setIsSubmitting(false);
      }
    };
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-auto">
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Q&A Submission</h2>
              <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="grid grid-cols-2 gap-4 bg-gray-50 p-4 rounded-lg mb-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Student Name</p>
                <p className="font-medium">{submission.studentName}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Submission Date</p>
                <p className="font-medium">{new Date(submission.submittedAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Status</p>
                <p className="font-medium">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    submission.status === 'reviewed' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {submission.status === 'reviewed' ? 'Reviewed' : 'Pending'}
                  </span>
                </p>
              </div>
            </div>
            
            <div className="mb-4">
              <h3 className="text-lg font-semibold mb-2">Question</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="whitespace-pre-wrap">{submission.question}</p>
              </div>
            </div>
            
            <div className="mb-4">
              <h3 className="text-lg font-semibold mb-2">Student&apos;s Answer</h3>
              <div className="bg-gray-50 p-4 rounded-lg h-64 overflow-y-auto">
                <p className="whitespace-pre-wrap">{submission.answer}</p>
              </div>
            </div>
            
            {submission.status === 'pending' && (
              <>
                <div className="mb-4">
                  <h3 className="text-lg font-semibold mb-2">Provide Feedback</h3>
                  <textarea
                    value={feedback}
                    onChange={(e) => setFeedback(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg h-32"
                    placeholder="Enter your feedback for the student..."
                  />
                </div>
                
                <div className="mb-4">
                  <h3 className="text-lg font-semibold mb-2">Score (0-100)</h3>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    value={score}
                    onChange={(e) => setScore(Math.min(100, Math.max(0, parseInt(e.target.value) || 0)))}
                    className="w-full p-3 border border-gray-300 rounded-lg"
                  />
                </div>
              </>
            )}
            
            <div className="flex justify-end">
              {submission.status === 'pending' ? (
                <button
                  onClick={handleSubmitReview}
                  disabled={isSubmitting || !feedback.trim()}
                  className={`px-4 py-2 rounded-lg ${
                    isSubmitting || !feedback.trim()
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-yellow-500 text-white hover:bg-yellow-600'
                  }`}
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Review'}
                </button>
              ) : (
                <button
                  onClick={onClose}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300"
                >
                  Close
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Q&A Submissions</h1>
      </div>
      
      {/* Tab buttons */}
      <div className="flex border-b border-gray-200 mb-6">
        {tabs.map((tab, index) => (
          <button
            key={index}
            className={`py-3 px-6 font-medium text-sm focus:outline-none ${
              activeTab === index 
                ? 'text-black border-b-2 border-yellow-500 hover:bg-[#FEFCE8]' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => handleTabChange(index)}
          >
            {tab.name}
          </button>
        ))}
      </div>
      
      {/* Table */}
      <NewTablePage
        columns={columns}
        data={submissions}
        actions={actions}
        loading={loading}
        
        // Pagination props
        currentPage={currentPage}
        totalPages={totalPages}
        changePage={handlePageChange}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        
        // Search and filter props
        showSearch={true}
        showNameFilter={true}
        showSortFilter={true}
        
        // Pass current state values
        searchTerm={searchTerm}
        searchField={searchField}
        sortField={sortField}
        sortDirection={sortDirection}
        
        // Pass handlers for search, filter and sort
        onSearch={handleSearch}
        onNameFilterChange={handleSearchFieldChange}
        onSort={handleSort}
        
        // Pass name filter options
        nameFilterOptions={searchFilterOptions}
      />
      
      {/* Submission Modal */}
      <SubmissionModal
        isOpen={showSubmissionModal}
        onClose={() => setShowSubmissionModal(false)}
        submission={selectedSubmission}
      />
    </div>
  );
};

export default QASubmissions;
