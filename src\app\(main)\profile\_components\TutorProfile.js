'use client';
import { Icon } from '@iconify/react';
import Image from 'next/image';

const TutorProfile = ({ profileData }) => {
  if (!profileData) {
    return (
      <div className="max-w-7xl mx-auto p-6 text-center">
        <div className="bg-white p-8 rounded-lg shadow-md">
          <Icon
            icon="mdi:account-alert"
            className="text-4xl mx-auto text-gray-400 mb-4"
          />
          <h2 className="text-2xl font-semibold text-gray-700">
            Profile Not Found
          </h2>
        </div>
      </div>
    );
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const renderEducation = () => (
    <div className="bg-white rounded-lg shadow-md p-6 border">
      <div className="flex items-center gap-2 border-b pb-3 mb-4">
        <Icon icon="mdi:school" className="text-xl text-yellow-600" />
        <h3 className="text-lg font-semibold">Education</h3>
      </div>
      {profileData.education?.map((edu) => (
        <div key={edu.id} className="mb-6 last:mb-0">
          <div className="flex justify-between items-start">
            <div>
              <h4 className="font-medium text-lg">{edu.degree}</h4>
              <p className="text-gray-600">{edu.institution}</p>
              <p className="text-sm text-gray-500">{edu.fieldOfStudy}</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">
                {formatDate(edu.startDate)} -{' '}
                {edu.isCurrent ? 'Present' : formatDate(edu.endDate)}
              </p>
              {edu.grade && (
                <p className="text-sm text-gray-600">{edu.grade}</p>
              )}
            </div>
          </div>
          {edu.description && (
            <p className="mt-2 text-gray-700">{edu.description}</p>
          )}
          {edu.activities && (
            <div className="mt-2">
              <p className="text-sm font-medium text-gray-600">Activities:</p>
              <p className="text-sm text-gray-600">{edu.activities}</p>
            </div>
          )}
          {edu.location && (
            <div className="flex items-center mt-2 text-sm text-gray-600">
              <Icon icon="mdi:map-marker" className="mr-1" />
              {edu.location}
            </div>
          )}
        </div>
      ))}
    </div>
  );

  const renderAssignedStudents = () => (
    <div className="bg-white rounded-lg shadow-md p-6 border">
      <div className="flex items-center justify-between border-b pb-3 mb-4">
        <div className="flex items-center gap-2">
          <Icon icon="mdi:account-group" className="text-xl text-yellow-600" />
          <h3 className="text-lg font-semibold">
            Assigned Students ({profileData.studentCount})
          </h3>
        </div>
      </div>
      <div className="space-y-4">
        {profileData.assignedStudents?.map((student) => (
          <div
            key={student.id}
            className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg"
          >
            <div className="flex items-center gap-3">
              <div className="bg-gray-200 rounded-full h-10 w-10 flex items-center justify-center">
                <Icon icon="mdi:account" className="text-gray-600 text-xl" />
              </div>
              <div>
                <p className="font-medium">{student.studentName}</p>
                <p className="text-sm text-gray-500">{student.moduleName}</p>
              </div>
            </div>
            <button className="text-yellow-600 hover:text-yellow-800 p-1">
              <Icon icon="mdi:chevron-right" className="text-xl" />
            </button>
          </div>
        ))}
      </div>
    </div>
  );

  const renderAssignedModules = () => (
    <div className="bg-white rounded-lg shadow-md p-6 border">
      <div className="flex items-center gap-2 border-b pb-3 mb-4">
        <Icon icon="mdi:book-education" className="text-xl text-yellow-600" />
        <h3 className="text-lg font-semibold">Assigned Modules</h3>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {profileData.assignedModules?.map((module) => (
          <div
            key={module.id}
            className="border rounded-lg p-3 hover:bg-gray-50"
          >
            <p className="font-medium">{module.name}</p>
            <div className="flex justify-between items-center mt-2">
              <span className="text-xs text-gray-500">
                Module ID: {module.id}
              </span>
              <button className="text-yellow-600 hover:text-yellow-800 text-sm flex items-center gap-1">
                View <Icon icon="mdi:open-in-new" className="text-xs" />
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="max-w-7xl mx-auto p-4 md:p-6 space-y-6">
      {/* Profile Header */}
      <div className="bg-gradient-to-r from-yellow-200 to-yellow-400 rounded-xl p-6 shadow-lg text-yellow-800">
        <div className="flex flex-col md:flex-row items-start gap-6">
          <div className="relative h-32 w-32 rounded-full border-4 border-white bg-gray-200 overflow-hidden shadow-md flex items-center justify-center">
            {profileData.profilePictureUrl ? (
              <Image
                src={profileData.profilePictureUrl}
                alt={profileData.name}
                fill
                className="object-cover"
              />
            ) : (
              <Icon
                icon="mdi:account-circle"
                className="h-full w-full text-gray-400"
              />
            )}
          </div>
          <div className="flex-1 space-y-3">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold">{profileData.name}</h1>
                <div className="flex items-center gap-2 text-yellow-700 mt-1">
                  <Icon icon="mdi:email" />
                  <span>{profileData.email}</span>
                </div>
                <div className="flex items-center gap-2 text-yellow-700 mt-1">
                  <Icon icon="mdi:phone" />
                  <span>{profileData.phoneNumber}</span>
                </div>
              </div>
              <div className="flex gap-2">
                {/* <button className="bg-white/20 hover:bg-white/30 backdrop-blur-sm px-4 border border-yellow-300 shadow py-2 rounded-lg flex items-center gap-2 transition-all">
                  <Icon icon="mdi:pencil" />
                  <span>Edit Profile</span>
                </button> */}
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              <span className="bg-white/20 px-3 py-1 rounded-full text-sm flex items-center gap-1">
                <Icon
                  icon={
                    profileData.isActive
                      ? 'mdi:check-circle'
                      : 'mdi:close-circle'
                  }
                />
                {profileData.isActive ? 'Active' : 'Inactive'}
              </span>
              <span className="bg-white/20 px-3 py-1 rounded-full text-sm flex items-center gap-1">
                <Icon
                  icon={
                    profileData.isApproved
                      ? 'mdi:shield-check'
                      : 'mdi:shield-alert'
                  }
                />
                {profileData.isApproved ? 'Approved' : 'Pending Approval'}
              </span>
              {profileData.roles.map((role, index) => (
                <span
                  key={index}
                  className="bg-white/20 px-3 py-1 rounded-full text-sm flex items-center gap-1 capitalize"
                >
                  <Icon icon="mdi:badge-account" />
                  {role}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column */}
        <div className="space-y-6 lg:col-span-2">
          {renderEducation()}
          {renderAssignedStudents()}

          {renderAssignedModules()}
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* Personal Info */}
          <div className="bg-white rounded-lg shadow-md p-6 border">
            <div className="flex items-center gap-2 border-b pb-3 mb-4">
              <Icon
                icon="mdi:account-details"
                className="text-xl text-yellow-600"
              />
              <h3 className="text-lg font-semibold">Personal Information</h3>
            </div>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-500">Gender</p>
                <p className="capitalize">{profileData.gender || 'N/A'}</p>
              </div>
              {profileData.address && (
                <div>
                  <p className="text-sm text-gray-500">Address</p>
                  <p>{profileData.address}</p>
                </div>
              )}
              {(profileData.city ||
                profileData.state ||
                profileData.country) && (
                <div>
                  <p className="text-sm text-gray-500">Location</p>
                  <p>
                    {[profileData.city, profileData.state, profileData.country]
                      .filter(Boolean)
                      .join(', ')}
                  </p>
                </div>
              )}
              {profileData.postalCode && (
                <div>
                  <p className="text-sm text-gray-500">Postal Code</p>
                  <p>{profileData.postalCode}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TutorProfile;
