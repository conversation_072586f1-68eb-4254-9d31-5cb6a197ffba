'use client';
import React from 'react';
import Link from 'next/link';
import { FiAlertTriangle, FiRefreshCw } from 'react-icons/fi';

const EssayError = ({ error, reset }) => {
  return (
    <div className="container mx-auto p-6">
      <div className="bg-white rounded-lg shadow-md p-8 max-w-2xl mx-auto">
        <div className="flex flex-col items-center text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <FiAlertTriangle className="text-red-500 text-2xl" />
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Something went wrong</h2>
          <p className="text-gray-600 mb-6">
            {error?.message || 'An error occurred while loading the diary content.'}
          </p>
          <div className="flex space-x-4">
            <button
              onClick={reset}
              className="flex items-center bg-yellow-500 text-white px-4 py-2 rounded-md hover:bg-yellow-600 transition-colors"
            >
              <FiRefreshCw className="mr-2" /> Try Again
            </button>
            <Link
              href="/eassy"
              className="flex items-center bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors"
            >
              Go to Essay.
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EssayError;
