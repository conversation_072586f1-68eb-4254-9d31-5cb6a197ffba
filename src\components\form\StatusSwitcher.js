import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import api from '@/lib/api';
import { toast } from 'sonner';

const StatusSwitcher = ({
  endPoint,
  status,
  onSuccess,
  activeColor = 'green',
  inactiveColor = 'gray',
}) => {
  const [currentStatus, setCurrentStatus] = useState(status);
  const [isLoading, setIsLoading] = useState(false);

  const toggleStatus = async () => {
    try {
      setIsLoading(true);

      // Make API call to update status
      console.log('Making API call to:', endPoint, 'with payload:', { isPromotionActive: !currentStatus });
      await api.patch(`${endPoint}`, {
        isPromotionActive: !currentStatus,
      });

      // Update local state
      setCurrentStatus(!currentStatus);

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error toggling status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Determine colors based on currentStatus
  const bgColor = currentStatus
    ? `bg-${activeColor}-100 border-${activeColor}-200`
    : `bg-${inactiveColor}-100 border-${inactiveColor}-200`;

  const toggleColor = currentStatus
    ? `bg-${activeColor}-500`
    : `bg-${inactiveColor}-400`;

  return (
    <button
      onClick={toggleStatus}
      disabled={isLoading}
      className={`relative inline-flex h-6 w-11 items-center rounded-full border ${bgColor} transition-colors focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-offset-1 disabled:opacity-50`}
      aria-pressed={currentStatus}
    >
      <span className="sr-only">Toggle status</span>
      <span
        className={`${
          currentStatus ? 'translate-x-6' : 'translate-x-1'
        } inline-block h-4 w-4 transform rounded-full ${toggleColor} transition-transform`}
      />
      {isLoading && (
        <span className="absolute -right-6">
          <Icon icon="eos-icons:loading" className="w-4 h-4 text-yellow-500" />
        </span>
      )}
    </button>
  );
};

export default StatusSwitcher;
