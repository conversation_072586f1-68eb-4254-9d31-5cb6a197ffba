'use client';
import useDataFetch from '@/hooks/useDataFetch';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';
import React from 'react';
import { format, formatDistanceToNow } from 'date-fns';
import RegularGoBack from '@/components/shared/RegularGoBack';

const StudentProfile = () => {
    const { id } = useParams();
    const router = useRouter();

    const { data: studentProfile, isLoading } = useDataFetch({
        queryKey: ['student-profile', id],
        endPoint: `/profiles/student/${id}`,
    });

    if (isLoading) {
        return (
            <div className="flex justify-center items-center min-h-[60vh]">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
            </div>
        );
    }

    if (!studentProfile) {
        return (
            <div className="text-center py-12">
                <Icon icon="mdi:account-alert" className="text-6xl text-yellow-500 mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-gray-800 mb-2">Student Not Found</h2>
                <p className="text-gray-600 mb-6">The student profile you're looking for doesn't exist or you don't have access to it.</p>
                <button
                    onClick={() => router.back()}
                    className="px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors"
                >
                    Go Back
                </button>
            </div>
        );
    }

    // Format dates
    const formatDate = (dateString) => {
        if (!dateString) return 'N/A';
        return format(new Date(dateString), 'MMM dd, yyyy');
    };

    return (
        <div className="">
            {/* Back Button */}
            <RegularGoBack title={'Students'} className={'pb-5 max-w-28'} />

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Left Column - Profile Card */}
                <div className="lg:col-span-1">
                    <div className="bg-white rounded-xl shadow-md overflow-hidden border">
                        {/* Profile Header */}
                        <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-24 relative"></div>

                        <div className="px-6 pb-6">
                            {/* Profile Picture */}
                            <div className="relative h-24 w-24 rounded-full border-4 border-white bg-gray-200 overflow-hidden shadow-md flex items-center justify-center -mt-12 mb-4 mx-auto">
                                {studentProfile?.profilePictureUrl ? (
                                    <Image
                                        src={studentProfile.profilePictureUrl}
                                        alt={studentProfile.name}
                                        fill
                                        className="object-cover"
                                    />
                                ) : (
                                    <Icon
                                        icon="mdi:account-circle"
                                        className="h-full w-full text-gray-400"
                                    />
                                )}
                            </div>

                            {/* Name and Status */}
                            <div className="text-center mb-6">
                                <h1 className="text-2xl font-bold text-gray-800">{studentProfile.name}</h1>
                                <div className="flex items-center justify-center mt-2">
                                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                        studentProfile.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                    }`}>
                                        <span className={`w-2 h-2 rounded-full mr-1.5 ${
                                            studentProfile.isActive ? 'bg-green-500' : 'bg-red-500'
                                        }`}></span>
                                        {studentProfile.isActive ? 'Active' : 'Inactive'}
                                    </span>
                                </div>
                            </div>

                            {/* Contact Information */}
                            <div className="space-y-3">
                                <div className="flex items-center text-gray-700">
                                    <Icon icon="mdi:email-outline" className="mr-3 text-xl text-gray-500" />
                                    <span>{studentProfile.email}</span>
                                </div>
                                {studentProfile.phoneNumber && (
                                    <div className="flex items-center text-gray-700">
                                        <Icon icon="mdi:phone-outline" className="mr-3 text-xl text-gray-500" />
                                        <span>{studentProfile.phoneNumber}</span>
                                    </div>
                                )}
                                {studentProfile.gender && (
                                    <div className="flex items-center text-gray-700">
                                        <Icon icon="mdi:gender-male-female" className="mr-3 text-xl text-gray-500" />
                                        <span className="capitalize">{studentProfile.gender}</span>
                                    </div>
                                )}
                            </div>

                            {/* Account Information */}
                            <div className="mt-6 pt-6 border-t border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-800 mb-3">Account Information</h3>
                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-gray-500">User ID:</span>
                                        <span className="font-medium">{studentProfile.userId}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-500">Account Type:</span>
                                        <span className="font-medium capitalize">{studentProfile.type}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-500">Joined:</span>
                                        <span className="font-medium">{formatDate(studentProfile.createdAt)}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default StudentProfile;