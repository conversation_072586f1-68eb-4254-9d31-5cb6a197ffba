'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { useDispatch, useSelector } from 'react-redux';
import { setGoBackStep } from '@/store/features/authSlice';
import api from '@/lib/api';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import FormInput from '@/components/form/FormInput';
import { Icon } from '@iconify/react';
import Link from 'next/link';
import NumberInput from '@/components/form/NumberInput';

const roles = [
  {
    id: 'tutor',
    title: 'Tutor',
    image: '/assets/images/auth/role-mentor.png',
  },
  {
    id: 'student',
    title: 'Student',
    image: '/assets/images/auth/role-user.png',
  },
];

export default function RegisterPage() {
  const [selectedRole, setSelectedRole] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const dispatch = useDispatch();
  const currentPath = useSelector((state) => state.auth.goBackStep);
  const [successMessage, setSuccessMessage] = useState('');

  // Reset path when component mounts
  useEffect(() => {
    dispatch(setGoBackStep('register-role'));
  }, [dispatch]);

  const RegisterValidation = Yup.object().shape({
    userId: Yup.string().required('ID is required'),
    email: Yup.string()
      .email('Invalid email format')
      .matches(
        /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
        'Invalid email format'
      )
      .required('Please enter a valid email address'),
    phoneNumber: Yup.string().required('Enter Phone Number'),
    // gender: Yup.string().required('Select Gender is required'),
    password: Yup.string()
      .required('Please enter a password')
      .min(8, 'Password must be at least 8 characters')
      .matches(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>_])[A-Za-z\d!@#$%^&*(),.?":{}|<>_]/,
        'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special (@, $, !, %, *, ?, &, or _) character'
      ),
    confirmPassword: Yup.string()
      .oneOf([Yup.ref('password'), null], 'Passwords must match')
      .required('Please confirm your password'),
    agreedToTerms: Yup.boolean()
      .oneOf([true], 'You must agree to Terms of Service and Privacy Policy')
      .required('You must agree to Terms of Service and Privacy Policy'),
  });

  const handleRoleSelect = (roleId) => {
    setSelectedRole(roleId);
  };

  const handleNext = () => {
    dispatch(setGoBackStep('register-role/register-form'));
  };

  const handleRegister = async (
    values,
    { setSubmitting, setErrors, setFieldError, resetForm }
  ) => {
    // console.log(values);
    try {
      const response = await api.post('/auth/register', values);
      // console.log(response);
      // if (response?.success) {
      resetForm();
      setSuccessMessage(response?.message);
      localStorage.setItem('email', values.email);
      // }
    } catch (error) {
      // console.log(error?.response?.data?.validationErrors);
      if (error?.response?.data?.validationErrors) {
        const validationErrors = error?.response?.data?.validationErrors;
        // Handle object format where keys are field names and values are arrays of error messages
        for (const field in validationErrors) {
          if (validationErrors.hasOwnProperty(field)) {
            const errorMessages = validationErrors[field];
            // Use the first error message for each field
            if (Array.isArray(errorMessages) && errorMessages.length > 0) {
              console.log(field, errorMessages[0]);
              setFieldError(field, errorMessages[0]);
            } else if (typeof errorMessages === 'string') {
              console.log(field, errorMessages);
              setFieldError(field, errorMessages);
            }
          }
        }
      }
      // setErrors({
      //   general: error?.response?.data?.message || 'Login failed!',
      // });
    }
    setSubmitting(false);
  };

  return (
    <div className="w-full max-w-2xl mx-auto p-4">
      {currentPath === 'register-role' ? (
        <>
          <div className="text-center mb-8 text-start text-gray-800">
            <h2 className="text-3xl font-semibold mb-4">SELECT USER ROLE</h2>
            <h2 className="text-xl font-[500] mb-4">
              Select user role for Sign Up
            </h2>
            <p className="text-gray-600">
              When signing up, users must select their role to access the
              appropriate features and permissions within the system.{' '}
              <span className="font-semibold">Admin</span> ,
              <span className="font-semibold"> Mentor</span>, and{' '}
              <span className="font-semibold"> User</span> , each with different
              levels of access. Selecting the correct role ensures a
              personalized and secure experience
            </p>
          </div>

          <div className="flex items-center justify-center max-sm:gap-3 gap-6 mb-8">
            {roles.map((role) => (
              <div
                key={role.id}
                onClick={() => handleRoleSelect(role.id)}
                className={`relative rounded-lg p-4 min-w-52 cursor-pointer transition-all duration-300 hover:shadow-lg
                  ${
                    selectedRole === role.id
                      ? 'border-2 border-green-500 shadow-lg'
                      : 'border-2 border-gray-200'
                  }`}
              >
                <div className="flex flex-col items-center">
                  <div className="w-14 h-14 xl:w-28 xl:h-28 relative mb-2 flex items-center">
                    <Image
                      src={role.image}
                      alt={role.title}
                      height={300}
                      width={300}
                      className="object-contain"
                    />
                  </div>
                  <h3 className="text-xl max-sm:text-base font-semibold mb-2">
                    {role.title}
                  </h3>
                </div>
                {selectedRole === role.id && (
                  <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2">
                    <Icon
                      icon="mdi:check-circle"
                      className="text-green-500 w-6 h-6 bg-white rounded-full"
                    />
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="flex justify-center">
            <button
              onClick={handleNext}
              disabled={!selectedRole}
              className={`px-5 py-3 w-full rounded-lg text-xl font-[500] transition-all duration-300
                ${
                  selectedRole
                    ? 'bg-[#FFDE34] text-gray-700 hover:bg-yellow-400'
                    : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                }`}
            >
              Next{' '}
              <Icon
                icon="ep:right"
                width="18"
                height="18"
                className="inline-block mb-0.5 ml-1"
              />
            </button>
          </div>

          <div className="text-sm text-center mt-4">
            Already have an account?{'  '}
            <Link href="/login" className="text-yellow-500">
              Sign in
            </Link>
          </div>
        </>
      ) : (
        <div className="w-full max-w-md mx-auto p-4 pb-0 space-y-3">
          <h2 className="text-3xl font-bold text-gray-900">SIGN UP</h2>

          <p className="text-gray-500">
            Now you are going to Sign Up as{' '}
            {selectedRole === 'admin' ? 'an' : 'a'}{' '}
            <span className="font-semibold">
              {selectedRole?.charAt(0).toUpperCase() + selectedRole?.slice(1)}
            </span>{' '}
            .
          </p>

          {successMessage?.length > 0 ? (
            <p className="text-green-500">{successMessage}</p>
          ) : (
            <Formik
              initialValues={{
                userId: '',
                email: '',
                phoneNumber: '',
                gender: '',
                password: '',
                confirmPassword: '',
                agreedToTerms: false,
                type: selectedRole,
              }}
              validationSchema={RegisterValidation}
              onSubmit={handleRegister}
            >
              {({ isSubmitting, errors, touched, values }) => (
                <Form className="space-y-3">
                  {/* {console.log(errors)} */}
                  <div className="space-y-3">
                    <FormInput
                      label="ID Select"
                      name="userId"
                      type="text"
                      placeholder="Enter your user ID"
                      required
                    />

                    <FormInput
                      label="Email Address"
                      name="email"
                      type="email"
                      placeholder="Enter email address"
                      required
                    />

                    <NumberInput
                      label="Phone Number"
                      name="phoneNumber"
                      type="tel"
                      placeholder="Enter phone number"
                      required
                    />

                    <div>
                      <label className="block text-sm sm:text-base font-medium mb-1 ">
                        Select Gender <span className="text-red-500">*</span>
                      </label>
                      <div className="flex gap-6">
                        <label className="flex items-center cursor-pointer">
                          <Field
                            type="radio"
                            name="gender"
                            value="male"
                            className="mr-2"
                          />
                          Male
                        </label>
                        <label className="flex items-center cursor-pointer">
                          <Field
                            type="radio"
                            name="gender"
                            value="female"
                            className="mr-2"
                          />
                          Female
                        </label>
                      </div>
                      {touched.gender && errors.gender && (
                        <div className="text-red-500 text-xs mt-1">
                          {errors.gender}
                        </div>
                      )}
                    </div>

                    <div className="grid max-sm:grid-cols-1 grid-cols-2 gap-4">
                      <div className="relative">
                        <FormInput
                          label="Password"
                          name="password"
                          type={showPassword ? 'text' : 'password'}
                          placeholder="Enter password"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-[42px] transform text-gray-500"
                        >
                          <Icon
                            icon={showPassword ? 'mdi:eye-off' : 'mdi:eye'}
                            width="16"
                            height="16"
                          />
                        </button>
                      </div>

                      <div className="relative">
                        <FormInput
                          label="Confirm Password"
                          name="confirmPassword"
                          type={showPassword ? 'text' : 'password'}
                          placeholder="Confirm password"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-[42px] transform text-gray-500"
                        >
                          <Icon
                            icon={showPassword ? 'mdi:eye-off' : 'mdi:eye'}
                            width="16"
                            height="16"
                          />
                        </button>
                      </div>
                    </div>

                    <p className="text-yellow-600 text-center text-xs">
                    Password must contain at least one uppercase letter, one lowercase letter, one number, and one special (@, $, !, %, *, ?, &, or _) character
                    </p>

                    <button
                      type="submit"
                      disabled={
                        isSubmitting ||
                        !values.userId ||
                        !values.email ||
                        !values.phoneNumber ||
                        !values.gender ||
                        !values.password ||
                        !values.confirmPassword ||
                        !values.agreedToTerms
                      }
                      className={`w-full py-2.5 rounded-md transition-colors ${
                        isSubmitting ||
                        !values.userId ||
                        !values.email ||
                        !values.phoneNumber ||
                        !values.gender ||
                        !values.password ||
                        !values.confirmPassword ||
                        !values.agreedToTerms
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'bg-yellow-300 text-gray-800 hover:bg-yellow-400'
                      }`}
                    >
                      {isSubmitting ? 'Signing up...' : 'Sign Up'}
                    </button>

                    <div className="flex items-center justify-center">
                      <Field
                        type="checkbox"
                        name="agreedToTerms"
                        className="h-4 w-4 text-yellow-500 border-gray-300 rounded cursor-pointer"
                      />
                      <span className="ml-2 text-sm">
                        I'm agree to The{' '}
                        <Link href="/terms" className="text-yellow-500">
                          Terms of Service
                        </Link>{' '}
                        and{' '}
                        <Link href="/privacy" className="text-yellow-500">
                          Privacy Policy
                        </Link>
                      </span>
                    </div>
                    {touched.agreedToTerms && errors.agreedToTerms && (
                      <div className="text-red-500 text-xs text-center">
                        {errors.agreedToTerms}
                      </div>
                    )}
                  </div>

                  <div className="text-sm text-center mt-4">
                    Already have an account?{'  '}
                    <Link href="/login" className="text-yellow-500">
                      Sign in
                    </Link>
                  </div>
                </Form>
              )}
            </Formik>
          )}
        </div>
      )}
    </div>
  );
}
