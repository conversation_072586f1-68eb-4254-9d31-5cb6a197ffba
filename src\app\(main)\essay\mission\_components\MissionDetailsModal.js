'use client';
import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import api from '@/lib/api';
import Image from 'next/image';
import { ButtonIcon } from '@/components/Button';
import EditorViewer from '@/components/EditorViewer';
import { useRouter } from 'next/navigation';

const MissionDetailsModal = ({
  isOpen,
  onClose,
  data,
  link,
  title,
  showBtn = true,
  bodyTitle,
  topic,
  icon,
}) => {
  if (!isOpen) return null;

  const router = useRouter();
  const [loading, setLoading] = useState(false);
  console.log(data)

  const handleGo = async () => {
    try {
      setLoading(true);
      const response = await api.post('/student-essay/start/task', {
        taskId: data?.id,
      });
      console.log(response);
      router.push(`/essay?essayId=${data?.id}`);
    } catch (error) {
      console.error('Error submitting mission:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-xl overflow-hidden">
        <div className="relative">
          <div className="absolute z-10 top-2 right-3">
            <ButtonIcon
              icon="mdi:close"
              innerBtnCls="h-8 w-8"
              btnIconCls="h-5 w-5"
              onClick={onClose}
              aria-label="Close modal"
              withbackground={false}
            />
          </div>
          <div className="bg-[#FFF6EF] p-6 relative shadow-lg text-center">
            <Image
              src={'/assets/images/all-img/woodFrame.png'}
              alt="Mission Confirmation"
              width={600}
              height={200}
              className="max-w-96 mx-auto h-auto"
              priority
            />

            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 mt-6 bg-[#FCF8EF] rounded-lg p-5 px-4 w-[55%]">
              <h2 className="text-2xl font-bold text-yellow-900 font-serif">
                {title}
              </h2>
            </div>
          </div>

          <div className="p-4 space-y-4">
            <div>
              <h2 className="text-xl font-medium text-yellow-700">
                {topic} Topic
              </h2>
              <p>{data?.title}</p>
            </div>
            <div>
              <h2 className="text-xl font-medium text-yellow-700">
                Instructions:
              </h2>
              <EditorViewer data={data?.description} />
            </div>
          </div>

          <div className="flex justify-center space-x-3 border-t border-dashed py-3">
            {showBtn && (
              <button
                disabled={loading}
                onClick={() => handleGo()}
                className="flex items-center gap-2 border border-yellow-800 text-3xl text-yellow-900 bg-gradient-to-b from-[#DCA600] to-[#FFDE5B] px-10 py-1 rounded-full mt-3"
              >
                {loading ? (
                  <>
                    <Icon
                      icon="eos-icons:loading"
                      className="animate-spin mr-2"
                    />
                    Loading...
                  </>
                ) : (
                  <>
                    GO{' '}
                    <ButtonIcon
                      icon={'tabler:arrow-right'}
                      innerBtnCls={'h-8 w-8 '}
                      btnIconCls={'h-4 w-4'}
                    />
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MissionDetailsModal;
