'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';

/**
 * Reusable FeedbackModal component for providing feedback with a wooden sign design
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to close the modal
 * @param {Function} props.onSubmit - Custom submit function that receives feedback text
 * @param {string} props.title - Title for the feedback modal (default: "Teachers Feedback")
 * @param {string} props.placeholder - Placeholder text for textarea (default: "Write here")
 * @param {string} props.submitButtonText - Text for submit button (default: "Confirm")
 * @param {string} props.submitButtonColor - Color class for submit button (default: "bg-yellow-500 hover:bg-yellow-600")
 * @param {Function} props.onFeedbackSubmitted - Optional callback after successful submission
 */
const FeedbackModal = ({ 
  isOpen, 
  onClose, 
  onSubmit,
  title = "Teachers Feedback",
  placeholder = "Write here",
  submitButtonText = "Confirm",
  submitButtonColor = "bg-yellow-500 hover:bg-yellow-600",
  onFeedbackSubmitted
}) => {
  const [feedback, setFeedback] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle feedback submission
  const handleSubmit = async () => {
    if (!feedback.trim()) {
      toast.error('Please enter feedback before submitting');
      return;
    }

    if (!onSubmit) {
      toast.error('No submit function provided');
      return;
    }

    setIsSubmitting(true);
    try {
      // Call the custom submit function passed as prop
      await onSubmit(feedback.trim());
      
      // Clear feedback and close modal on success
      setFeedback('');
      onClose();
      
      // Call optional callback
      if (onFeedbackSubmitted) {
        onFeedbackSubmitted();
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      toast.error(error.message || 'Failed to submit feedback');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!isSubmitting) {
      setFeedback('');
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={handleClose}
        >
          <motion.div
            className="bg-white rounded-xl shadow-xl w-full max-w-2xl overflow-hidden relative"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: 'spring', damping: 20, stiffness: 300 }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Wooden sign header */}
            <div className="relative">
              <div className="bg-[#FFF9FB] pt-2 pb-2 px-2 shadow-xl flex justify-center">
                <Image
                  src="/assets/images/all-img/wooden-feedback-sign.png"
                  alt="Feedback"
                  width={300}
                  height={80}
                  priority
                />
              </div>
            </div>

            {/* Modal content */}
            <div className="p-6">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {title}
                </label>
                <textarea
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg h-32"
                  placeholder={placeholder}
                  disabled={isSubmitting}
                />
              </div>

              {/* Action buttons */}
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={handleClose}
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleSubmit}
                  disabled={isSubmitting || !feedback.trim()}
                  className={`px-4 py-2 text-white rounded-md disabled:opacity-50 ${submitButtonColor}`}
                >
                  {isSubmitting ? 'Submitting...' : submitButtonText}
                </button>
              </div>
            </div>

            {/* Close button */}
            <button
              className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              <Image
                src="/assets/images/all-img/cross-bg.png"
                alt="Close"
                width={40}
                height={40}
                className="w-full h-auto"
                priority
              />
            </button>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default FeedbackModal;
