import { format } from 'date-fns';

/**
 * Formats a date with ordinal day (1st, 2nd, 3rd, etc.)
 * @param {Date|string|number} date - The date to format
 * @param {string} formatType - The format type to use
 * @returns {string} The formatted date string
 */
export const formatDate = (date, formatType = 'default') => {
  if (!date) return '';
  
  const dateObj = date instanceof Date ? date : new Date(date);
  
  // Return empty string for invalid dates
  if (isNaN(dateObj.getTime())) return '';
  
  switch (formatType) {
    case 'ordinal':
      // Format like "29th May, 2025"
      const day = dateObj.getDate();
      const month = dateObj.toLocaleString('en-US', { month: 'long' });
      const year = dateObj.getFullYear();
      const ordinal = getOrdinalSuffix(day);
      return `${day}${ordinal} ${month}, ${year}`;
      
    case 'short':
      // Format like "29 May 2025"
      return format(dateObj, 'dd MMM yyyy');
      
    case 'numeric':
      // Format like "29/05/2025"
      return format(dateObj, 'dd/MM/yyyy');
      
    case 'iso':
      // Format like "2025-05-29"
      return format(dateObj, 'yyyy-MM-dd');
      
    case 'full':
      // Format like "Thursday, May 29, 2025"
      return dateObj.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      
    case 'time':
      // Format like "29 May 2025, 14:30"
      return format(dateObj, 'dd MMM yyyy, HH:mm');
      
    case 'datetime':
      // Format like "29 May 2025, 2:30 PM"
      return format(dateObj, 'dd MMM yyyy, h:mm a');
      
    default:
      // Default format using locale string
      return dateObj.toLocaleDateString();
  }
};

/**
 * Gets the ordinal suffix for a number (1st, 2nd, 3rd, etc.)
 * @param {number} n - The number to get the ordinal suffix for
 * @returns {string} The ordinal suffix
 */
const getOrdinalSuffix = (n) => {
  if (n >= 11 && n <= 13) {
    return 'th';
  }
  
  switch (n % 10) {
    case 1: return 'st';
    case 2: return 'nd';
    case 3: return 'rd';
    default: return 'th';
  }
};
