'use client';
import React from 'react';
import { Formik, Form, FieldArray } from 'formik';
import * as Yup from 'yup';
import { useRouter, useParams } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import api from '@/lib/api';
import FormInput from '@/components/form/FormInput';
import FormSelect from '@/components/form/FormSelect';
import FormRadio from '@/components/form/FormRadio';
import FormCheckbox from '@/components/form/FormCheckbox';
import NumberInput from '@/components/form/NumberInput';
import RegularGoBack from '@/components/shared/RegularGoBack';
import { Icon } from '@iconify/react';
import useDataFetch from '@/hooks/useDataFetch';

const validationSchema = Yup.object().shape({
  name: Yup.string().required('Name is required'),
  type: Yup.string().required('Type is required'),
  subscriptionType: Yup.string().required('Subscription type is required'),
  autoRenew: Yup.boolean().required('Auto renew status is required'),
  description: Yup.string().required('Description is required'),
  price: Yup.number().required('Price is required').min(0, 'Price must be at least 0'),
  durationDays: Yup.number().required('Duration is required').min(1, 'Duration must be at least 1 day'),
  isActive: Yup.boolean().required('Active status is required'),
  planFeatureIds: Yup.array(),
  legacyFeatures: Yup.array().of(
    Yup.object().shape({
      type: Yup.string().required('Legacy feature type is required'),
      name: Yup.string().required('Legacy feature name is required'),
      description: Yup.string().required('Legacy feature description is required'),
      isActive: Yup.boolean().required('Active status is required'),
    })
  ),
});

const planTypeOptions = [
  { value: 'starter', label: 'Starter' },
  { value: 'standard', label: 'Standard' },
  { value: 'pro', label: 'Pro' },
  { value: 'ultimate', label: 'Ultimate' },
];

const subscriptionTypeOptions = [
  { value: 'monthly', label: 'Monthly' },
  { value: 'yearly', label: 'Yearly' },
];

const autoRenewOptions = [
  { value: true, label: 'Yes' },
  { value: false, label: 'No' },
];

const activeStatusOptions = [
  { value: true, label: 'Yes' },
  { value: false, label: 'No' },
];

const featureTypeOptions = [
  { value: 'hec_user_diary', label: 'HEC User Diary' },
  { value: 'hec_play', label: 'HEC Play' },
  { value: 'english_qa_writing', label: 'English Q&A Writing Platform' },
  { value: 'english_essay', label: 'English Essay Platform' },
  { value: 'english_novel', label: 'English Novel Platform' },
];

const EditPlan = () => {
  const { id } = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();

  const { data: planDetails, isLoading } = useDataFetch({
    queryKey: ['plan-details', id],
    endPoint: `/plans/${id}`,
  });

  // Fetch all available plan features
  const { data: featuresData, isLoading: featuresLoading } = useDataFetch({
    queryKey: ['plan-features'],
    endPoint: '/plan-features',
  });

  // The API returns features directly in the data property
  const availableFeatures = featuresData || [];

  const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
    try {
      // Create the update payload with planFeatureIds
      const { planFeatureIds, ...rest } = values;
      const updatePayload = {
        ...rest,
        planFeatureIds
      };

      await api.patch(`/plans/${id}`, updatePayload);
      router.push('/dashboard/plans');
      queryClient.invalidateQueries(['subscription-plans']);
      queryClient.invalidateQueries(['plan-details', id]);
    } catch (error) {
      console.log(error);
      if (error?.response?.data?.validationErrors) {
        const validationErrors = error.response.data.validationErrors;
        Object.keys(validationErrors).forEach((field) => {
          setFieldError(`${field}`, validationErrors[field][0]);
        });
      } else {
        alert('Failed to update plan. Please try again.');
      }
    } finally {
      setSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  // No need to extract plan features separately as we're using planFeatureIds directly

  return (
    <div className="p-5">
      <RegularGoBack className={'pb-5 max-w-32'} title={'Subscription Plans'} />

      <Formik
        enableReinitialize={true}
        initialValues={{
          name: planDetails?.name || '',
          type: planDetails?.type || 'starter',
          subscriptionType: planDetails?.subscriptionType || 'monthly',
          autoRenew: planDetails?.autoRenew || true,
          description: planDetails?.description || '',
          price: planDetails?.price || '',
          durationDays: planDetails?.durationDays || '',
          isActive: planDetails?.isActive || true,
          planFeatureIds: planDetails?.planFeatures?.map(feature => feature.id) || [],
          legacyFeatures: planDetails?.legacyFeatures || [],
        }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ values, isSubmitting }) => (
          <Form className="space-y-6">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h2 className="text-xl font-semibold mb-4 text-gray-800">Plan Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormInput
                  label="Plan Name"
                  name="name"
                  placeholder="Enter plan name"
                  required
                />

                <FormSelect
                  label="Plan Type"
                  name="type"
                  options={planTypeOptions}
                  required
                />

                <FormSelect
                  label="Subscription Type"
                  name="subscriptionType"
                  options={subscriptionTypeOptions}
                  required
                />

                <FormRadio
                  label="Auto Renew"
                  name="autoRenew"
                  options={autoRenewOptions}
                  isHorizontal={true}
                  required
                />

                <div className="md:col-span-2">
                  <FormInput
                    label="Description"
                    name="description"
                    placeholder="Enter plan description"
                    isTextarea={true}
                    required
                  />
                </div>

                <NumberInput
                  label="Price (₩)"
                  name="price"
                  placeholder="Enter price"
                  required
                />

                <NumberInput
                  label="Duration (days)"
                  name="durationDays"
                  placeholder="Enter duration in days"
                  required
                />

                <FormRadio
                  label="Active Status"
                  name="isActive"
                  options={activeStatusOptions}
                  isHorizontal={true}
                  required
                />
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-gray-800">Plan Features</h2>
                <div className="text-sm text-gray-500">Select features to include in this plan</div>
              </div>

              {featuresLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-500 mx-auto"></div>
                  <p className="mt-2 text-gray-600">Loading features...</p>
                </div>
              ) : availableFeatures.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {availableFeatures.map(feature => (
                    <div key={feature.id} className="p-4 border rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                      <div className="flex items-start">
                        <input
                          type="checkbox"
                          id={`feature-${feature.id}`}
                          name="planFeatureIds"
                          value={feature.id}
                          onChange={(e) => {
                            const currentIds = [...values.planFeatureIds];
                            if (e.target.checked) {
                              if (!currentIds.includes(feature.id)) {
                                currentIds.push(feature.id);
                              }
                            } else {
                              const index = currentIds.indexOf(feature.id);
                              if (index !== -1) {
                                currentIds.splice(index, 1);
                              }
                            }
                            setFieldValue('planFeatureIds', currentIds);
                          }}
                          checked={values.planFeatureIds.includes(feature.id)}
                          className="mt-1 h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                        />
                        <label htmlFor={`feature-${feature.id}`} className="ml-3 block">
                          <span className="font-medium text-gray-800">{feature.name}</span>
                          <span className="block text-sm text-gray-600 mt-1">{feature.description}</span>
                          <span className="block text-xs text-gray-500 mt-1">Type: {feature.type}</span>
                          {feature.isActive !== undefined && (
                            <span className={`inline-block mt-1 px-2 py-0.5 text-xs rounded ${feature.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                              {feature.isActive ? 'Active' : 'Inactive'}
                            </span>
                          )}
                        </label>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 bg-gray-50 rounded-lg">
                  <p className="text-gray-600">No features available. Please create features first.</p>
                  <button
                    type="button"
                    onClick={() => router.push('/dashboard/plan-features/add')}
                    className="mt-4 px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
                  >
                    Create New Feature
                  </button>
                </div>
              )}
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm mt-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-gray-800">Legacy Features</h2>
                <div className="text-sm text-gray-500">Add legacy features for backward compatibility</div>
              </div>

              <FieldArray name="legacyFeatures">
                {({ push, remove }) => (
                  <div>
                    {values.legacyFeatures.map((legacyFeature, index) => (
                      <div key={index} className="mb-6 p-4 border rounded-lg bg-gray-50 relative">
                        <div className="absolute top-2 right-2">
                          <button
                            type="button"
                            onClick={() => remove(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <Icon icon="heroicons-outline:trash" className="w-5 h-5" />
                          </button>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormSelect
                            label="Feature Type"
                            name={`legacyFeatures.${index}.type`}
                            options={featureTypeOptions}
                            required
                          />
                          <FormInput
                            label="Feature Name"
                            name={`legacyFeatures.${index}.name`}
                            placeholder="Enter feature name"
                            required
                          />
                          <FormInput
                            label="Feature Description"
                            name={`legacyFeatures.${index}.description`}
                            placeholder="Enter feature description"
                            required
                          />
                          <FormRadio
                            label="Active Status"
                            name={`legacyFeatures.${index}.isActive`}
                            options={activeStatusOptions}
                            isHorizontal={true}
                            required
                          />
                        </div>
                      </div>
                    ))}
                    <button
                      type="button"
                      onClick={() => push({
                        type: '',
                        name: '',
                        description: '',
                        isActive: true,
                      })}
                      className="mt-2 flex items-center text-blue-600 hover:text-blue-800"
                    >
                      <Icon icon="material-symbols:add-circle-outline" className="w-5 h-5 mr-1" />
                      Add Legacy Feature
                    </button>
                  </div>
                )}
              </FieldArray>
            </div>

            <div className="flex justify-end space-x-4 mt-6">
              <button
                type="button"
                onClick={() => router.push('/dashboard/plans')}
                className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-6 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:opacity-50"
              >
                {isSubmitting ? 'Saving...' : 'Update Plan'}
              </button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default EditPlan;
