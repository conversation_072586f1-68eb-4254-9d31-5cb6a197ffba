'use client';
import { useDispatch, useSelector } from 'react-redux';
import { Icon } from '@iconify/react/dist/iconify.js';
import { setShowModal } from '@/store/features/crudSlice';
import { useRouter } from 'next/navigation';
import Pagination from '../Pagination';

const BasicTablePage = ({
  tableHeaderExtra = null,
  title,
  createButton,
  createBtnLink,
  createPage,
  editPage,
  actions = [],
  columns,
  changePage,
  data,
  filter = true,
  setFilter,
  currentPage,
  totalPages,
  submitForm,
  loading,
  showCreateButton = true,
  setShowBulkModal,
  totalItems,
  rowsPerPage,
  setRowsPerPage,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();

  // const handlePageChange = (value) => {
  //   changePage(`?page=${value}`);
  // };
  const openCreateModal = () => {
    dispatch(setShowModal(true));
  };

  return (
    <div className="bg-white rounded-lg lg:overflow-y-visible overflow-y-auto">
      <div className="flex justify-between items-center mb-2">
        <h4 className="card-title text-black text-xl">{title}</h4>

        <div className="flex gap-2">
          {tableHeaderExtra}
          {/* {filter && <GlobalFilter filter={filter} setFilter={setFilter} />} */}
          {createButton && showCreateButton && (
            <button
              className="btn bg-yellow-300 hover:bg-yellow-400 px-3 py-1.5 rounded "
              onClick={() =>
                createBtnLink ? router.push(createBtnLink) : openCreateModal()
              }
            >
              {createButton}
            </button>
          )}
        </div>
      </div>
      {loading ? (
        <h2 className="text-gray-600 text-start">Loading...</h2>
      ) : data?.length > 0 ? (
        <table className="min-w-full divide-y  divide-slate-100 table-fixed shadow-md rounded-lg overflow-x-auto">
          <thead className="bg-gray-100 rounded-t-lg">
            <tr className="border-gray-300">
              {columns.map((column, i) => (
                <th
                  key={i}
                  scope="col"
                  className={`table-th py-3 font-[500] text-md `}
                >
                  {/* ${
                      column.field.match(/(email|phone)/)
                        ? 'text-left min-w-52 px-4'
                        : 'text-center min-w-28'
                    } */}
                  {column.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="w-full divide-y divide-slate-100 text-black">
            {data?.map((row, dataIndex) => (
              <tr key={dataIndex} className="text-sm">
                {columns.map(
                  (column, index) =>
                    column.field && (
                      <td
                        key={index}
                        className={`table-td py-4 w-full md:w-auto px-3 text-center`}
                      >
                        {row[column.field]}
                      </td>
                    )
                )}
                {actions.length > 0 && (
                  <td className="table-td ">
                    <div className="divide-y divide-slate-100 flex items-center justify-center gap-2">
                      {actions.map((item, i) => (
                        <div
                          key={i}
                          className={`text-sm cursor-pointer bg-white border shadow p-1.5 rounded-md ${item.className}`}
                          onClick={() => item.onClick(dataIndex)}
                        >
                          <span className="text-base">
                            <Icon icon={item?.icon} />
                          </span>
                        </div>
                      ))}
                    </div>
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      ) : (
        <p className="text-gray-600 text-start">No data found</p>
      )}

      {totalItems > 0 && (
        <Pagination changePage={changePage} currentPage={currentPage} totalItems={totalItems} rowsPerPage={rowsPerPage} setRowsPerPage={setRowsPerPage} />
      )}
    </div>
  );
};

export default BasicTablePage;
