'use client';

import React from 'react';
import Tooltip from './Tooltip';
import { Icon } from '@iconify/react';

/**
 * Example component to demonstrate the usage of the Tooltip component
 */
const TooltipExample = () => {
  return (
    <div className="p-8 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-semibold mb-6">Tooltip Examples</h2>
      
      <div className="space-y-8">
        {/* Positions */}
        <div>
          <h3 className="text-lg font-medium mb-4">Tooltip Positions</h3>
          <div className="flex flex-wrap gap-4">
            <Tooltip content="This is a top tooltip" position="top">
              <button className="px-4 py-2 bg-blue-500 text-white rounded-md">
                Top Tooltip
              </button>
            </Tooltip>
            
            <Tooltip content="This is a right tooltip" position="right">
              <button className="px-4 py-2 bg-blue-500 text-white rounded-md">
                Right Tooltip
              </button>
            </Tooltip>
            
            <Tooltip content="This is a bottom tooltip" position="bottom">
              <button className="px-4 py-2 bg-blue-500 text-white rounded-md">
                Bottom Tooltip
              </button>
            </Tooltip>
            
            <Tooltip content="This is a left tooltip" position="left">
              <button className="px-4 py-2 bg-blue-500 text-white rounded-md">
                Left Tooltip
              </button>
            </Tooltip>
          </div>
        </div>
        
        {/* Colors */}
        <div>
          <h3 className="text-lg font-medium mb-4">Tooltip Colors</h3>
          <div className="flex flex-wrap gap-4">
            <Tooltip content="Default tooltip" color="default">
              <button className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md">
                Default
              </button>
            </Tooltip>
            
            <Tooltip content="Dark tooltip" color="dark">
              <button className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md">
                Dark
              </button>
            </Tooltip>
            
            <Tooltip content="Light tooltip" color="light">
              <button className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md">
                Light
              </button>
            </Tooltip>
            
            <Tooltip content="Yellow tooltip" color="yellow">
              <button className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md">
                Yellow
              </button>
            </Tooltip>
          </div>
        </div>
        
        {/* Sizes */}
        <div>
          <h3 className="text-lg font-medium mb-4">Tooltip Sizes</h3>
          <div className="flex flex-wrap gap-4">
            <Tooltip content="Small tooltip" size="sm">
              <button className="px-4 py-2 bg-green-500 text-white rounded-md">
                Small
              </button>
            </Tooltip>
            
            <Tooltip content="Medium tooltip" size="md">
              <button className="px-4 py-2 bg-green-500 text-white rounded-md">
                Medium
              </button>
            </Tooltip>
            
            <Tooltip content="Large tooltip" size="lg">
              <button className="px-4 py-2 bg-green-500 text-white rounded-md">
                Large
              </button>
            </Tooltip>
          </div>
        </div>
        
        {/* With Icons */}
        <div>
          <h3 className="text-lg font-medium mb-4">Tooltips with Icons</h3>
          <div className="flex flex-wrap gap-4">
            <Tooltip content="Help information" color="yellow">
              <button className="p-2 rounded-full bg-yellow-100 text-yellow-800">
                <Icon icon="lucide:help-circle" className="w-5 h-5" />
              </button>
            </Tooltip>
            
            <Tooltip content="Information" color="default">
              <button className="p-2 rounded-full bg-blue-100 text-blue-800">
                <Icon icon="lucide:info" className="w-5 h-5" />
              </button>
            </Tooltip>
            
            <Tooltip content="Warning" color="dark">
              <button className="p-2 rounded-full bg-red-100 text-red-800">
                <Icon icon="lucide:alert-triangle" className="w-5 h-5" />
              </button>
            </Tooltip>
          </div>
        </div>
        
        {/* Rich Content */}
        <div>
          <h3 className="text-lg font-medium mb-4">Rich Content Tooltip</h3>
          <Tooltip 
            content={
              <div>
                <h4 className="font-bold mb-1">Rich Content</h4>
                <p>You can add any content inside a tooltip.</p>
                <ul className="list-disc pl-4 mt-1">
                  <li>Including lists</li>
                  <li>And other elements</li>
                </ul>
              </div>
            } 
            size="lg"
            color="light"
          >
            <button className="px-4 py-2 bg-purple-500 text-white rounded-md">
              Rich Content Tooltip
            </button>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

export default TooltipExample;
