import { useField } from 'formik';

const FormInput = ({ label, isTextarea, required, defaultValue, ...props }) => {
  const [field, meta] = useField(props);

  return (
    <div className="">
      <label className="block text-sm sm:text-base font-[500] mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      {!isTextarea ? (
        <input
          {...field}
          {...props}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-none focus:outline-1 focus:outline-yellow-300 ${
            meta.touched && meta.error ? 'border-red-500' : 'border-gray-300'
          }`}
        />
      ) : (
        <textarea
          {...field}
          {...props}
          className={`w-full px-3 py-2 border min-h-28 rounded-lg focus:ring-none focus:outline-yellow-300 ${
            meta.touched && meta.error ? 'border-red-500' : 'border-gray-300'
          }`}
        />
      )}
      {meta.touched && meta.error && (
        <div className="text-red-500 text-xs mt-1">{meta.error}</div>
      )}
    </div>
  );
};

export default FormInput;