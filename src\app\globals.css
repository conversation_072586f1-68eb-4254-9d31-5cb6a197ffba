/* We're using Next.js font loading system instead of direct imports */

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
  --font-open-sans-fallback: 'Open Sans', Arial, sans-serif;
}

/* @media (prefers-color-scheme: light) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
} */

body {
  color: var(--foreground);
  background: var(--background);
  font-family: var(--font-open-sans), var(--font-open-sans-fallback), sans-serif;
}

/* Default font class */
.font-open-sans {
  font-family: var(--font-open-sans), var(--font-open-sans-fallback), sans-serif;
}

.swiper-button-prev,
.swiper-button-next {
  width: 40px !important; /* Adjust button size */
  height: 40px !important;
  background-color: white !important; /* Set background */
  border-radius: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.swiper-button-prev::after,
.swiper-button-next::after {
  font-size: 24px !important;
  color: gray !important;
}

/* Hide number input spinner for all browsers */
.no-spinner::-webkit-outer-spin-button,
.no-spinner::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.no-spinner {
  -webkit-appearance: textfield; /* Chrome, Safari, newer versions of Opera */
  -moz-appearance: textfield; /* Firefox */
  appearance: textfield; /* Standard */
}
/* Common styles */
.loader-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.loader-text {
  margin-top: 1rem;
  color: #666;
  font-size: 0.9rem;
}

/* Spinning Book */
.book-loader {
  perspective: 1000px;
}

.book {
  width: 60px;
  height: 80px;
  position: relative;
  transform-style: preserve-3d;
  animation: rotate 2s infinite linear;
}

.book-page {
  position: absolute;
  width: 100%;
  height: 100%;
  background: #f0f0f0;
  border: 2px solid #ddd;
  transform-origin: left;
  animation: flip 1.5s infinite ease-in-out;
}

/* Pulsing Pages */
.pulsing-pages {
  display: flex;
  gap: 4px;
}

.pulsing-pages .page {
  width: 20px;
  height: 30px;
  background: #4a90e2;
  animation: pulse 1.5s infinite ease-in-out;
}

/* Circular Progress */
.circular-loader {
  width: 50px;
  height: 50px;
}

.circular-loader circle {
  stroke: #4a90e2;
  stroke-linecap: round;
  animation: circle-progress 2s infinite linear;
}

/* Flipping Pages */
.flipping-pages {
  position: relative;
  width: 40px;
  height: 40px;
}

.flip-page {
  position: absolute;
  width: 100%;
  height: 100%;
  background: #4a90e2;
  animation: flip-page 1.2s infinite ease-in-out;
}

/* Dots Wave */
.dots-wave {
  display: flex;
  gap: 6px;
}

.dot {
  width: 8px;
  height: 8px;
  background: #4a90e2;
  border-radius: 50%;
  animation: wave 1s infinite ease-in-out;
}

/* Bookshelf */
.bookshelf {
  display: flex;
  gap: 8px;
  padding: 10px;
  background: #f5f5f5;
  border-radius: 4px;
}

.book-item {
  width: 20px;
  height: 60px;
  background: #4a90e2;
  animation: bounce 0.6s infinite alternate;
}

/* Text Scramble */
.text-scramble {
  display: flex;
  gap: 2px;
}

.letter {
  font-size: 1.2rem;
  font-weight: bold;
  color: #4a90e2;
  animation: scramble 2s infinite;
}

/* Progress Bar */
.progress-bar {
  width: 200px;
  height: 4px;
  background: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  width: 100%;
  height: 100%;
  background: #4a90e2;
  animation: progress 2s infinite;
}

/* Ink Drop */
.ink-drop {
  position: relative;
  width: 60px;
  height: 60px;
}

.drop {
  width: 20px;
  height: 20px;
  background: #4a90e2;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: drop 2s infinite;
}

/* Minimalist Spinner */
.minimalist-spinner {
  width: 40px;
  height: 40px;
  position: relative;
}

.spinner-ring {
  width: 100%;
  height: 100%;
  border: 3px solid #f0f0f0;
  border-top-color: #4a90e2;
  border-radius: 50%;
  animation: spin 1s infinite linear;
}

/* Animations */
@keyframes rotate {
  from {
    transform: rotateY(0deg);
  }
  to {
    transform: rotateY(360deg);
  }
}

@keyframes flip {
  0%,
  100% {
    transform: rotateY(0deg);
  }
  50% {
    transform: rotateY(-180deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(0.8);
    opacity: 0.5;
  }
}

@keyframes circle-progress {
  0% {
    stroke-dasharray: 0 100;
  }
  100% {
    stroke-dasharray: 100 100;
  }
}

@keyframes flip-page {
  0%,
  100% {
    transform: rotateX(0deg);
  }
  50% {
    transform: rotateX(180deg);
  }
}

@keyframes wave {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes bounce {
  to {
    transform: translateY(-10px);
  }
}

@keyframes scramble {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes progress {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes drop {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(3);
    opacity: 0;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Topbar Styles */
.topbar-dropdown {
  transition: all 0.2s ease;
}

.topbar-button {
  transition: all 0.2s ease;
}

.topbar-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-input:focus {
  box-shadow: 0 0 0 2px rgba(255, 222, 52, 0.5);
}



.yellow-btn {
  background-image: linear-gradient(3.66deg, #DCA600 26.83%, #FFDE5B 97.36%);
  background-color: #FFFAC2;
}
.yellow-btn:hover{
  background-image: none;
}

/* Custom Scrollbar Styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #E6D7A9;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #D9C88F;
}







/* Canvas container responsive styles */
.canvas-container-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.canvas-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.canvas-wrapper {
  transform-origin: center;
  max-width: 100%;
  max-height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Scale canvas to fit container while maintaining aspect ratio */
.canvas-wrapper > div {
  transform: scale(0.95); /* Default scale reduced to account for browser zoom */
  transform-origin: center;
  max-width: 100%;
  max-height: 100%;
}

/* Responsive scaling for different screen sizes */
@media (max-width: 1600px) {
  .canvas-wrapper > div {
    transform: scale(0.65);
  }
}

@media (max-width: 1366px) {
  .canvas-wrapper > div {
    transform: scale(0.55);
  }
}

@media (max-width: 1200px) {
  .canvas-wrapper > div {
    transform: scale(0.5);
  }
}

@media (max-width: 1024px) {
  .canvas-wrapper > div {
    transform: scale(0.45);
  }
}

@media (max-width: 768px) {
  .canvas-wrapper > div {
    transform: scale(0.4);
  }
}

@media (max-width: 480px) {
  .canvas-wrapper > div {
    transform: scale(0.35);
  }
}

/* date time component styles */
.custom-calendar {
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 8px;
  font-family: var(--font-open-sans), 'Open Sans', sans-serif;
}

.react-datepicker__header {
  background: none;
  border-bottom: none;
  padding-top: 10px;
}

.react-datepicker__day-name,
.react-datepicker__day,
.react-datepicker__time-name {
  margin: 0.3rem;
  font-size: 0.85rem;
  font-weight: 500;
}

.custom-day {
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  line-height: 2rem;
  text-align: center;
}

.custom-day-selected {
  background-color: #facc15;
  color: white;
  font-weight: bold;
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  line-height: 2rem;
  text-align: center;
}

.react-datepicker__time-container {
  border-left: none;
}

.react-datepicker__time {
  border-radius: 0 0 12px 12px;
}

.react-datepicker__time-box {
  width: 100px;
}

.react-datepicker__time-list-item--selected {
  background-color: #facc15;
  color: white;
  font-weight: bold;
}
