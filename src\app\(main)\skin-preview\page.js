'use client';
import CanvasPreview from '@/components/skin/CanvasPreview';
import React from 'react';

const SkinPreview = () => {
  const skin = {
    canvasItems: [
      {
        id: 'subject',
        type: 'text',
        textType: 'subject',
        zIndex: 5,
        content: 'My Diary Subject',
        styles: {
          fontFamily: 'Roboto',
          color: '#fefbfb',
          fontSize: '1.25rem',
          width: 300,
          height: 40,
          x: 50,
          y: 20,
          textAlign: 'left',
        },
      },
      {
        id: 'date',
        type: 'text',
        textType: 'date',
        zIndex: 5,
        content: '04 May 2025',
        styles: {
          fontFamily: 'Roboto',
          color: '#fafafa',
          fontSize: '0.875rem',
          width: 200,
          height: 30,
          x: 60,
          y: 78,
          textAlign: 'right',
        },
        dateFormat: 'dd MMM yyyy',
      },
      {
        id: 'body',
        type: 'text',
        textType: 'body',
        zIndex: 5,
        content:
          'Write your diary entry here. This is the body text of your diary where you can describe your day, thoughts, and experiences.',
        styles: {
          fontFamily: 'Roboto',
          color: '#ffffff',
          fontSize: '1rem',
          width: 232,
          height: 209,
          x: 319,
          y: 81,
          textAlign: 'left',
        },
      },
      {
        id: 'image-1746417362832',
        type: 'image',
        zIndex: 1,
        styles: {
          x: 359,
          y: 191,
          width: 217,
          height: 274,
        },
        image:
          'http://103.209.40.213:3010/media/shop-items/32cb0002-1fd8-41d5-a727-0d10fe686d8a?v=1746417335924',
      },
    ],
    canvasWidth: 800,
    canvasHeight: 600,
    canvasBackground: '#000080',
  };

  // Example of custom data override (optional)
  const contentData = {
    subject: 'Custom Subject Textsss',
    date: '25 May 2025',
    body: 'This is a custom body text overriding the default.',
  };

  return (
    <>
      <CanvasPreview skin={skin} contentData={contentData} />
    </>
  );
};

export default SkinPreview;
