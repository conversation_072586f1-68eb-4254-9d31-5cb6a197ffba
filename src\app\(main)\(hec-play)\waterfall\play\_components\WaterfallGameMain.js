'use client';
import React, { useState, useEffect } from 'react';
import {
  DndContext,
  rectIntersection,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
} from '@dnd-kit/core';
import { restrictToWindowEdges } from '@dnd-kit/modifiers';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import {
  DraggableOption,
  DroppableBlank,
  DragOverlayContent,
  FeedbackComponent,
} from './WaterfallGame2';
import api from '@/lib/api';
import GameSummary2 from './GameSummary2';
import Button from '@/components/Button';

const WaterfallGameMain = ({ initialData, refetchGame }) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [score, setScore] = useState(0);
  const [gameCompleted, setGameCompleted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [userAnswers, setUserAnswers] = useState([]);
  const [submitResult, setSubmitResult] = useState(null);
  const [error, setError] = useState(null);

  // Game state
  const [blanks, setBlanks] = useState([]);
  const [options, setOptions] = useState([]);
  const [activeId, setActiveId] = useState(null);
  const [activeOption, setActiveOption] = useState(null);
  const [showFeedback, setShowFeedback] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  const [hasChecked, setHasChecked] = useState(false);

  const questions = initialData?.questions || [];
  const setId = initialData?.id || '';

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor)
  );

  // Initialize blanks and options for the current question
  useEffect(() => {
    if (questions.length > 0 && currentQuestionIndex < questions.length) {
      const question = questions[currentQuestionIndex];

      // Count the number of blanks in the question
      const blankCount = (
        question.question_text_plain.match(/\[\[gap\]\]/g) || []
      ).length;

      // Initialize blanks array with empty values
      setBlanks(Array(blankCount).fill(null));

      // Initialize options array with the provided options
      setOptions(
        question.options.map((option, index) => ({
          id: `option-${index}`,
          text: option,
          used: false,
        }))
      );

      // Reset state for new question
      setShowFeedback(false);
      setIsCorrect(false);
      setHasChecked(false);
    }
  }, [questions, currentQuestionIndex]);

  // Handle drag start
  const handleDragStart = (event) => {
    const { active } = event;
    setActiveId(active.id);

    if (active.id.startsWith('option-')) {
      const optionIndex = parseInt(active.id.split('-')[1]);
      setActiveOption(options[optionIndex].text);
    }
  };

  // Handle drag end
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      // If dropping onto a blank
      if (over.id.startsWith('blank-')) {
        const blankIndex = parseInt(over.id.split('-')[1]);
        const newBlanks = [...blanks];

        // If dragging from options
        if (active.id.startsWith('option-')) {
          const optionIndex = parseInt(active.id.split('-')[1]);
          const newOptions = [...options];

          // Check if the blank already has a value
          const currentBlankValue = newBlanks[blankIndex];

          // If the blank already has a value, free up that option first
          if (currentBlankValue !== null) {
            const currentOptionIndex = newOptions.findIndex(
              (opt) => opt.text === currentBlankValue
            );

            if (currentOptionIndex !== -1) {
              newOptions[currentOptionIndex] = {
                ...newOptions[currentOptionIndex],
                used: false,
              };
            }
          }

          // Update the blank with the option text
          newBlanks[blankIndex] = newOptions[optionIndex].text;

          // Mark the option as used
          newOptions[optionIndex] = {
            ...newOptions[optionIndex],
            used: true,
          };

          setOptions(newOptions);
          setBlanks(newBlanks);

          // Reset feedback when making changes
          if (hasChecked) {
            setShowFeedback(false);
            setHasChecked(false);
          }
        }
      }
    }

    setActiveId(null);
    setActiveOption(null);
  };

  // Reset a single blank and return its option to the available options
  const handleResetBlank = (index) => {
    // Get the value of the blank being reset
    const blankValue = blanks[index];

    if (blankValue) {
      // Create a new blanks array with the specified blank set to null
      const newBlanks = [...blanks];
      newBlanks[index] = null;
      setBlanks(newBlanks);

      // Find the option that matches this value and mark it as unused
      const newOptions = [...options];
      const optionIndex = newOptions.findIndex(
        (opt) => opt.text === blankValue
      );

      if (optionIndex !== -1) {
        newOptions[optionIndex] = {
          ...newOptions[optionIndex],
          used: false,
        };
        setOptions(newOptions);
      }

      // Reset feedback when making changes
      if (hasChecked) {
        setShowFeedback(false);
        setHasChecked(false);
      }
    }
  };

  // Check answers
  const handleCheckAnswers = () => {
    const currentQuestion = questions[currentQuestionIndex];

    // If no blanks are filled, just go to next question
    if (blanks.every((blank) => blank === null)) {
      handleNextQuestion();
      return;
    }

    // Check if all blanks are filled
    const allFilled = blanks.every((blank) => blank !== null);
    if (!allFilled) {
      // Show a message that all blanks need to be filled
      alert('Please fill all the blanks before checking your answer.');
      return;
    }

    // Check if answers are correct
    const isAnswerCorrect = checkAnswers(
      blanks,
      currentQuestion.correct_answers
    );
    setIsCorrect(isAnswerCorrect);
    setShowFeedback(true);
    setHasChecked(true);

    // Add to user answers
    const questionId = currentQuestion.id;

    // Update or add to user answers
    const existingAnswerIndex = userAnswers.findIndex(
      (a) => a.question_id === questionId
    );
    if (existingAnswerIndex !== -1) {
      const updatedAnswers = [...userAnswers];
      updatedAnswers[existingAnswerIndex] = {
        question_id: questionId,
        answers: [...blanks],
        is_correct: isAnswerCorrect,
      };
      setUserAnswers(updatedAnswers);
    } else {
      setUserAnswers([
        ...userAnswers,
        {
          question_id: questionId,
          answers: [...blanks],
          is_correct: isAnswerCorrect,
        },
      ]);
    }

    // Award points if correct
    if (isAnswerCorrect) {
      setScore(score + 10);
    }
  };

  // Check if answers are correct
  const checkAnswers = (filledBlanks, correctAnswers) => {
    if (!correctAnswers) return false;

    return filledBlanks.every(
      (answer, index) => answer === correctAnswers[index]
    );
  };

  // Handle next question
  const handleNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      handleSubmitAnswers();
    }
  };

  // Handle submit answers
  const handleSubmitAnswers = async () => {
    if (userAnswers.length === 0) return;

    setIsSubmitting(true);

    try {
      const response = await api.post('/play/waterfall/submit', {
        set_id: setId,
        answers: userAnswers.map((a) => ({
          question_id: a.question_id,
          answers: a.answers,
        })),
      });

      setSubmitResult(response.data);
      setGameCompleted(true);
    } catch (err) {
      setError(err.message || 'Failed to submit answers');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Restart game
  const handleRestartGame = () => {
    // If refetchGame function is provided, call it to get new questions
    if (typeof refetchGame === 'function') {
      // Refetch new game data from the API
      refetchGame();
    }

    // Reset all game state
    setCurrentQuestionIndex(0);
    setScore(0);
    setGameCompleted(false);
    setUserAnswers([]);
    setSubmitResult(null);
    setError(null);
    setShowFeedback(false);
    setIsCorrect(false);
    setHasChecked(false);
  };

  // Render the current question
  const renderCurrentQuestion = () => {
    if (!questions.length) return null;

    const currentQuestion = questions[currentQuestionIndex];
    const isLastQuestion = currentQuestionIndex === questions.length - 1;

    let gapIndex = 0;
    const correctAnswer = currentQuestion.question_text_plain.replace(
      /\[\[gap\]\]/g,
      () => {
        return currentQuestion.correct_answers[gapIndex++] || '';
      }
    );
    // Split the question text by the [[gap]] markers
    const parts = currentQuestion.question_text_plain.split(/\[\[gap\]\]/g);

    return (
      <div className="relative min-h-[calc(100vh-200px)] space-y-10 z-10">
        <div className="flex justify-between items-start">
          <div className="w-full">
            <h1 className="text-lg font-medium mb-6">
              Drag and drop blocks falling from the sky to match the words in
              the given sentence
            </h1>

            {/* Waterfall container */}
            <div className="relative min-h-[220px] mb-8 rounded-lg">
              <div id="options-container" className="w-full h-full relative">
                {options.map(
                  (option, index) =>
                    !option.used && (
                      <DraggableOption
                        key={option.id}
                        id={option.id}
                        option={option.text}
                        isActive={activeId === option.id}
                        index={index}
                        totalOptions={options.filter((o) => !o.used).length}
                      />
                    )
                )}
              </div>
            </div>

            {/* Question with blanks */}
            <div className="text-lg mb-12 flex flex-wrap gap-2 items-center">
              {parts.map((part, index) => (
                <React.Fragment key={index}>
                  {part}
                  {index < parts.length - 1 && (
                    <DroppableBlank
                      id={`blank-${index}`}
                      value={blanks[index]}
                      index={index}
                      onReset={handleResetBlank}
                      correctAnswers={currentQuestion.correct_answers}
                    />
                  )}
                </React.Fragment>
              ))}
            </div>

            {/* Check/Continue/Submit button */}
            <div className="flex justify-center mt-8">
              {/* <button
                onClick={hasChecked ? handleNextQuestion : handleCheckAnswers}
                className="bg-yellow-400 hover:bg-yellow-500 text-black px-6 py-2 rounded-full flex items-center"
                disabled={isSubmitting}
              >
                {hasChecked
                  ? 'Continue'
                  : blanks.every((b) => b === null)
                  ? 'Continue'
                  : isLastQuestion && !hasChecked
                  ? 'Check'
                  : 'Check'}
                <Icon
                  icon="material-symbols:arrow-forward-rounded"
                  className="ml-1"
                />
              </button> */}

            </div>
          </div>
        </div>

        <FeedbackComponent
          button={<Button
                onClick={hasChecked ? handleNextQuestion : handleCheckAnswers}
                disabled={isSubmitting}
                className="ml-4"
                icon={"material-symbols:arrow-forward-rounded"}
                buttonText={hasChecked
                  ? 'Continue'
                  : blanks.every((b) => b === null)
                  ? 'Continue'
                  : isLastQuestion && !hasChecked
                  ? 'Check'
                  : 'Check'}
              />}
          isCorrect={isCorrect}
          showFeedback={showFeedback}
          correctAnswer={correctAnswer}
          onContinue={handleNextQuestion}
        />
      </div>
    );
  };

  // If game is completed, show summary
  if (gameCompleted) {
    return (
      <GameSummary2
        resultData={
          submitResult?.data || {
            set_title: initialData?.set_title || 'Waterfall Game',
            total_correct_answers: userAnswers.filter((a) => a.is_correct)
              .length,
            total_questions: questions.length,
            score: Math.round((score / (questions.length * 10)) * 100),
            answers: userAnswers,
          }
        }
        onRestart={handleRestartGame}
      />
    );
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={rectIntersection}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      modifiers={[restrictToWindowEdges]}
    >
      <div className="max-w-7xl mx-auto h-full p-4">
        {renderCurrentQuestion()}

        <div className="absolute right-0 bottom-0 z-0">
          <Image
            src={'/assets/images/all-img/cat2.png'}
            alt={'cat'}
            width={400}
            height={500}
            className="max-w-[550px] h-auto"
          />
        </div>

        {/* Drag overlay */}
        <DragOverlay>
          {activeOption ? <DragOverlayContent option={activeOption} /> : null}
        </DragOverlay>
      </div>
    </DndContext>
  );
};

export default WaterfallGameMain;
