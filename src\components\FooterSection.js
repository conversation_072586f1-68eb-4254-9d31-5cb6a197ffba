import Image from 'next/image';

const FooterSection = () => {
  return (
    <div className="max-w-7xl mx-auto relative z-10 -bottom-10 pt-14">
      <Image
        src="/assets/images/all-img/introduction/footerTopBg.png"
        alt="icon"
        width={1300}
        height={380}
        className="w-full relative z-0 bottom-0 px-5 xl:px-0 max-sm:min-h-36 rounded-xl"
        priority
      />
      <Image
        src="/assets/images/all-img/footer_girl.png"
        alt="icon"
        width={500}
        height={600}
        className="absolute bottom-0 left-3 z-10 max-w-32 sm:max-w-40 md:max-w-56 lg:max-w-80 px-2 lg:px-5"
        priority
      />

      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 mt-5 z-10 space-y-1 lg:space-y-4 ml-5 lg:ml-0 text-center">
        <h2 className="text-lg sm:text-2xl lg:text-5xl text-white">
          Ready to Join <br className='max-sm:hidden' /> Hello English Coaching
        </h2>
        <div className="p-px bg-white rounded-full border border-yellow-800 text-xs lg:text-lg max-w-28 lg:max-w-40 mx-auto">
          <button className="px-4 py-1 lg:py-2 bg-[#864D0D] text-white rounded-full hover:bg-[#6A3B0D] w-full">
            Join HEC
          </button>
        </div>
      </div>
    </div>
  );
};

export default FooterSection;
