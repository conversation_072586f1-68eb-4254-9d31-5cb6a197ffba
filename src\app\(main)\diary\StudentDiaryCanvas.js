'use client';

import { useEffect, useCallback, useState, useRef } from 'react';
import { useDispatch } from 'react-redux';
import Canvas from '@/components/skin/Canvas';


const StudentDiaryCanvas = ({ data }) => {
  const [zoomLevel, setZoomLevel] = useState(1);
  const canvasWrapperRef = useRef(null);

  // Detect browser zoom level
  useEffect(() => {
    const detectZoom = () => {
      const zoom = Math.round((window.outerWidth / window.innerWidth) * 100) / 100;
      setZoomLevel(zoom);
    };

    detectZoom();
    window.addEventListener('resize', detectZoom);

    return () => window.removeEventListener('resize', detectZoom);
  }, []);

  // Calculate responsive scale based on window width and zoom level
  const getResponsiveScale = () => {
    const width = window.innerWidth;
    let baseScale = 1;

    // Base scaling for different screen sizes
    if (width <= 480) {
      baseScale = 0.35;
    } else if (width <= 768) {
      baseScale = 1.39;
    } else if (width <= 1024) {
      baseScale = 1.6;
    } else if (width <= 1200) {
      baseScale = 1.4;
    } else if (width <= 1366) {
      baseScale = 1.5;
    } else if (width <= 1600) {
      baseScale = 1.39;
    } else {
      baseScale = 1;
    }

    // Adjust for zoom level - more aggressive scaling when zoomed in
    if (zoomLevel > 1) {
      return baseScale * (1 / zoomLevel);
    }
    return baseScale;
  };

  // Rest of your component code remains the same until the return statement...

  return (
    <div className="">
      <div className="canvas-container">
        {data?.skin ? (
          <div
            ref={canvasWrapperRef}
            className="canvas-wrapper"
            style={{
              width: '100%',
              height: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              transform: `scale(${getResponsiveScale()})`,
              transformOrigin: 'center'
            }}
          >
            <Canvas />
          </div>
        ) : (
          <div className="h-[400px] overflow-auto whitespace-pre-wrap" style={{ backgroundColor: data?.backgroundColor }}>
            {data?.content}
          </div>
        )}
      </div>
    </div>
  );
};

export default StudentDiaryCanvas;