'use client';
import RegularGoBack from '@/components/shared/RegularGoBack';
import useDataFetch from '@/hooks/useDataFetch';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import React from 'react';

const TeacherProfile = () => {
  const { id } = useParams();

  const { data: profileData, isLoading } = useDataFetch({
    queryKey: ['teacher-profile'],
    endPoint: `/profiles/tutor/${id}`,
  });

  if (isLoading) {
    return (
      <div className="max-w-3xl mx-auto p-8 flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  if (!profileData) {
    return (
      <div className="max-w-3xl mx-auto p-8 text-center min-h-[60vh] flex flex-col justify-center">
        <Icon
          icon="mdi:alert-circle-outline"
          className="text-5xl text-yellow-500 mx-auto mb-4"
        />
        <h2 className="text-2xl font-semibold text-gray-700">
          Tutor profile not found
        </h2>
        <p className="text-gray-500 mt-2">
          The tutor profile you're looking for doesn't exist or is no longer
          available.
        </p>
      </div>
    );
  }

  // Calculate teaching experience (if bio contains years of experience)
  const experienceMatch = profileData?.bio?.match(/(\d+)\s*years?/i);
  const yearsOfExperience = experienceMatch ? experienceMatch[1] : null;

  // Get assigned modules (subjects they teach)
  const teachingSubjects =
    profileData?.assignedModules?.map((module) => module.name) || [];

  return (
    <div className="max-w-3xl mx-auto p-4 md:p-8">
      <RegularGoBack title={'Tutors'} className={'pb-5 max-w-28'} />

      <div className="bg-white rounded-xl shadow-lg overflow-hidden">
        {/* Card Header - Yellow Banner */}
        <div className="bg-gradient-to-r from-yellow-400 to-yellow-500 h-24"></div>

        <div className="px-6 pb-6 relative space-y-5">
          {/* Profile Picture */}
          <div className="relative h-24 w-24 rounded-full border-4 border-white bg-gray-200 overflow-hidden shadow-md flex items-center justify-center -mt-12 mb-4">
            {profileData?.profilePicture ? (
              <Image
                src={profileData?.profilePicture}
                alt={profileData?.name}
                height={300}
                width={300}
                className="object-cover"
              />
            ) : (
              <Icon
                icon="mdi:account-circle"
                className="h-full w-full text-gray-400"
              />
            )}
          </div>

          {/* Name and Status */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between ">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">
                {profileData.name}
              </h1>
              <div className="flex items-center mt-1">
                <span
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    profileData.isActive
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  <span
                    className={`w-2 h-2 rounded-full mr-1.5 ${
                      profileData.isActive ? 'bg-green-500' : 'bg-gray-500'
                    }`}
                  ></span>
                  {profileData.isActive
                    ? 'Active Tutor'
                    : 'Currently Unavailable'}
                </span>
              </div>
            </div>
          </div>

          {/* Bio Section */}
          <div className="">
            <h2 className="text-lg font-semibold text-gray-800 mb-2 flex items-center">
              <Icon
                icon="mdi:information-outline"
                className="mr-2 text-yellow-500"
              />
              About
            </h2>
            <p className="text-gray-600">
              {profileData.bio || 'No bio information available.'}
            </p>
          </div>

          {/* Teaching Information */}
          <div className="grid grid-cols-1 gap-6">
            {/* Experience */}
            <div>
              <h2 className="text-lg font-semibold text-gray-800 mb-2 flex items-center">
                <Icon
                  icon="mdi:school-outline"
                  className="mr-2 text-yellow-500"
                />
                Experience
              </h2>
              <div className="bg-gray-50 p-4 rounded-lg">
                {yearsOfExperience ? (
                  <div className="flex items-center">
                    <div className="bg-yellow-100 p-2 rounded-full mr-3">
                      <Icon
                        icon="mdi:calendar-clock"
                        className="text-xl text-yellow-600"
                      />
                    </div>
                    <div>
                      <p className="text-gray-600">
                        {yearsOfExperience} years of teaching experience
                      </p>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500">
                    Experience information not available
                  </p>
                )}
              </div>
            </div>

            {/* Subjects */}
            <div>
              <h2 className="text-lg font-semibold text-gray-800 mb-2 flex items-center">
                <Icon
                  icon="mdi:book-open-variant"
                  className="mr-2 text-yellow-500"
                />
                Teaching Subjects
              </h2>
              <div className="bg-gray-50 p-4 rounded-lg">
                {teachingSubjects.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {teachingSubjects.map((subject, index) => (
                      <span
                        key={index}
                        className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm"
                      >
                        {subject}
                      </span>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No subjects assigned yet</p>
                )}
              </div>
            </div>
          </div>

          {/* Student Count */}
          <div className="mt-6 bg-gray-50 p-4 rounded-lg flex items-center">
            <div className="bg-yellow-100 p-2 rounded-full mr-3">
              <Icon
                icon="mdi:account-group"
                className="text-xl text-yellow-600"
              />
            </div>
            <div>
              <p className="text-gray-600">
                <span className="font-semibold">
                  {profileData.studentCount || 0}
                </span>{' '}
                students currently learning with this tutor
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeacherProfile;
