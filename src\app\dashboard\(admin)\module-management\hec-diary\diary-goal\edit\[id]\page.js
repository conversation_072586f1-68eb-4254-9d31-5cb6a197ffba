'use client';
import React, { useState, useEffect } from 'react';
import { Formik, Form } from 'formik';
import FormInput from '@/components/form/FormInput';
import * as Yup from 'yup';
import { useRouter, useParams } from 'next/navigation';
import api from '@/lib/api';

const EditStage = () => {
  const router = useRouter();
  const params = useParams();
  const [loading, setLoading] = useState(true);
  const [initialData, setInitialData] = useState(null);
  const [error, setError] = useState(null);

  const validationSchema = Yup.object().shape({
    title: Yup.string().required('Title is required'),
    level: Yup.number()
      .required('Level is required')
      .min(1, 'Level must be at least 1')
      .integer('Level must be a whole number'),
    wordLimit: Yup.number()
      .required('Word limit is required')
      .min(1, 'Word limit must be at least 1')
      .integer('Word limit must be a whole number'),
    description: Yup.string().required('Description is required'),
    isActive: Yup.boolean(),
  });

  // Fetch stage data on component mount
  useEffect(() => {
    const fetchStageData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetch stage data by ID
        const response = await api.get(`/admin/diary/settings/${params.id}`);
        console.log('Fetched stage data:', response.data);
        
        setInitialData(response.data);
      } catch (error) {
        console.error('Error fetching stage data:', error);
        setError('Failed to load stage data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchStageData();
    }
  }, [params.id]);

  const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
    try {
      // Prepare data for API
      const apiData = {
        title: values.title,
        level: parseInt(values.level),
        wordLimit: parseInt(values.wordLimit),
        description: values.description,
        isActive: values.isActive,
      };

      console.log('Updating stage with data:', apiData);

      // Make API call to update stage
      const response = await api.put(`/admin/diary/settings/${params.id}`, apiData);
      console.log('Stage updated successfully:', response);

      // Redirect on success
      router.push('/dashboard/module-management/hec-diary/diary-goal');

    } catch (error) {
      console.error('Error updating stage:', error);
      
      // Handle validation errors from backend
      const validationErrors = error?.response?.data?.validationErrors;
      if (validationErrors) {
        Object.keys(validationErrors).forEach((field) => {
          setFieldError(`${field}`, validationErrors[field][0]);
        });
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  // Loading state
  if (loading) {
    return (
      <div className='min-h-screen bg-white p-5'>
        <div className="flex items-center gap-3 mb-3">
          <button
            onClick={handleBack}
            className="flex items-center justify-center w-8 h-8 rounded-full transition-colors"
            aria-label="Go back"
          >
            <svg 
              className="w-4 h-4 text-gray-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
          <h1 className="card-title text-black text-xl">Edit Stage</h1>
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <span className="ml-3 text-gray-600">Loading stage data...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className='min-h-screen bg-white p-5'>
        <div className="flex items-center gap-3 mb-3">
          <button
            onClick={handleBack}
            className="flex items-center justify-center w-8 h-8 rounded-full transition-colors"
            aria-label="Go back"
          >
            <svg 
              className="w-4 h-4 text-gray-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
          <h1 className="card-title text-black text-xl">Edit Stage</h1>
        </div>
        <div className="bg-red-100 border border-red-300 text-red-700 px-4 py-3 rounded">
          <p>{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded text-sm"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Form with data
  const initialValues = {
    title: initialData?.title || '',
    level: initialData?.level || '',
    wordLimit: initialData?.wordLimit || '',
    description: initialData?.description || '',
    isActive: initialData?.isActive ?? true,
  };

  return (
    <div className='min-h-screen bg-white p-5'>
      <div className="flex items-center gap-3 mb-3">
        <button
          onClick={handleBack}
          className="flex items-center justify-center w-8 h-8 rounded-full transition-colors"
          aria-label="Go back"
        >
          <svg 
            className="w-4 h-4 text-gray-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>
        <h1 className="card-title text-black text-xl">Edit Stage</h1>
      </div>

      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        enableReinitialize={true} // Important: allows form to update when initialData changes
      >
        {({ isSubmitting, errors, touched, resetForm, values, setFieldValue }) => (
          <Form className="mx-auto bg-gray-100 p-6 rounded-lg shadow-md">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              {/* Title Field */}
              <div className="col-span-1 md:col-span-2">
                <label htmlFor="title" className="block font-bold mb-1">
                  Stage Title *
                </label>
                <FormInput
                  type="text"
                  name="title"
                  id="title"
                  placeholder="e.g., Beginner Level"
                />
              </div>

              {/* Level Field */}
              <div>
                <label htmlFor="level" className="block font-bold mb-1">
                  Level *
                </label>
                <FormInput
                  type="number"
                  name="level"
                  id="level"
                  placeholder="e.g., 1"
                  min="1"
                />
              </div>

              {/* Word Limit Field */}
              <div>
                <label htmlFor="wordLimit" className="block font-bold mb-1">
                  Word Limit *
                </label>
                <FormInput
                  type="number"
                  name="wordLimit"
                  id="wordLimit"
                  placeholder="e.g., 100"
                  min="1"
                />
              </div>

              {/* Description Field */}
              <div className="col-span-1 md:col-span-2">
                <label htmlFor="description" className="block font-bold mb-1">
                  Description *
                </label>
                <textarea
                  name="description"
                  id="description"
                  rows="3"
                  placeholder="e.g., Settings for beginners with basic vocabulary"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  value={values.description}
                  onChange={(e) => setFieldValue('description', e.target.value)}
                />
                {errors.description && touched.description && (
                  <div className="text-red-500 text-sm mt-1">{errors.description}</div>
                )}
              </div>

              {/* Active Status Field */}
              <div className="col-span-1 md:col-span-2">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    name="isActive"
                    checked={values.isActive}
                    onChange={(e) => setFieldValue('isActive', e.target.checked)}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="font-bold">Active Stage</span>
                </label>
                <p className="text-sm text-gray-600 mt-1">
                  Check this box to make the stage available to users
                </p>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end gap-4 mt-7">
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  console.log('Cancel clicked, resetting form');
                  resetForm();
                }}
                className="bg-gray-300 hover:bg-gray-400 text-black font-medium py-2 px-4 rounded transition-colors"
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="bg-[#FFDE34] hover:bg-yellow-300 text-black font-medium py-2 px-4 rounded disabled:opacity-50 transition-colors"
              >
                {isSubmitting ? 'Updating...' : 'Update Stage'}
              </button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default EditStage;