'use client';
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import useDataFetch from '@/hooks/useDataFetch';
import { toast } from 'sonner';
import AdminProfile from './_components/AdminProfile';
import TutorProfile from './_components/TutorProfile';
import StudentProfile from './_components/StudentProfile';

const ProfileDetails = () => {
  const { user } = useSelector((state) => state.auth);
  const userId = user?.id;
  const [profileData, setProfileData] = useState(null);
  const [loading, setLoading] = useState(true);

  // Determine which API endpoint to use based on user role
  const getProfileEndpoint = () => {
    if (user?.roles?.includes('admin')) {
      return `profiles/admin/${userId}`;
    } else if (user?.roles?.includes('tutor')) {
      return `profiles/tutor/${userId}`;
    } else {
      return `profiles/student/${userId}`;
    }
  };

  // Fetch profile data
  const { data: userDetails, isLoading, error } = useDataFetch({
    queryKey: ['Profile-details', userId, user?.roles],
    endPoint: getProfileEndpoint(),
    enabled: !!userId,
  });

  useEffect(() => {
    if (userDetails) {
      setProfileData(userDetails);
      setLoading(false);
    }

    if (error) {
      toast.error('Failed to load profile data');
      setLoading(false);
    }
  }, [userDetails, error]);

  // Show loading state
  if (loading || isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-500"></div>
      </div>
    );
  }

  // Render the appropriate profile component based on user role
  const renderProfileComponent = () => {
    if (user?.roles?.includes('admin')) {
      return <AdminProfile profileData={profileData} />;
    } else if (user?.roles?.includes('tutor')) {
      return <TutorProfile profileData={profileData} />;
    } else {
      return <StudentProfile profileData={profileData} />;
    }
  };

  return renderProfileComponent();
};

export default ProfileDetails;
