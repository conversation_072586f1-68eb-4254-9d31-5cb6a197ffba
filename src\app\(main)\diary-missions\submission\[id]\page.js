'use client';
import Button, { ButtonIcon } from '@/components/Button';
import <PERSON><PERSON>iewer from '@/components/EditorViewer';
import DetailsModal from '@/components/form/modal/MissionConfirmationModal';
import GoBack from '@/components/shared/GoBack';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { Editor } from '@tinymce/tinymce-react';
import { Form, Formik } from 'formik';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';

const Submittion = () => {
  const { id } = useParams();

  const getEndPoint = `/diary/missions/entries/mission/${id}`;

  const editorRef = useRef(null);
  const updateTimeoutRef = useRef(null);
  const [value, setValue] = useState('');
  const [currentWordCount, setCurrentWordCount] = useState(0);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  const { data, refetch } = useDataFetch({
    queryKey: [`question-info`, getEndPoint],
    endPoint: getEndPoint,
  });

  const questionDetails = data?.mission;

  const showSubmission = data?.content?.length > 0 && !isSubmitted;
  const showFeedback = data?.content?.length > 0 && data?.feedback;

  // Count words in HTML content
  const countWords = (html) => {
    if (!html) return 0;
    // Remove HTML tags
    const text = html.replace(/<[^>]*>/g, ' ');
    // Remove entities
    const cleanText = text.replace(/&nbsp;|&amp;|&lt;|&gt;|&quot;|&#39;/g, ' ');
    // Remove extra spaces and split by whitespace
    const words = cleanText
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0);
    return words.length;
  };

  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      setSubmitting(true);
      const response = await api.post(
        `/diary/missions/entries/${data?.id}/submit`,
        values
      );
      console.log(response);
      setIsSubmitted(true);
    } catch (error) {
      console.log(error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdate = async (values, { setSubmitting }) => {
    try {
      // Ensure we have the answer value
      if (!values || !values.content) return;
      setSubmitting(true);
      // If it's an explicit submission (not auto-save), update immediately
      if (!values._autoSave) {
        const response = await api.put(
          `/diary/missions/entries/${data?.id}`,
          {
            content: values.content,
          },
          { showSuccessToast: false }
        );

        console.log('Saved:', response);
        refetch();
        setIsSubmitted(false);
        return;
      }

      // For auto-save, use debouncing
      try {
        const response = await api.put(
          updateEndPoint,
          {
            content: values.answer,
          },
          { showSuccessToast: false }
        );

        console.log('Auto-saved (debounced):', response);
      } catch (error) {
        console.error('Error auto-saving submission:', error);
      }
    } catch (error) {
      console.error('Error updating submission:', error);
    } finally {
      setSubmitting(false);
    }
  };

  // Initialize word count when component mounts or when questionDetails changes
  useEffect(() => {
    if (questionDetails?.content) {
      const initialCount = countWords(questionDetails.content);
      setCurrentWordCount(initialCount);
      setValue(questionDetails.content);
    }
  }, [questionDetails?.content]);

  return (
    <div className="relative">
      <div className="max-w-7xl mx-auto px-5 xl:px-0 relative z-10">
        <GoBack title={'HEC Play'} linkClass="my-5 mb-8 w-full max-w-40" />

        <div className="p-5 rounded-lg bg-[#FFF9FB] shadow-lg space-y-5 mb-10">
          <div className="p-5 bg-[#FCF8EF] rounded-lg [box-shadow:2px_2px_12px_0px_#F5D1B066_inset,_-2px_-2px_12px_0px_#F5D1B066_inset]">
            <h1 className="text-2xl text-yellow-800 font-semibold">
              {questionDetails?.title}
            </h1>
            <div className="flex items-start gap-6">
              {questionDetails?.picture && (
                <Image
                  src={questionDetails?.picture}
                  alt={questionDetails?.title}
                  width={200}
                  height={200}
                />
              )}
              <div>
                {(questionDetails?.instructions ||
                  questionDetails?.description) && (
                  <p className="font-semibold text-lg text-gray-700">
                    Instruction:
                  </p>
                )}
              </div>
            </div>
            <EditorViewer
              data={
                questionDetails?.instructions || questionDetails?.description
              }
            />
          </div>

          {showSubmission ? (
            <div className="space-y-3">
              <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                <h1 className="text-xl text-yellow-800 font-semibold">
                  My Submission
                </h1>
                <EditorViewer
                  data={
                    data?.content?.length > 200
                      ? data?.content?.slice(0, 400) + '...'
                      : data?.content
                  }
                />
                <div className="absolute right-2 top-2">
                  <ButtonIcon
                    icon={'ri:edit-2-fill'}
                    innerBtnCls={'h-10 w-10'}
                    btnIconCls={'h-5 w-5'}
                    onClick={() => setIsSubmitted(true)}
                  />
                </div>
              </div>

              <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                <h1 className="text-xl text-yellow-800 font-semibold">
                  Tutor Correction Zone
                </h1>
                {showFeedback && (
                  <>
                    {data?.feedbacks?.length > 0 && (
                      <div className="rounded-md">
                        <ul className=" text-sm text-gray-800">
                          {data?.feedbacks.map((item, index) => (
                            <EditorViewer key={index} data={item} />
                          ))}
                        </ul>
                      </div>
                    )}

                    <div className="absolute right-2 top-2">
                      <ButtonIcon
                        icon={'arcticons:feedback-2'}
                        innerBtnCls={'h-10 w-10'}
                        btnIconCls={'h-4 w-4'}
                        onClick={() => setShowDetailsModal(true)}
                      />
                    </div>
                  </>
                )}

                <p
                  className={`${
                    !(data?.feedbacks?.length > 0) &&
                    'text-red-600'
                  } text-center mt-2`}
                >
                  {!(data?.feedbacks?.length > 0) &&
                    'Not Confirmed yet'}
                </p>
              </div>
            </div>
          ) : (
            <Formik
              initialValues={{
                content: value || questionDetails?.submission?.answer || '',
              }}
              onSubmit={(values, formikHelpers) => {
                if (currentWordCount >= questionDetails?.targetWordCount) {
                  // If word count meets or exceeds target, submit the answer
                  handleSubmit(values, formikHelpers);
                } else {
                  // If word count is less than target, just update
                  handleUpdate(values, formikHelpers);
                }
              }}
              enableReinitialize
            >
              {({ isSubmitting }) => (
                <Form>
                  <div className="space-y-2">
                    <div className="relative">
                      <Editor
                        name="answer"
                        apiKey={
                          process.env.NEXT_TINYMCE_TOKEN ||
                          'w64sqy7kf7wf2qoy3qqfmyatf85cys00il0jcezjers4pl9o'
                        }
                        onInit={(_evt, editor) => (editorRef.current = editor)}
                        initialValue={
                          data?.content || '<p></p>'
                        }
                        onEditorChange={(content) => {
                          setValue(content);
                          setCurrentWordCount(countWords(content));
                        }}
                        init={{
                          height: 500,
                          menubar: false,
                          plugins: [],
                          toolbar:
                            'undo redo | blocks | bold italic forecolor | ' +
                            'alignleft aligncenter alignright alignjustify | ' +
                            'bullist numlist outdent indent | removeformat',
                          content_style:
                            'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
                        }}
                      />

                      {/* Word count display */}
                      <div className="absolute left-2 w-[98.5%] z-20 flex justify-between items-center mt-2 text-sm">
                        <div className="font-medium text-gray-600">
                          {questionDetails?.targetWordCount && (
                            <span>
                              {currentWordCount} /{' '}
                              {questionDetails?.targetWordCount} words
                            </span>
                          )}
                        </div>
                      </div>

                      <div className="h-6 rounded-b-lg w-[98.5%] bg-white absolute left-1 bottom-1 z-10"></div>
                    </div>

                    {questionDetails?.targetWordCount && (
                      <div className="flex justify-end">
                        <div
                          className={`text-sm px-3 py-1 rounded-full ${
                            currentWordCount >= questionDetails?.targetWordCount
                              ? 'bg-green-100 text-green-700'
                              : 'bg-yellow-100 text-yellow-700'
                          }`}
                        >
                          {currentWordCount >= questionDetails?.targetWordCount
                            ? 'Target word count reached! You can submit your answer.'
                            : `Need ${
                                questionDetails?.targetWordCount -
                                currentWordCount
                              } more words to reach target count.`}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-center mt-3 gap-3">
                    <Button
                      buttonText="Cancel"
                      type="button"
                      onClick={() => setIsSubmitted(false)}
                    />
                    <Button
                      disabled={isSubmitting}
                      buttonText={
                        (currentWordCount >= questionDetails?.targetWordCount && !data?.status === 'NEW')
                          ? isSubmitting
                            ? 'Submitting...'
                            : 'Submit'
                          : isSubmitting
                          ? 'Updating...'
                          : 'Update'
                      }
                      type="submit"
                      className="bg-yellow-400 hover:bg-yellow-500 text-black"
                    />
                  </div>
                </Form>
              )}
            </Formik>
          )}
        </div>
      </div>

      <DetailsModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        data={questionDetails?.submission?.corrections?.grammar}
        title="Teacher's Feedback"
        link={`/answer`}
        showBtn={false}
      />
    </div>
  );
};

export default Submittion;
