'use client';
import BasicTablePage from '@/components/form/BasicTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import { Icon } from '@iconify/react';

const PlanFeatures = () => {
  const router = useRouter();
  const [page, setPage] = useState(1);
  const [limit] = useState(10);

  const {
    data,
    isLoading,
    refetch,
  } = useDataFetch({
    queryKey: ['plan-features', page, limit],
    endPoint: '/plan-features',
    params: { page, limit }
  });

  // The API returns features directly in the data property
  const features = data || [];

  const tableData = features?.map((item, index) => {
    return {
      title: item?.name,
      createdBy: (
        <div className="flex items-center gap-2">
          <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-xs">
            {item?.createdBy?.charAt(0) || 'A'}
          </div>
          <span>{item?.createdBy || 'Admin'}</span>
        </div>
      ),
      createdDate: item?.createdAt ? new Date(item?.createdAt).toLocaleDateString() : '20th December, 2023',
      status: (
        <span className={`text-${item.isActive ? 'green' : 'red'}-600 px-3 py-1 rounded-full text-xs font-medium ${item.isActive ? 'bg-green-100' : 'bg-red-100'}`}>
          {item.isActive ? 'Active' : 'Inactive'}
        </span>
      ),
    };
  });

  const columns = [
    {
      label: 'Feature Title',
      field: 'title',
    },
    {
      label: 'Created By',
      field: 'createdBy',
    },
    {
      label: 'Creation Date',
      field: 'createdDate',
    },
    {
      label: 'ACTIVE STATUS',
      field: 'status',
    },
    {
      label: 'ACTION',
      field: '',
    },
  ];

  const actions = [
    {
      name: 'edit',
      icon: 'material-symbols:edit-outline',
      className: 'text-gray-600',
      onClick: (val) => {
        router.push(`/dashboard/plan-features/edit/${features[val]?.id}`);
      },
    },
    {
      name: 'delete',
      icon: 'heroicons-outline:trash',
      className: 'text-red-600',
      onClick: (val) => {
        handleDelete(features[val]?.id);
      },
    },
  ];

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this feature?')) {
      try {
        await api.delete(`/plan-features/${id}`);
        refetch();
      } catch (error) {
        console.log(error);
        alert('Failed to delete feature. It might be in use by one or more plans.');
      }
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-xl font-semibold">Feature List</h1>
        <button
          onClick={() => router.push('/dashboard/plan-features/add')}
          className="bg-yellow-400 hover:bg-yellow-500 text-black px-4 py-2 rounded-md flex items-center gap-2"
        >
          Add Feature
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="p-4 border-b">
          <div className="relative">
            <input
              type="text"
              placeholder="Search for feature name..."
              className="w-full pl-10 pr-4 py-2 border rounded-md"
            />
            <Icon icon="material-symbols:search" className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column, index) => (
                  <th key={index} className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {column.label}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {isLoading ? (
                <tr>
                  <td colSpan={columns.length} className="px-6 py-4 text-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-yellow-500 mx-auto"></div>
                  </td>
                </tr>
              ) : tableData.length > 0 ? (
                tableData.map((row, rowIndex) => (
                  <tr key={rowIndex} className="hover:bg-gray-50">
                    {columns.map((column, colIndex) => (
                      <td key={colIndex} className="px-6 py-4 whitespace-nowrap">
                        {column.field ? row[column.field] : (
                          <div className="flex space-x-2">
                            {actions.map((action, actionIndex) => (
                              <button
                                key={actionIndex}
                                onClick={() => action.onClick(rowIndex)}
                                className={`p-1 rounded-full ${action.className}`}
                              >
                                <Icon icon={action.icon} className="w-5 h-5" />
                              </button>
                            ))}
                          </div>
                        )}
                      </td>
                    ))}
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={columns.length} className="px-6 py-4 text-center text-gray-500">
                    No features found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Handle pagination for both response formats */}
        {(data?.totalCount > limit || (data?.data && data?.data.length >= limit)) && (
          <div className="px-6 py-3 flex items-center justify-between border-t">
            <div className="text-sm text-gray-500">
              Showing {((page - 1) * limit) + 1} to {Math.min(page * limit, data?.totalCount || features.length)} of {data?.totalCount || 'all'} results
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setPage(Math.max(1, page - 1))}
                disabled={page === 1}
                className="px-3 py-1 border rounded-md disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => setPage(page + 1)}
                disabled={(data?.totalCount && page * limit >= data.totalCount) || (data?.data && data.data.length < limit)}
                className="px-3 py-1 border rounded-md disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PlanFeatures;
