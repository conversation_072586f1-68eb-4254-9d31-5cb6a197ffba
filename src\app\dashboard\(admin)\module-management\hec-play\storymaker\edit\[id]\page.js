'use client';
import React, { useRef, useState } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useParams, useRouter } from 'next/navigation';
import { toast } from 'sonner';
import api from '@/lib/api';
import FormInput from '@/components/form/FormInput';
import NumberInput from '@/components/form/NumberInput';
import FormSelect from '@/components/form/FormSelect';
import RegularGoBack from '@/components/shared/RegularGoBack';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import useDataFetch from '@/hooks/useDataFetch';
import { Editor } from '@tinymce/tinymce-react';

const validationSchema = Yup.object({
  title: Yup.string().required('Story title is required'),
  instruction: Yup.string().required('Instruction is required'),
  score: Yup.number()
    .required('Total score is required')
    .min(1, 'Score must be at least 1'),
  word_limit: Yup.number().min(1, 'Word limit must be at least 1'),
  deadline: Yup.number().min(1, 'Deadline must be at least 1 day'),
});

const EditStory = () => {
  const { id } = useParams();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imagePreview, setImagePreview] = useState(null);
  const [instructionValue, setInstructionValue] = useState('');
  const editorRef = useRef(null);

  const { data, isLoading } = useDataFetch({
    queryKey: ['story-details', id],
    endPoint: `/play/story-maker/admin/stories/${id}`,
  });

  const initialValues = {
    title: data?.title || '',
    instruction: data?.instruction || '',
    picture: null,
    score: data?.score || '',
    word_limit: data?.word_limit || '',
    deadline: data?.deadline || '',
  };

  const handleImageChange = (e, setFieldValue) => {
    const file = e.target.files[0];
    if (file) {
      setFieldValue('picture', file);

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = (setFieldValue) => {
    setFieldValue('picture', null);
    setImagePreview(null);
  };

  const handleSubmit = async (values) => {
    setIsSubmitting(true);
    try {
      const formData = new FormData();
      formData.append('title', values.title);
      formData.append('instruction', values.instruction);
      formData.append('score', values.score);

      if (values.word_limit) {
        formData.append('word_limit', values.word_limit);
      }

      if (values.deadline) {
        formData.append('deadline', values.deadline);
      }

      // Only append picture if a new one is selected
      if (values.picture) {
        formData.append('picture', values.picture);
      }

      await api.patch(`/play/story-maker/admin/stories/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      router.push('/dashboard/module-management/hec-play/storymaker');
    } catch (error) {
      console.error('Error updating story maker:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="mb-6">
        <RegularGoBack title="Edit Question Set" />
      </div>

      <div className="bg-gray-50 rounded-lg shadow p-6">
        <div className="mb-6">
          <p className="text-gray-600 text-sm">
            Fill up the required <span className="text-red-500">*</span> fields
            below to update the question set
          </p>
        </div>

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ setFieldValue, values }) => (
            <Form className="space-y-6">
              <div className="">
                <label className="block text-sm font-medium mb-2">
                  Question Image{' '}
                  {!data?.picture && <span className="text-red-500">*</span>}
                </label>
                <div className="border-2 border-dashed border-gray-200 rounded-lg p-4">
                  {/* Hidden file input */}
                  <input
                    type="file"
                    name="picture"
                    accept="image/png,image/jpeg,image/jpg"
                    onChange={(e) => handleImageChange(e, setFieldValue)}
                    className="hidden"
                    id="fileInput"
                  />

                  {/* Preview Area */}
                  <div
                    className={`border-2 ${
                      !values.picture ? 'border-gray-200' : 'border-gray-200'
                    } rounded-lg p-4 min-h-40 flex items-center justify-center text-center bg-[#FFFCF5]`}
                  >
                    {!imagePreview ? (
                      <div>
                        {data?.picture ? (
                          <div className="relative">
                            <Image
                              src={data.picture}
                              alt={data.title}
                              width={200}
                              height={150}
                              className="max-h-40 mx-auto"
                            />
                            <div
                              className="mt-3 text-center cursor-pointer text-blue-600 hover:text-blue-800"
                              onClick={() =>
                                document.getElementById('fileInput').click()
                              }
                            >
                              Click to change image
                            </div>
                          </div>
                        ) : (
                          <div
                            className="flex flex-col items-center cursor-pointer"
                            onClick={() =>
                              document.getElementById('fileInput').click()
                            }
                          >
                            <Icon
                              icon="solar:gallery-wide-linear"
                              className="w-12 h-12 text-gray-400 mb-2"
                            />
                            <div>
                              <span className="text-yellow-500">
                                Upload a file
                              </span>
                              <span> or drag and drop</span>
                            </div>
                            <p className="text-sm text-gray-500 mt-1">
                              PNG, JPEG up to 5MB
                            </p>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="relative">
                        <Image
                          src={imagePreview}
                          alt="Preview"
                          width={200}
                          height={150}
                          className="mx-auto"
                        />
                      </div>
                    )}
                  </div>

                  {/* Image Actions */}
                  {imagePreview && (
                    <div className="flex items-center justify-between gap-2 mt-2">
                      <span className="text-sm text-gray-500">
                        File type: PNG, JPEG
                      </span>
                      <div className="flex gap-2">
                        <button
                          type="button"
                          className="px-3 py-1 text-sm bg-yellow-400 rounded-md flex items-center gap-1"
                          onClick={() =>
                            document.getElementById('fileInput').click()
                          }
                        >
                          <Icon
                            icon="garden:reload-stroke-12"
                            className="w-4 h-4"
                          />
                          Change
                        </button>
                        <button
                          type="button"
                          className="px-3 py-1 text-sm bg-red-100 text-red-600 rounded-md flex items-center gap-1"
                          onClick={() => removeImage(setFieldValue)}
                        >
                          <Icon
                            icon="solar:trash-bin-trash-linear"
                            className="w-4 h-4"
                          />
                          Remove
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <FormInput
                    label="Story Title"
                    name="title"
                    placeholder="Story Maker Question Set Title"
                    required
                  />
                </div>

                <div>
                  <NumberInput
                    label="Total Score"
                    name="score"
                    min={1}
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <NumberInput label="Word Limit" name="word_limit" min={1} />
                </div>
                <div>
                  <FormSelect
                    label="Deadline"
                    name="deadline"
                    required
                    options={[
                      { value: 1, label: '1 day' },
                      { value: 2, label: '2 days' },
                      { value: 3, label: '3 days' },
                      { value: 5, label: '5 days' },
                      { value: 7, label: '7 days' },
                      { value: 14, label: '14 days' },
                      { value: 30, label: '30 days' },
                    ]}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm md:text-base font-medium mb-2">
                  Instruction <span className="text-red-500">*</span>
                </label>
                <Editor
                  apiKey={
                    process.env.NEXT_TINYMCE_TOKEN ||
                    'w64sqy7kf7wf2qoy3qqfmyatf85cys00il0jcezjers4pl9o'
                  }
                  onInit={(_evt, editor) => (editorRef.current = editor)}
                  initialValue={ data?.instruction || '<p></p>'}
                  onEditorChange={(content) => {
                    setFieldValue('instruction', content);
                    setInstructionValue(content);
                  }}
                  init={{
                    height: 300,
                    menubar: false,
                    plugins: [],
                    toolbar:
                      'undo redo | blocks | bold italic forecolor | ' +
                      'alignleft aligncenter alignright alignjustify | ' +
                      'bullist numlist outdent indent | removeformat',
                    content_style:
                      'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
                  }}
                />
              </div>

              <div className="flex justify-end mt-8">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-yellow-400 hover:bg-yellow-500 text-yellow-900 font-medium py-2 px-8 rounded-lg transition-colors duration-200"
                >
                  {isSubmitting ? 'Updating...' : 'Update Story'}
                </button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default EditStory;
