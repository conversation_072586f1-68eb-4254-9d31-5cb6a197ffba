'use client';
import { useRef, useEffect } from 'react';
import Image from 'next/image';

/**
 * DiaryFeedbackModal Component
 * 
 * A modal component for displaying teacher feedback for a student diary entry.
 * Styled to match the wooden sign design from Figma.
 * 
 * @param {Object} props
 * @param {Object} props.diary - The diary object containing feedback details
 * @param {Function} props.onClose - Function to call when closing the modal
 * @returns {JSX.Element|null} The DiaryFeedbackModal component or null if not shown
 */
const DiaryFeedbackModal = ({ diary, onClose }) => {
  const modalRef = useRef(null);

  useEffect(() => {
    // Handle escape key press
    const handleEscapeKey = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    // Handle click outside
    const handleClickOutside = (e) => {
      if (modalRef.current && !modalRef.current.contains(e.target)) {
        onClose();
      }
    };

    // Add event listeners
    document.addEventListener('keydown', handleEscapeKey);
    document.addEventListener('mousedown', handleClickOutside);
    
    // Prevent body scrolling when modal is open
    document.body.style.overflow = 'hidden';

    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.removeEventListener('mousedown', handleClickOutside);
      // Restore body scrolling when modal is closed
      document.body.style.overflow = 'auto';
    };
  }, [onClose]);

  if (!diary) return null;

  // Format the feedback points from the feedbacks array - accessing .feedback property
  const feedbackPoints = diary.feedbacks && Array.isArray(diary.feedbacks) 
    ? diary.feedbacks.map(feedback => feedback.feedback || feedback.comment || feedback.content || feedback.text)
    : [];

  // Get diary score
  const score = diary.score || 'Not graded';

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div 
        ref={modalRef}
        className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden relative"
      >
        {/* Close button - Round orange/amber with X */}
        <div className="absolute top-4 right-4 z-10">
          <button
            type="button"
            onClick={onClose}
            className="focus:outline-none"
            aria-label="Close"
          >
            <Image 
              src="/assets/images/all-img/cross-bg.png" 
              alt="Close" 
              width={40} 
              height={40} 
              className="w-10 h-10" 
            />
          </button>
        </div>
        
        {/* Wooden Sign Header */}
        <div className="bg-[#FFF9FB] pt-6 pb-8 px-6 border-b border-amber-200">
          <div className="flex justify-center mb-2">
            <div className="relative w-full max-w-md">
              {/* Wooden sign background with text overlay */}
              <Image
                src="/assets/images/all-img/teacheressay-bg.png"
                alt="Diary Feedback"
                width={600}
                height={200}
                className="w-full h-auto"
                priority
              />
            </div>
          </div>
        </div>
        
        {/* Diary Info Section */}
        <div className="px-8 py-4 bg-gray-50 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-lg font-semibold text-gray-800">
                {diary.title || 'Diary Entry'}
              </h2>
              <p className="text-sm text-gray-600">
                By: {diary?.diary?.user?.name || 'Unknown Student'}
              </p>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-600">Score</div>
              <div className="text-2xl font-bold text-amber-600">{score}</div>
            </div>
          </div>
        </div>
        
        {/* Feedback Content */}
        <div className="px-8 py-6">
          <h3 className="text-2xl font-semibold mb-4 text-gray-800">Teacher's Feedback</h3>
          
          {feedbackPoints.length > 0 ? (
            <ol className="list-decimal pl-6 space-y-4">
              {feedbackPoints.map((point, index) => (
                <li key={index} className="text-gray-700">{point}</li>
              ))}
            </ol>
          ) : (
            <div className="text-center py-8">
              <div className="text-gray-400 mb-2">
                <svg className="w-16 h-16 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                </svg>
              </div>
              <p className="text-gray-500 italic">No feedback provided yet.</p>
              {diary.status === 'reviewed' && (
                <p className="text-sm text-gray-400 mt-2">
                  This diary has been reviewed but no specific feedback comments were added.
                </p>
              )}
            </div>
          )}
        </div>
        
        {/* Divider */}
        <div className="border-t border-gray-200 mx-8"></div>
        
        {/* Footer with action buttons */}
        <div className="flex justify-end p-4 bg-gray-50">
          <button
            type="button"
            className="bg-amber-600 text-white px-4 py-2 rounded-md hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-amber-500"
            onClick={onClose}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default DiaryFeedbackModal;