'use client';
import FormInput from '@/components/form/FormInput';
import api from '@/lib/api';
import { Form, Formik } from 'formik';
import React, { useState } from 'react';
import * as Yup from 'yup';

const ForgotUserId = () => {
  const [message, setMessage] = useState('');
  return (
    <div className="w-full max-w-2xl mx-auto p-4 py-16 lg:py-0">
      <h1 className="text-3xl font-semibold text-gray-900 text-center mb-5">
        HELLO ENGLISH COACHING - HEC
      </h1>
      <div className=" text-center space-y-2 mb-5">
        <h1 className="text-xl font-semibold text-gray-900">Verify Email</h1>

        <p className={message?.length > 0 ? 'text-green-500' : 'text-gray-600'}>
          {message?.length > 0
            ? message
            : 'Enter the email address to find your userId.'}
        </p>
      </div>
      { !message?.length > 0 && <Formik
        validationSchema={Yup.object().shape({
          email: Yup.string().required('Please provide an email.'),
        })}
        initialValues={{
          email: '',
        }}
        onSubmit={async (values, { setSubmitting, setErrors, resetForm }) => {
          try {
            setSubmitting(true);

            const response = await api.post('/auth/forgot-userid', values);
            if (response?.success) {
              resetForm();
              setMessage(response?.message);
            }
          } catch (error) {
            console.log(error);
          }
          setSubmitting(false);
        }}
      >
        {({ isSubmitting, values }) => (
          <Form className="max-w-md mx-auto space-y-4">
            <FormInput
              label="Email Address"
              name="email"
              type="email"
              placeholder="Enter email address"
              required
            />

            <button
              type="submit"
              disabled={isSubmitting || !values.email}
              className={`w-full py-2 px-4 rounded-md transition-colors ${
                isSubmitting || !values.email
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-yellow-300 text-gray-800 hover:bg-yellow-400'
              }`}
            >
              {isSubmitting ? 'Sending...' : 'Send Email'}
            </button>
          </Form>
        )}
      </Formik>}
    </div>
  );
};

export default ForgotUserId;
