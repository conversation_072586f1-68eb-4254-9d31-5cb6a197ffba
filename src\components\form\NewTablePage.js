'use client';
import { useDispatch } from 'react-redux';
import { Icon } from '@iconify/react/dist/iconify.js';
import { setShowModal } from '@/store/features/crudSlice';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

const NewTablePage = ({
  tableHeaderExtra = null,
  title = 'Table',
  createButton = '',
  createBtnLink,
  createPage,
  editPage,
  actions = [],
  columns = [],
  changePage,
  data = [],
  filter = true,
  setFilter,
  currentPage = 1,
  totalPages = 1,
  submitForm,
  loading = false,
  showCreateButton = true,
  setShowBulkModal,
  totalItems = 0,
  rowsPerPage = 10,
  setRowsPerPage,
  // UI control props
  showSearch = true,
  showNameFilter = true,
  showSortFilter = true,
  // Checkbox control prop
  showCheckboxes = true,
  // Modal override
  openCreateModal: customOpenCreateModal = null,
  // New props for controlled components
  onSearch = null,
  onNameFilterChange = null,
  onSort = null,
  nameFilterOptions = [],
  searchTerm = '',
  searchField = '',
  sortField = '',
  sortDirection = 'asc',
  // New prop for back button functionality
  onBack = null,
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const [selectedRows, setSelectedRows] = useState([]);
  const [selectAll, setSelectAll] = useState(false);

  // Local state for search input - reflects controlled prop
  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm);

  // State for dropdown visibility
  const [isNameDropdownOpen, setIsNameDropdownOpen] = useState(false);
  const [isSortDropdownOpen, setIsSortDropdownOpen] = useState(false);

  // Update local search term when prop changes
  useEffect(() => {
    setLocalSearchTerm(searchTerm);
  }, [searchTerm]);

  // Modified to use custom handler if provided
  const handleCreateButton = () => {
    if (customOpenCreateModal) {
      // Use the custom function if provided
      customOpenCreateModal();
    } else if (createBtnLink) {
      // Navigate if link provided
      router.push(createBtnLink);
    } else {
      // Default behavior: open modal using Redux
      dispatch(setShowModal(true));
    }
  };

  const toggleSelectAll = () => {
    if (selectAll) {
      setSelectedRows([]);
    } else {
      setSelectedRows(data.map((_, index) => index));
    }
    setSelectAll(!selectAll);
  };

  const toggleSelectRow = (index) => {
    if (selectedRows.includes(index)) {
      setSelectedRows(selectedRows.filter((i) => i !== index));
    } else {
      setSelectedRows([...selectedRows, index]);
    }
  };

  // Handle search input changes
  const handleSearchInputChange = (e) => {
    const value = e.target.value;
    setLocalSearchTerm(value);

    // Debounce could be implemented here, but for simplicity we're passing directly
    if (onSearch) {
      onSearch(value);
    }
  };

  // Handle name filter changes
  const handleNameFilterSelect = (field) => {
    if (onNameFilterChange) {
      onNameFilterChange(field);
    }
    setIsNameDropdownOpen(false);
  };

  // Handle sorting
  const handleSortChange = (field) => {
    if (onSort) {
      onSort(field);
    }
    setIsSortDropdownOpen(false);
  };

  // Get display name for the current search field
  const getCurrentSearchFieldName = () => {
    if (!nameFilterOptions.length) return 'Name';

    const option = nameFilterOptions.find((opt) => opt.value === searchField);
    return option ? option.label : 'Name';
  };

  // Calculate actual number of pages needed based on data length
  const actualTotalPages = Math.ceil(totalItems / rowsPerPage) || 1;

  // Function to render cell content based on column configuration
  const renderCellContent = (row, column) => {
    const value = row[column.field];

    // If the column has a custom cell renderer, use it
    if (column.cellRenderer) {
      return column.cellRenderer(value, row);
    }

    // Handle avatar display for name fields
    if (column.field === 'name' && row.avatar) {
      return (
        <div className="flex items-center">
          <div className="h-8 w-8 rounded-full overflow-hidden mr-3 bg-gray-200">
            <img
              src={row.avatar || '/api/placeholder/32/32'}
              alt={value}
              className="h-full w-full object-cover"
            />
          </div>
          <div className="font-medium text-gray-900">{value}</div>
        </div>
      );
    }

    // Return the raw value for normal cells
    return value;
  };

  return (
    <div className="bg-white rounded-lg lg:overflow-y-visible overflow-y-auto">
      {/* Title Section with Back Arrow - title on left side only */}
      {(showNameFilter || showSearch || showSortFilter) && (
        <div className="flex items-center mb-4">
          {onBack && (
            <button
              onClick={onBack}
              className="mr-2 hover:bg-gray-100 p-1 rounded-full transition-colors"
            >
              <Icon icon="mdi:arrow-left" width="20" height="20" />
            </button>
          )}
          <h4 className="card-title text-black text-xl">{title}</h4>
        </div>
      )}

      {/* Search and Filters Section - below title */}
      <div className="flex flex-wrap justify-between mb-4">
        <div className="flex flex-col">
          {!showNameFilter && !showSearch && !showSortFilter && (
            <div className="flex items-center mb-4">
              {onBack && (
                <button
                  onClick={onBack}
                  className="mr-2 hover:bg-gray-100 p-1 rounded-full transition-colors"
                >
                  <Icon icon="mdi:arrow-left" width="20" height="20" />
                </button>
              )}
              <h4 className="card-title text-black text-xl">{title}</h4>
            </div>
          )}

          <div className="flex items-center space-x-2 flex-wrap">
            {/* Name dropdown - conditionally rendered */}
            {showNameFilter && (
              <div className="relative">
                <button
                  className="border rounded-md py-1.5 px-3 flex items-center gap-1 bg-white"
                  onClick={() => setIsNameDropdownOpen(!isNameDropdownOpen)}
                >
                  <span className="text-sm font-medium">
                    {getCurrentSearchFieldName()}
                  </span>
                  <Icon icon="mdi:chevron-down" width="16" height="16" />
                </button>
                {isNameDropdownOpen && (
                  <div className="absolute mt-1 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                    {nameFilterOptions.map((option, index) => (
                      <div
                        key={index}
                        className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
                        onClick={() => handleNameFilterSelect(option.value)}
                      >
                        {option.label}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Search box - conditionally rendered */}
            {showSearch && (
              <div className="relative">
                <input
                  type="text"
                  placeholder={`Search by ${getCurrentSearchFieldName()}`}
                  className="border rounded-md py-1.5 pl-9 pr-4 w-64 focus:outline-none"
                  value={localSearchTerm}
                  onChange={handleSearchInputChange}
                />
                <div className="absolute left-3 top-2 text-gray-400">
                  <Icon icon="mdi:magnify" width="18" height="18" />
                </div>
              </div>
            )}

            {/* Sort by dropdown - conditionally rendered */}
            {showSortFilter && (
              <div className="relative">
                <button
                  className="border rounded-md py-1.5 px-3 flex items-center gap-1 bg-white"
                  onClick={() => setIsSortDropdownOpen(!isSortDropdownOpen)}
                >
                  <span className="text-sm font-medium">Sort by</span>
                  <Icon icon="mdi:chevron-down" width="16" height="16" />
                </button>
                {isSortDropdownOpen && (
                  <div className="absolute mt-1 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                    {columns.map((column, index) => (
                      <div
                        key={index}
                        className="px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
                        onClick={() => handleSortChange(column.field)}
                      >
                        {column.label}{' '}
                        {sortField === column.field &&
                          (sortDirection.toUpperCase() === 'ASC' ? '↑' : '↓')}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {tableHeaderExtra}
          </div>
        </div>

        {/* Add user button - right side */}
        {showCreateButton && createButton && (
          <div>
            <button
              className="btn bg-yellow-300 hover:bg-yellow-400 px-3 py-1.5 rounded"
              onClick={handleCreateButton}
            >
              {createButton}
            </button>
          </div>
        )}
      </div>

      {/* Table section */}
      <div className="w-full">
        {loading ? (
          <h2 className="text-gray-600 text-start">Loading...</h2>
        ) : data.length > 0 ? (
          <table className="min-w-full divide-y divide-slate-100 table-fixed shadow-md rounded-lg overflow-x-auto">
            <thead className="bg-gray-100 rounded-t-lg">
              <tr className="border-gray-300">
                {showCheckboxes && (
                  <th className="table-th py-3 font-[500] text-md text-left pl-3">
                    <div className="">
                      <input
                        type="checkbox"
                        className="h-4 w-4 rounded border-gray-300"
                        checked={selectAll}
                        onChange={toggleSelectAll}
                      />
                    </div>
                  </th>
                )}
                <th className="table-th py-3 font-[500] text-md text-left pl-3">
                  #
                </th>
                {columns.map((column, index) => (
                  <th
                    key={index}
                    className="table-th py-3 font-[500] text-md text-left pl-3"
                  >
                    <div className="flex items-center">
                      {column.label}
                      {column.sortable && (
                        <Icon
                          icon={
                            sortField === column.field
                              ? sortDirection.toUpperCase() === 'ASC'
                                ? 'mdi:sort-ascending'
                                : 'mdi:sort-descending'
                              : 'mdi:unfold-more-horizontal'
                          }
                          className="ml-1 cursor-pointer"
                          onClick={() => handleSortChange(column.field)}
                          width="16"
                          height="16"
                        />
                      )}
                    </div>
                  </th>
                ))}
                {actions.length > 0 && (
                  <th className="table-th py-3 font-[500] text-md text-left pl-3">
                    ACTION
                  </th>
                )}
              </tr>
            </thead>
            <tbody className="w-full divide-y divide-slate-100 text-black">
              {data.map((row, index) => (
                <tr key={index} className="text-sm">
                  {showCheckboxes && (
                    <td className="table-td py-4 w-full md:w-auto px-3 text-left">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300"
                          checked={selectedRows.includes(index)}
                          onChange={() => toggleSelectRow(index)}
                        />
                      </div>
                    </td>
                  )}
                  <td className="table-td py-4 w-full md:w-auto px-3 text-left">
                    {index + 1 + (currentPage - 1) * rowsPerPage}
                  </td>
                  {columns.map((column, colIndex) => (
                    <td
                      key={colIndex}
                      className="table-td py-4 w-full md:w-auto px-3 text-left"
                    >
                      {renderCellContent(row, column)}
                    </td>
                  ))}
                  {actions.length > 0 && (
                    <td className="table-td py-4 w-full md:w-auto px-3 text-left">
                      <div className="divide-y divide-slate-100 flex items-center gap-2">
                        {actions.map((action, actionIndex) => {
                          // Determine if the action should be enabled based on a 'condition' property
                          const shouldEnable =
                            !action.condition || action.condition(row);
                          const buttonClassName =
                            typeof action.className === 'function'
                              ? action.className(row)
                              : action.className || '';
                          const combinedClassName = `text-sm  bg-white border shadow p-1.5 rounded-md ${buttonClassName} ${
                            shouldEnable
                              ? 'hover:bg-gray-100'
                              : 'opacity-50 cursor-not-allowed'
                          }`;

                          return (
                            <div
                              key={actionIndex}
                              className={combinedClassName}
                              onClick={() =>
                                shouldEnable &&
                                action.onClick &&
                                action.onClick(row)
                              }
                            >
                              <span className="text-base">
                                <Icon icon={action.icon} />
                              </span>
                            </div>
                          );
                        })}
                      </div>
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p className="text-gray-600 text-start">No data found</p>
        )}
      </div>

      {/* Pagination section */}
      {totalItems > 0 && (
        <div className="flex items-center justify-between p-4 border-t border-gray-200 flex-wrap">
          {/* Left: Record count */}
          <div className="text-sm text-gray-700">
            {`${(currentPage - 1) * rowsPerPage + 1}-${Math.min(
              currentPage * rowsPerPage,
              totalItems
            )} of ${totalItems}`}
          </div>

          {/* Center: Pagination - Now using actualTotalPages */}
          <div className="flex items-center space-x-1">
            <button
              onClick={() => changePage(currentPage - 1)}
              disabled={currentPage === 1}
              className="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
            >
              <Icon icon="mdi:chevron-left" className="w-5 h-5" />
            </button>

            {/* Dynamic pagination buttons based on actual total pages */}
            {actualTotalPages <= 5 ? (
              // If 5 or fewer pages, show all
              Array.from({ length: actualTotalPages }, (_, i) => (
                <button
                  key={i}
                  onClick={() => changePage(i + 1)}
                  className={`px-3 py-1 rounded ${
                    currentPage === i + 1
                      ? 'bg-yellow-400 text-white font-medium'
                      : 'hover:bg-gray-100'
                  }`}
                >
                  {i + 1}
                </button>
              ))
            ) : (
              // If more than 5 pages, show smart pagination
              <>
                {/* First page */}
                <button
                  onClick={() => changePage(1)}
                  className={`px-3 py-1 rounded ${
                    currentPage === 1
                      ? 'bg-yellow-400 text-white font-medium'
                      : 'hover:bg-gray-100'
                  }`}
                >
                  1
                </button>

                {/* Ellipsis for first gap if needed */}
                {currentPage > 3 && <span className="px-2">...</span>}

                {/* Pages around current page */}
                {Array.from(
                  { length: Math.min(3, actualTotalPages) },
                  (_, i) => {
                    let pageNum;
                    if (currentPage <= 2) {
                      // Near start
                      pageNum = i + 2;
                    } else if (currentPage >= actualTotalPages - 1) {
                      // Near end
                      pageNum = actualTotalPages - 3 + i;
                    } else {
                      // In middle
                      pageNum = currentPage - 1 + i;
                    }

                    // Only show if within valid range
                    if (pageNum > 1 && pageNum < actualTotalPages) {
                      return (
                        <button
                          key={i}
                          onClick={() => changePage(pageNum)}
                          className={`px-3 py-1 rounded ${
                            currentPage === pageNum
                              ? 'bg-yellow-400 text-white font-medium'
                              : 'hover:bg-gray-100'
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    }
                    return null;
                  }
                ).filter(Boolean)}

                {/* Ellipsis for second gap if needed */}
                {currentPage < actualTotalPages - 2 && (
                  <span className="px-2">...</span>
                )}

                {/* Last page */}
                <button
                  onClick={() => changePage(actualTotalPages)}
                  className={`px-3 py-1 rounded ${
                    currentPage === actualTotalPages
                      ? 'bg-yellow-400 text-white font-medium'
                      : 'hover:bg-gray-100'
                  }`}
                >
                  {actualTotalPages}
                </button>
              </>
            )}

            <button
              onClick={() => changePage(currentPage + 1)}
              disabled={currentPage === actualTotalPages}
              className="p-1 rounded hover:bg-gray-100 disabled:opacity-50"
            >
              <Icon icon="mdi:chevron-right" className="w-5 h-5" />
            </button>
          </div>

          {/* Right: Rows per page */}
          <div className="flex items-center">
            <span className="text-sm text-gray-700 mr-2">Rows per page:</span>
            <select
              className="border-none bg-transparent text-sm"
              value={rowsPerPage}
              onChange={(e) =>
                setRowsPerPage && setRowsPerPage(Number(e.target.value))
              }
            >
              <option value="10">10</option>
              <option value="25">25</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
          </div>
        </div>
      )}
    </div>
  );
};

export default NewTablePage;
