'use client';
import React, { useState } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { toast } from 'sonner';
import api from '@/lib/api';
import { useRouter, useParams } from 'next/navigation';
import RegularGoBack from '@/components/shared/RegularGoBack';
import useDataFetch from '@/hooks/useDataFetch';
import FormInput from '@/components/form/FormInput';
import FormDatePicker from '@/components/form/FormDatePicker';
import NumberInput from '@/components/form/NumberInput';

// Validation schema for mission
const validationSchema = Yup.object().shape({
  title: Yup.string().required('Mission title is required'),
  description: Yup.string().required('Description is required'),
  publishDate: Yup.date().required('Publish date is required'),
  expiryDate: Yup.date()
    .required('Expiry date is required')
    .min(
      Yup.ref('publishDate'),
      'The expiry date must be later than the publish date'
    ),
  targetWordCount: Yup.number()
    .required('Minimum word limit is required')
    .positive('Must be a positive number')
    .integer('Must be a number'),
  targetMaxWordCount: Yup.number()
    .positive('Must be a positive number')
    .integer('Must be a number')
    .min(
      Yup.ref('targetWordCount'),
      'Maximum word limit must be greater than minimum word limit'
    ),
  score: Yup.number()
    .positive('Must be a positive number')
    .integer('Must be a number'),
});

const EditMissionDiary = () => {
  const router = useRouter();
  const { id } = useParams();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch mission data
  const {
    data: mission,
    isLoading,
    error,
  } = useDataFetch({
    queryKey: ['mission-diary', id],
    endPoint: `/diary/admin/missions/${id}`,
  });

  const handleSubmit = async (values, { setFieldError }) => {
    try {
      setIsSubmitting(true);
      await api.put(`/diary/admin/missions/${id}`, values);

      router.push('/dashboard/module-management/hec-diary/mission-diary');
    } catch (error) {
      console.error('Error updating mission diary:', error);

      // Handle validation errors
      const validationErrors = error?.response?.data?.validationErrors;
      if (validationErrors) {
        for (const field in validationErrors) {
          if (validationErrors.hasOwnProperty(field)) {
            const errorMessages = validationErrors[field];
            if (Array.isArray(errorMessages) && errorMessages.length > 0) {
              setFieldError(field, errorMessages[0]);
            } else if (typeof errorMessages === 'string') {
              setFieldError(field, errorMessages);
            }
          }
        }
      } else {
        toast.error(
          error?.response?.data?.message || 'Failed to update mission diary'
        );
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  return (
    <div className="">
      <RegularGoBack title="Edit Mission Diary" />

      <div className="mt-6">
        <Formik
          initialValues={{
            title: mission.title || '',
            description: mission.description || '',
            publishDate: mission.publishDate
              ? new Date(mission.publishDate).toISOString().split('T')[0]
              : new Date().toISOString().split('T')[0],
            expiryDate: mission.expiryDate
              ? new Date(mission.expiryDate).toISOString().split('T')[0]
              : new Date(new Date().setDate(new Date().getDate() + 7))
                  .toISOString()
                  .split('T')[0],
            targetWordCount: mission.targetWordCount || '',
            targetMaxWordCount: mission.targetMaxWordCount || '',
            score: mission.score || '',
          }}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting: formSubmitting }) => (
            <Form>
              <div className="p-5 bg-white rounded-lg shadow-md border">
                <div className="bg-gray-50 rounded-lg shadow-sm p-6 mb-6 border border-dashed border-gray-300">
                  {/* Mission form fields */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                      <FormInput
                        label="Mission Title"
                        name="title"
                        placeholder="Write here"
                        required
                      />
                    </div>
                    <div>
                      <FormInput
                        label="Description"
                        name="description"
                        placeholder="Write here"
                        required
                      />
                    </div>
                    <div>
                      <FormDatePicker
                        label="Select Publish Date"
                        name="publishDate"
                        required
                      />
                    </div>
                    <div>
                      <NumberInput
                        label="Word Limit (Minimum)"
                        name="targetWordCount"
                        placeholder="Enter minimum word limit"
                        required
                      />
                    </div>
                    <div>
                      <NumberInput
                        label="Word Limit (Maximum)"
                        name="targetMaxWordCount"
                        placeholder="Enter Maximum word limit"
                        required
                      />
                    </div>
                    <div>
                      <FormDatePicker
                        label="Mission Deadline"
                        name="expiryDate"
                        placeholder="Select expiry date"
                        required
                      />
                    </div>
                    <div>
                      <NumberInput
                        label="Score"
                        name="score"
                        placeholder="Enter Score"
                        required
                      />
                    </div>
                  </div>
                </div>

                {/* Form actions */}
                <div className="flex justify-end space-x-2">
                  <button
                    type="button"
                    className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded"
                    onClick={() => router.back()}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting || formSubmitting}
                    className="bg-yellow-300 hover:bg-yellow-400 text-gray-800 font-medium py-2 px-4 rounded"
                  >
                    {isSubmitting || formSubmitting
                      ? 'Updating...'
                      : 'Update Mission'}
                  </button>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default EditMissionDiary;
