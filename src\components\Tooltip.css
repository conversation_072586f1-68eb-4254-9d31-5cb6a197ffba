/* Custom Tooltip Styles */

/* Import Tippy.js themes */
@import 'tippy.js/themes/light.css';
@import 'tippy.js/themes/light-border.css';

/* Yellow theme */
.tippy-box[data-theme~='yellow'] {
  background-color: #FFEB3B;
  color: #000;
}

.tippy-box[data-theme~='yellow'] .tippy-arrow {
  color: #FFEB3B;
}
.tippy-box[data-theme~='user'] .tippy-arrow {
  color: #432005;
}

/* Size variations */
.tippy-box[data-theme~='sm'] {
  font-size: 0.875rem;
  /* padding: 0.25rem 0.5rem; */
}

.tippy-box[data-theme~='md'] {
  font-size: 1rem;
  /* padding: 0.5rem 0.75rem; */
}

.tippy-box[data-theme~='lg'] {
  font-size: 1.125rem;
  /* padding: 0.75rem 1rem; */
}
