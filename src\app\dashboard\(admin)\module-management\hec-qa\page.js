'use client';

import React, { useState } from 'react';
import QuestionBank from './question-bank/page';
import CreateQuestions from './create-questions/page';
import MissionQAList from './mission-qa-List/page';
import CreateMission from './create-mission-qa/page';
// import MissionQAList from './missionQaList/page';
// import CreateQuestions from '../createQuestions/page';
// import CreateMission from '../createMissionQa/page';
import { Icon } from '@iconify/react'; // Make sure you have this package installed

function HecQA() {
  const [activeTab, setActiveTab] = useState('missionQAList');
  
  // Function to handle back button click
  const handleBackClick = () => {
    // You might want to implement custom back navigation logic here
    window.history.back();
  };
  
  // Function to render the correct content based on active tab
  const renderContent = () => {
    switch(activeTab) {
      case 'questionBank':
        return <QuestionBank />;
      case 'createQuestions':
        return <CreateQuestions />;
      case 'missionQAList':
        return <MissionQAList />;
      case 'createMissionQA':
        return <CreateMission />;
      default:
        return <div>Select a tab</div>;
    }
  };
  
  return (
    <div className="flex flex-col h-screen">
      {/* Header with Back Button and Title */}
      <div className="p-4 bg-white shadow-sm flex items-center">
        {/* Back Arrow */}
        <button 
          onClick={handleBackClick}
          className="p-2 rounded-full hover:bg-gray-200 mr-4"
        >
          <Icon icon="mdi:arrow-left" className="text-xl" />
        </button>
        <div className="text-xl">
          HEC Q & A
        </div>
      </div>
      
      {/* Main Content Area with Sidebar and Content */}
      <div className="flex flex-1">
        {/* Sidebar */}
        <div className="w-64 bg-[#FEFCE8] p-4 flex flex-col rounded-lg">
          <button
            className={`mb-2 p-2 text-left ${activeTab === 'questionBank' ? 'bg-[#FFDE34] text-black' : 'hover:bg-gray-200'}`}
            onClick={() => setActiveTab('questionBank')}
          >
            Question Bank
          </button>
          <button
            className={`mb-2 p-2 text-left ${activeTab === 'createQuestions' ? 'bg-[#FFDE34] text-black' : 'hover:bg-gray-200'}`}
            onClick={() => setActiveTab('createQuestions')}
          >
            Create Questions
          </button>
          <button
            className={`mb-2 p-2 text-left ${activeTab === 'missionQAList' ? 'bg-[#FFDE34] text-black' : 'hover:bg-gray-200'}`}
            onClick={() => setActiveTab('missionQAList')}
          >
            Mission Q & A List
          </button>
          <button
            className={`mb-2 p-2 text-left ${activeTab === 'createMissionQA' ? 'bg-[#FFDE34] text-black' : 'hover:bg-gray-200'}`}
            onClick={() => setActiveTab('createMissionQA')}
          >
            Create Mission Q & A
          </button>
        </div>
        
        {/* Content Area */}
        <div className="flex-1 p-6">
          {renderContent()}
        </div>
      </div>
    </div>
  );
}

export default HecQA;