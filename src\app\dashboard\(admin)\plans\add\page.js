'use client';
import React from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import api from '@/lib/api';
import { Icon } from '@iconify/react';
import useDataFetch from '@/hooks/useDataFetch';

const validationSchema = Yup.object().shape({
  name: Yup.string().required('Name is required'),
  type: Yup.string().required('Plan type is required').oneOf(['starter', 'standard', 'pro', 'ultimate'], 'Type must be one of: starter, standard, pro, ultimate'),
  subscriptionType: Yup.string().required('Subscription type is required'),
  price: Yup.number().required('Price is required').min(0, 'Price must be at least 0'),
  description: Yup.string().required('Description is required'),
  isActive: Yup.boolean().required('Active status is required'),
  features: Yup.array(),
});

const planTypeOptions = [
  { value: 'starter', label: 'Starter' },
  { value: 'standard', label: 'Standard' },
  { value: 'pro', label: 'Pro' },
  { value: 'ultimate', label: 'Ultimate' },
];

const subscriptionTypeOptions = [
  { value: 'monthly', label: 'Monthly' },
  { value: 'yearly', label: 'Yearly' },
];

const activeStatusOptions = [
  { value: true, label: 'Active' },
  { value: false, label: 'Inactive' },
];

const AddPlan = () => {
  const router = useRouter();
  const queryClient = useQueryClient();

  // Fetch all available plan features
  const { data: featuresData, isLoading: featuresLoading } = useDataFetch({
    queryKey: ['plan-features'],
    endPoint: '/plan-features',
  });

  // The API returns features directly in the data property
  const availableFeatures = featuresData || [];

  const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
    try {
      // Extract planFeatureIds from values
      const { planFeatureIds } = values;

      // Create an array of feature names for the selected features
      // If we can't get the feature names, use placeholder names
      let featureNames = [];

      if (planFeatureIds.length > 0) {
        // Try to get the actual feature names if available
        if (availableFeatures.length > 0) {
          featureNames = availableFeatures
            .filter(feature => planFeatureIds.includes(feature.id))
            .map(feature => feature.name);
        } else {
          // Fallback to generic feature names
          featureNames = planFeatureIds.map((_, index) => `Feature ${index + 1}`);
        }
      }

      // Create the payload according to the expected format
      const planPayload = {
        name: values.name,
        type: values.type,
        subscriptionType: values.subscriptionType,
        price: parseFloat(values.price),
        description: values.description,
        features: featureNames,
        isActive: values.isActive
      };

      console.log('Sending plan payload:', planPayload);
      await api.post('/plans', planPayload);
      router.push('/dashboard/plans');
      queryClient.invalidateQueries(['subscription-plans']);
    } catch (error) {
      console.log(error);
      if (error?.response?.data?.validationErrors) {
        const validationErrors = error.response.data.validationErrors;
        Object.keys(validationErrors).forEach((field) => {
          setFieldError(`${field}`, validationErrors[field][0]);
        });
      } else {
        alert('Failed to create plan. Please try again.');
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center mb-6">
        <button
          onClick={() => router.push('/dashboard/plans')}
          className="mr-2 text-gray-600"
        >
          <Icon icon="material-symbols:arrow-back" className="w-5 h-5" />
        </button>
        <h1 className="text-xl font-semibold">Create Plan</h1>
      </div>

      <Formik
        initialValues={{
          name: '',
          type: 'starter',
          subscriptionType: 'monthly',
          description: '',
          price: '',
          isActive: true,
          planFeatureIds: [],
        }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ values, isSubmitting, handleChange, setFieldValue }) => (
          <Form className="bg-white p-6 rounded-lg shadow-sm">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Plan Name<span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="name"
                  value={values.name}
                  onChange={handleChange}
                  placeholder="Pro Plan"
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-yellow-500 focus:border-yellow-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Plan Type<span className="text-red-500">*</span>
                </label>
                <select
                  name="type"
                  value={values.type}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-yellow-500 focus:border-yellow-500"
                >
                  {planTypeOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Subscription Type<span className="text-red-500">*</span>
                </label>
                <select
                  name="subscriptionType"
                  value={values.subscriptionType}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-yellow-500 focus:border-yellow-500"
                >
                  {subscriptionTypeOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Plan Price<span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  name="price"
                  value={values.price}
                  onChange={handleChange}
                  placeholder="29.99"
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-yellow-500 focus:border-yellow-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Active Status
                </label>
                <div className="flex space-x-4">
                  {activeStatusOptions.map((option) => (
                    <label key={option.value.toString()} className="inline-flex items-center">
                      <input
                        type="radio"
                        name="isActive"
                        value={option.value.toString()}
                        checked={values.isActive === option.value}
                        onChange={() => setFieldValue('isActive', option.value)}
                        className="h-4 w-4 text-yellow-500 focus:ring-yellow-500 border-gray-300"
                      />
                      <span className="ml-2 text-gray-700">{option.label}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            <div className="mt-6">
              <h3 className="text-md font-medium mb-3">Select Features</h3>
              {featuresLoading ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-yellow-500 mx-auto"></div>
                  <p className="mt-2 text-gray-600">Loading features...</p>
                </div>
              ) : availableFeatures.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {availableFeatures.map(feature => (
                    <div key={feature.id} className="flex items-start">
                      <input
                        type="checkbox"
                        id={`feature-${feature.id}`}
                        name="planFeatureIds"
                        value={feature.id}
                        onChange={(e) => {
                          const currentIds = [...values.planFeatureIds];
                          if (e.target.checked) {
                            if (!currentIds.includes(feature.id)) {
                              currentIds.push(feature.id);
                            }
                          } else {
                            const index = currentIds.indexOf(feature.id);
                            if (index !== -1) {
                              currentIds.splice(index, 1);
                            }
                          }
                          setFieldValue('planFeatureIds', currentIds);
                        }}
                        checked={values.planFeatureIds.includes(feature.id)}
                        className="mt-1 h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                      />
                      <label htmlFor={`feature-${feature.id}`} className="ml-3 block">
                        <span className="font-medium text-gray-800">{feature.name}</span>
                      </label>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 bg-gray-50 rounded-lg">
                  <p className="text-gray-600">No features available. Please create features first.</p>
                </div>
              )}
            </div>

            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description<span className="text-red-500">*</span>
              </label>
              <textarea
                name="description"
                value={values.description}
                onChange={handleChange}
                placeholder="Plan description..."
                rows={3}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-yellow-500 focus:border-yellow-500"
                required
              />
            </div>

            <div className="flex justify-end space-x-4 mt-6">
              <button
                type="button"
                onClick={() => router.push('/dashboard/plans')}
                className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-6 py-2 bg-yellow-400 text-black rounded-md hover:bg-yellow-500 disabled:opacity-50"
              >
                {isSubmitting ? 'Creating...' : 'Create Plan'}
              </button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default AddPlan;
