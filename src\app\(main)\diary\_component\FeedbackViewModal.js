'use client';

import React from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { formatDate } from '@/utils/dateFormatter';

/**
 * FeedbackViewModal component for displaying teacher feedback with a wooden sign design
 * @param {Object} props - Component props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to close the modal
 * @param {Array} props.feedbacks - Array of feedback objects
 */
const FeedbackViewModal = ({ isOpen, onClose, feedbacks = [] }) => {
  // Handle modal close
  const handleClose = () => {
    onClose();
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={handleClose}
        >
          <motion.div
            className="bg-white rounded-xl shadow-xl w-full max-w-2xl overflow-hidden relative"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: 'spring', damping: 20, stiffness: 300 }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Wooden sign header */}
            <div className="relative">
              <div className="bg-[#FFF9FB] pt-2 pb-2 px-2 shadow-xl flex justify-center">
                <Image
                  src="/assets/images/all-img/wooden-feedback-sign.png"
                  alt="Teacher's Feedback"
                  width={300}
                  height={80}
                  priority
                />
              </div>
            </div>

            {/* Modal content */}
            <div className="p-6">
              <div className="mb-4">
                <h3 className="text-xl font-semibold mb-4">Feedback History</h3>
                {feedbacks.length > 0 ? (
                  <div className="space-y-4">
                    {feedbacks.map((item) => (
                      <div key={item.id} className="border-b pb-4">
                        <div className="flex justify-between items-center mb-2">
                          <span className="font-medium">{item.tutorName}</span>
                          <span className="text-sm text-gray-500">
                            {formatDate(item.createdAt, 'full')}
                          </span>
                        </div>
                        <p className="text-gray-700">{item.feedback}</p>
                        {/* {item.rating && (
                          <div className="mt-2 flex items-center">
                            <span className="mr-2 text-sm">Rating:</span>
                            <div className="flex">
                              {[...Array(5)].map((_, i) => (
                                <span key={i} className="text-yellow-500">
                                  {i < item.rating ? '★' : '☆'}
                                </span>
                              ))}
                            </div>
                          </div>
                        )} */}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 italic">No feedback available.</p>
                )}
              </div>

              {/* Action buttons */}
              <div className="flex justify-end mt-6">
                <button
                  type="button"
                  onClick={handleClose}
                  className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
                >
                  Close
                </button>
              </div>
            </div>

            {/* Close button */}
            <button
              className="absolute top-1 right-1 w-8 h-8 hover:opacity-80 transition-opacity"
              onClick={handleClose}
              aria-label="Close greeting modal"
            >
              <Image
                src="/assets/images/all-img/cross-bg.png"
                alt="Close"
                width={30}
                height={30}
                className="w-full h-full object-contain"
              />
            </button>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default FeedbackViewModal;
