'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import TodaysDiary from './_components/TodaysDiary';
import MissionDiary from './_components/MissionDiary';
import HecDiaryLayout from './review/_components/HecDiaryLayout';

const HecDiary = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');

  // Set default active tab or use the one from URL
  const [activeTab, setActiveTab] = useState(tabParam || 'todaysDiary');

  // Update state when URL changes
  useEffect(() => {
    if (tabParam) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  // Function to render the correct content based on active tab
  const renderContent = () => {
    switch(activeTab) {
      case 'todaysDiary':
        return <TodaysDiary />;
      case 'missionDiary':
        return <MissionDiary />;
      default:
        return <div>Select a tab</div>;
    }
  };

  return (
    <HecDiaryLayout activeTab={activeTab}>
      {renderContent()}
    </HecDiaryLayout>
  );
};

export default HecDiary;