import React from 'react';
import { Icon } from '@iconify/react';
import { motion } from 'framer-motion';

const Header = () => {
  return (
    <motion.header
      className="bg-white border-b border-gray-200 px-4 py-3 flex items-center"
      initial={{ y: -10, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <button
        className="flex items-center text-gray-700 hover:text-gray-900 transition-colors"
        aria-label="Go back"
      >
        <Icon icon="lucide:chevron-left" width={20} height={20} />
        <span className="ml-1 font-medium">HEC Play</span>
      </button>
    </motion.header>
  );
};

export default Header;