'use client';
import React, { useState } from 'react';
import { FiSearch, FiChevronLeft, FiChevronRight } from 'react-icons/fi';

const MilestonePage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Sample milestone data
  const milestones = [
    {
      id: 1,
      name: 'Complete 30 or more English words in the English diary',
      instruction: 'Efficiency at work: How it benefits you and your workplace.',
      wordWritten: '250 words',
      status: 'Done',
    },
    {
      id: 2,
      name: 'Complete 200 or more English words in the English diary',
      instruction: 'The future of work: How technology is revolutionizing efficiency.',
      wordWritten: '25 words',
      status: 'Incomplete',
    },
    {
      id: 3,
      name: 'Complete 200 or more English words in the English diary',
      instruction: 'Time is of the essence: Time management techniques for maximum productivity.',
      wordWritten: '250 words',
      status: 'Done',
    },
    {
      id: 4,
      name: 'Complete 30 or more English words in the English diary',
      instruction: 'Efficiency at work: How it benefits you and your workplace.',
      wordWritten: '250 words',
      status: 'Done',
    },
    {
      id: 5,
      name: 'Complete 200 or more English words in the English diary',
      instruction: 'Time is of the essence: Time management techniques for maximum productivity.',
      wordWritten: '250 words',
      status: 'On Going',
    },
    {
      id: 6,
      name: 'Complete 30 or more English words in the English diary',
      instruction: 'The importance of learning and growth for long-term productivity.',
      wordWritten: '250 words',
      status: 'Done',
    },
    {
      id: 7,
      name: 'Complete 30 or more English words in the English diary',
      instruction: 'Efficiency at work: How it benefits you and your workplace.',
      wordWritten: '250 words',
      status: 'Done',
    },
    {
      id: 8,
      name: 'Complete 30 or more English words in the English diary',
      instruction: 'Efficiency at work: How it benefits you and your workplace.',
      wordWritten: '250 words',
      status: 'Done',
    },
    {
      id: 9,
      name: 'Complete 30 or more English words in the English diary',
      instruction: 'Efficiency at work: How it benefits you and your workplace.',
      wordWritten: '250 words',
      status: 'Done',
    },
    {
      id: 10,
      name: 'Complete 30 or more English words in the English diary',
      instruction: 'Efficiency at work: How it benefits you and your workplace.',
      wordWritten: '250 words',
      status: 'Done',
    },
  ];

  const totalRecords = 150;
  const totalPages = Math.ceil(totalRecords / rowsPerPage);

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const renderPagination = () => {
    const pages = [];
    const maxVisiblePages = 5;

    // Always show first page
    pages.push(
      <button
        key="page-1"
        onClick={() => handlePageChange(1)}
        className={`w-8 h-8 rounded-full flex items-center justify-center ${currentPage === 1 ? 'bg-yellow-100 text-yellow-800' : 'text-gray-600'}`}
      >
        1
      </button>
    );

    // Calculate range of visible pages
    let startPage = Math.max(2, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 1);

    // Adjust if we're near the end
    if (endPage - startPage < maxVisiblePages - 1) {
      startPage = Math.max(2, endPage - maxVisiblePages + 1);
    }

    // Add ellipsis if needed before visible pages
    if (startPage > 2) {
      pages.push(<span key="ellipsis-1" className="px-2">...</span>);
    }

    // Add visible pages
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={`page-${i}`}
          onClick={() => handlePageChange(i)}
          className={`w-8 h-8 rounded-full flex items-center justify-center ${currentPage === i ? 'bg-yellow-100 text-yellow-800' : 'text-gray-600'}`}
        >
          {i}
        </button>
      );
    }

    // Add ellipsis if needed after visible pages
    if (endPage < totalPages - 1) {
      pages.push(<span key="ellipsis-2" className="px-2">...</span>);
    }

    // Always show last page if there is more than one page
    if (totalPages > 1) {
      pages.push(
        <button
          key={`page-${totalPages}`}
          onClick={() => handlePageChange(totalPages)}
          className={`w-8 h-8 rounded-full flex items-center justify-center ${currentPage === totalPages ? 'bg-yellow-100 text-yellow-800' : 'text-gray-600'}`}
        >
          {totalPages}
        </button>
      );
    }

    return pages;
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold text-[#4A2511] mb-6">Milestone</h1>

      {/* Search bar */}
      <div className="mb-6 max-w-xs">
        <div className="relative">
          <input
            type="text"
            placeholder="Search by name"
            className="w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            <FiSearch className="text-gray-400" />
          </div>
        </div>
      </div>

      {/* Milestone table */}
      <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-6">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead>
              <tr className="bg-[#FFFBE6] border-b border-gray-200">
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 tracking-wider w-12">
                  <div className="flex items-center">
                    <span>#</span>
                    <button className="ml-1 text-gray-500">
                      <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                  </div>
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 tracking-wider">
                  Milestone Name
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 tracking-wider">
                  Instruction
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 tracking-wider">
                  Word Written
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {milestones.map((milestone, index) => (
                <tr key={milestone.id} className="hover:bg-gray-50">
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-700">{milestone.id}</td>
                  <td className="px-4 py-3 text-sm text-gray-700">{milestone.name}</td>
                  <td className="px-4 py-3 text-sm text-gray-700">{milestone.instruction}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-700">{milestone.wordWritten}</td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <span
                      className={`inline-flex items-center px-3 py-1 rounded-md text-xs font-medium ${
                        milestone.status === 'Done'
                          ? 'bg-[#E0F2E9] text-green-800'
                          : milestone.status === 'Incomplete'
                            ? 'bg-[#FADADD] text-red-800'
                            : 'bg-[#FFF9C2] text-yellow-800'
                      }`}
                    >
                      {milestone.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="grid grid-cols-3 items-center mb-4">
        {/* Left: Record count */}
        <div className="text-sm text-gray-700">
          1-10 of {totalRecords}
        </div>

        {/* Center: Pagination controls */}
        <div className="flex items-center justify-center">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="w-8 h-8 rounded-full flex items-center justify-center text-gray-600 disabled:opacity-50"
          >
            <FiChevronLeft />
          </button>

          <div className="flex items-center space-x-1 mx-2">
            {renderPagination()}
          </div>

          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="w-8 h-8 rounded-full flex items-center justify-center text-gray-600 disabled:opacity-50"
          >
            <FiChevronRight />
          </button>
        </div>

        {/* Right: Rows per page */}
        <div className="flex items-center justify-end">
          <span className="text-sm text-gray-700 mr-2">Rows per page:</span>
          <div className="relative">
            <select
              className="appearance-none bg-white border border-gray-300 rounded-md py-1 pl-3 pr-8 text-sm text-gray-700 leading-tight focus:outline-none"
              value={rowsPerPage}
              onChange={(e) => setRowsPerPage(Number(e.target.value))}
            >
              <option value="10">10</option>
              <option value="25">25</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MilestonePage;
