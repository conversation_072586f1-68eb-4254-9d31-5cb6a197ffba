'use client';
import React from 'react';
import ImageGroup from './ImageGroup';

const KeyFeatures = () => {
  const featureData = [
    {
      thumbImg: '/assets/images/all-img/introduction/headerpart3five.png',
      diaryImg: '/assets/images/all-img/introduction/headerpart3one.png',
      title: 'HEC My Diary',
      description:
        "User Completed diary written in chosen Skin. HEC editor corrects the user's diary grammar and corresponding sentences.",
    },
    {
      thumbImg: '/assets/images/all-img/introduction/headerpart3four.png',
      diaryImg: '/assets/images/all-img/introduction/headerpart3one.png',
      title: 'HEC Play',
      description:
        "User Completed diary written in chosen Skin. HEC editor corrects the user's diary grammar and corresponding sentences.This fun and interactive app helps kids learn English through engaging games,activities. Designed for  learners, HEC focuses on vocabulary and sentence formation in a playful way.",
    },
    {
      thumbImg: '/assets/images/all-img/introduction/headerpart3eight.png',
      diaryImg: '/assets/images/all-img/introduction/headerpart3one.png',
      title: 'HEC Q&A',
      description:
        "To make English learning fun and interactive, here’s a weekly or monthly  mission where learners answer different types of questions to practice Each week, learners get a set of questions in different format",
    },
    {
      thumbImg: '/assets/images/all-img/introduction/headerpart3eleven.png',
      diaryImg: '/assets/images/all-img/introduction/headerpart3one.png',
      title: 'HEC Essay',
      description:
        "This app is designed to help children improve their English writing skills through guided coaching, interactive exercises, and essay-writing challenges.",
    },
    {
      thumbImg: '/assets/images/all-img/introduction/headerpart3twelve.png',
      diaryImg: '/assets/images/all-img/introduction/headerpart3one.png',
      title: 'HEC Novel',
      description:
        "This is designed to help children improve their English writing skills through guided coaching, interactive exercises, and novel-writing challenges.",
    },
  ];
  return (
    <div className="bg-[#FFF6EF] relative overflow-hidden">
      <div className="w-full h-[150px] bg-white rounded-b-[200%] 2xl:h-[200px] z-0 2xl:rounded-b-[300%] relative"></div>
      <div className="text-[#f5f5f5] flex flex-col items-center">
        <div>
          <h1 className="text-3xl lg:text-5xl font-semibold text-gray-700 text-center pt-10 mt-10">
            Our Key Features
          </h1>

          <div>
            {featureData.map((feature, index) => (
              <ImageGroup
                key={index}
                index={index + 1}
                thumbImg={feature.thumbImg}
                diaryImg={feature.diaryImg}
                title={feature.title}
                description={feature.description}
                featureData={featureData}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default KeyFeatures;
