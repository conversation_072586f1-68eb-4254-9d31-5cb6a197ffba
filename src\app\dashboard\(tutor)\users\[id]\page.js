'use client';
import { Icon } from '@iconify/react';
import { useParams, useRouter } from 'next/navigation';
import api from '@/lib/api';
import BasicInfo from './_component/BasicInfo';
import Education from './_component/Education';
import UserCard from './_component/UserCard';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';

const UserDetails = () => {
  const { id } = useParams();
  const router = useRouter();
  const initialExperience = "Hi! I'm Mr. <PERSON>, and I'm thrilled to be teaching 5th grade this year. I can make easier students lesson what is very effective. Hi, I have 5 years experience in teaching profession. I can make easier students lesson what is very effective. I've been an educator for 8 years and love watching students grow both academically and personally. My classroom is all about curiosity, creativity, and community. When I'm not teaching, I enjoy hiking, trying new recipes, and reading mystery novels.";
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [experience, setExperience] = useState(initialExperience);

  return (
    <div className="pb-5 pt-3">
      {/* Go Back */}
      <div
        onClick={() => router.back()}
        className="flex items-center gap-2 text-lg font-medium mb-6 cursor-pointer hover:text-yellow-600"
      >
        <Icon icon="eva:arrow-back-fill" className="w-6 h-6" />
        <span>User Details</span>
      </div>

      {/* User Card */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6 border">
        <UserCard id={id} />
      </div>

      {/* Basic Information */}
      <div>
        <div className="flex items-center w-full mb-4">
          <h3 className="text-lg font-semibold min-w-40">Basic Information</h3>
          <div className="w-full border h-0.5"></div>
        </div>
        <BasicInfo />
      </div>

      {/* Coaching Experience */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-6 border">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-lg font-semibold">Coaching Experience</h3>
          {!isEditing && (
            <button 
              onClick={() => setIsEditing(true)} 
              className="p-2 hover:bg-gray-100 rounded-full"
            >
              <Icon icon="material-symbols:edit-outline" className="w-5 h-5" />
            </button>
          )}
        </div>
        
        {isEditing ? (
          <div className="space-y-4">
            <textarea
              value={experience}
              onChange={(e) => setExperience(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-yellow-500 focus:outline-none"
            />
            <div className="flex justify-end gap-2">
              <button
                onClick={async () => {
                  // try {
                  //   setIsLoading(true);
                  //   await api.put(`/users/${id}/experience`, { experience });
                  //   setIsEditing(false);
                  //   toast.success('Experience updated successfully');
                  // } catch (error) {
                  //   toast.error('Failed to update experience');
                  // } finally {
                  //   setIsLoading(false);
                  // }
                }}
                disabled={isLoading}
                className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-yellow-300"
              >
                {isLoading ? 'Saving...' : 'Save'}
              </button>
              <button
                onClick={() => {
                  setIsEditing(false);
                  setExperience(initialExperience); // Reset to original value on cancel
                }}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
              >
                Cancel
              </button>
            </div>
          </div>
        ) : (
          <p className="text-gray-600">
            {experience}
          </p>
        )}
      </div>

      {/* Education */}
      <Education />
    </div>
  );
};

export default UserDetails;
