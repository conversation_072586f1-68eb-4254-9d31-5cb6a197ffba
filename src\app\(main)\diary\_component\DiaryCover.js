import React from 'react';
import Button from '@/components/Button';
import DiaryIconsSidebar from '@/app/(main)/diary/_component/DiaryIconsSidebar';

const DiaryCover = ({ hasEntries, onOpen }) => {
  return (
    <>
      {/* Left vertical blue bar */}
      <div className="absolute left-0 top-0 bottom-0 w-[60px] bg-[#1E3A8A]"></div>

      {/* Change Cover button */}
      <div className="absolute top-6 right-6">
        <Button
          icon="mdi:palette-outline"
          buttonText="Change Cover"
        />
      </div>

      {/* Icons on right side */}
      <DiaryIconsSidebar position="right-[-50px] top-1/4" />

      {/* Center card with My Diary text */}
      <div
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg shadow-md px-12 py-6 text-center min-w-[200px] border border-gray-200 cursor-pointer"
        onClick={onOpen}
      >
        <h1 className="text-2xl font-bold text-[#1E3A8A]">My Diary</h1>
        {hasEntries ? (
          <p className="text-[#3B82F6] mt-1 cursor-pointer">Open</p>
        ) : (
          <p className="text-gray-500 mt-1">No entries yet</p>
        )}
      </div>

      {/* Size indicator at bottom */}
      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 text-xs text-gray-500 bg-white px-2 py-0.5 rounded-t-md">
        750 Hpx × 50 Wpx
      </div>
    </>
  );
};

export default DiaryCover;
