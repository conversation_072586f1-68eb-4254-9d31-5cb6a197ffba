import { useField } from 'formik';
import { useCallback, useState } from 'react';

const FormFile = ({ label, accept, maxSize = 5242880, ...props }) => {
  const [field, meta, helpers] = useField(props);
  const { setValue } = helpers;
  const [preview, setPreview] = useState(null);
  const [dragActive, setDragActive] = useState(false);

  const handleFile = useCallback(
    (file) => {
      if (!file) return;

      // Check file type
      if (accept && !accept.split(',').some((type) => file.type.match(type.trim()))) {
        helpers.setError('Invalid file type');
        return;
      }

      // Check file size (default 5MB)
      if (file.size > maxSize) {
        helpers.setError('File is too large');
        return;
      }

      setValue(file);

      // Create preview for images
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onloadend = () => setPreview(reader.result);
        reader.readAsDataURL(file);
      } else {
        setPreview(null);
      }
    },
    [accept, maxSize, setValue, helpers]
  );

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  return (
    <div className="">
      <label className="block text-sm font-medium mb-1">
        {label}
      </label>
      <div
        className={`relative border-2 border-dashed rounded-lg p-4 text-center ${dragActive
          ? 'border-indigo-500 bg-indigo-50'
          : 'border-gray-300'}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          type="file"
          onChange={(e) => handleFile(e.target.files[0])}
          accept={accept}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          {...props}
        />
        <div className="space-y-2">
          <div className="text-sm text-gray-600">
            Drag and drop your file here, or click to select
          </div>
          {accept && (
            <div className="text-xs text-gray-500">
              Accepted files: {accept}
            </div>
          )}
          {maxSize && (
            <div className="text-xs text-gray-500">
              Max size: {(maxSize / 1024 / 1024).toFixed(1)}MB
            </div>
          )}
        </div>
      </div>

      {preview && (
        <div className="mt-2">
          <img src={preview} alt="Preview" className="max-h-32 mx-auto" />
        </div>
      )}

      {field.value && (
        <div className="mt-2 text-sm text-gray-600">
          Selected: {field.value.name}
        </div>
      )}

      {meta.touched && meta.error ? (
        <div className="text-red-500 text-xs mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export default FormFile;