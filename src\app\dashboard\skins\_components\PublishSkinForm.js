'use client';
import React from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import FormInput from '@/components/form/FormInput';
import FormSelect from '@/components/form/FormSelect';
import FormRadio from '@/components/form/FormRadio';
import NumberInput from '@/components/form/NumberInput';
import FormCheckbox from '@/components/form/FormCheckbox';
import Button from '@/components/Button';

const PublishSkinForm = ({
  selectedSkin,
  onSubmit,
  onCancel,
  isPublishing,
  promotions
}) => {
  // Generate initial values based on the selected skin
  const initialValues = {
    itemNumber: `SK-${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
    title: selectedSkin?.name || '',
    description: selectedSkin?.description || '',
    type: 'in_app_purchase',
    price: 9.99,
    isPurchasableInRewardpoint: true,
    isActive: true,
    isFeatured: false,
    promotionId: '',
    metadata: ''
  };
  return (
    <Formik
      initialValues={initialValues}
      validationSchema={Yup.object({
        itemNumber: Yup.string().required('Item number is required'),
        title: Yup.string().required('Title is required'),
        description: Yup.string().required('Description is required'),
        type: Yup.string().required('Type is required'),
        price: Yup.number().when('type', {
          is: 'in_app_purchase',
          then: (schema) => schema.required('Price is required').min(0, 'Price must be positive'),
          otherwise: (schema) => schema.nullable()
        }),
        isPurchasableInRewardpoint: Yup.boolean(),
        isActive: Yup.boolean().required('Active status is required'),
        isFeatured: Yup.boolean().required('Featured status is required'),
        promotionId: Yup.string(),
        metadata: Yup.string()
      })}
      onSubmit={onSubmit}
    >
      {({ values }) => (
        <Form>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <FormInput
                label="Item Number"
                name="itemNumber"
                placeholder="SK-001"
                required
              />
            </div>

            <div>
              <FormInput
                label="Title"
                name="title"
                placeholder="Modern Blue Diary Skin"
                required
              />
            </div>
            <div>
              <FormSelect
                label="Promotion"
                name="promotionId"
                options={
                  promotions?.items?.map((promo) => ({
                    value: promo.id,
                    label: promo.name,
                  })) || []
                }
                placeholder="Select a promotion (optional)"
              />
            </div>
            <div className="md:col-span-2">
              <FormInput
                label="Description"
                name="description"
                placeholder="A modern blue theme with clean typography for your diary"
                isTextarea={true}
                required
              />
            </div>
            <div>
              <FormRadio
                label="Type"
                name="type"
                options={[
                  { value: 'in_app_purchase', label: 'In-app Purchase' },
                  { value: 'free', label: 'Free' }
                ]}
                required
              />
            </div>
            <div>
              <FormInput
                label="Metadata (Tags)"
                name="metadata"
                placeholder="modern,blue,clean"
              />
            </div>
            {values.type === 'in_app_purchase' && (
              <div>
                <NumberInput
                  label="Price"
                  name="price"
                  placeholder="9.99"
                  required
                />
              </div>
            )}
            <div>
              <FormCheckbox
                label="Purchasable with Reward Points"
                name="isPurchasableInRewardpoint"
              />
            </div>
            <div>
              <FormRadio
                label="Active Status"
                name="isActive"
                options={[
                  { value: true, label: 'Active' },
                  { value: false, label: 'Inactive' }
                ]}
                required
              />
            </div>
            <div>
              <FormRadio
                label="Featured"
                name="isFeatured"
                options={[
                  { value: true, label: 'Yes' },
                  { value: false, label: 'No' }
                ]}
                required
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              buttonText="Cancel"
              onClick={onCancel}
              className="w-auto bg-gray-200 text-gray-800 hover:bg-gray-300"
              type="button"
              disabled={isPublishing}
            />
            <Button
              buttonText={isPublishing ? "Publishing..." : "Publish"}
              className="w-auto bg-blue-500 text-white hover:bg-blue-600"
              type="submit"
              disabled={isPublishing}
            />
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default PublishSkinForm;
