'use client';
import React, { useState } from 'react';
import { FiCalendar, FiTrendingUp, FiTrendingDown, FiTarget } from 'react-icons/fi';

const SuccessRatePage = () => {
  const [timeRange, setTimeRange] = useState('month');
  
  // Sample success rate data
  const successData = {
    month: {
      rate: 85,
      completed: 17,
      total: 20,
      trend: 'up',
      trendValue: 5,
      categories: [
        { name: 'Writing', rate: 90, completed: 9, total: 10 },
        { name: 'Reading', rate: 80, completed: 8, total: 10 },
      ],
    },
    quarter: {
      rate: 78,
      completed: 47,
      total: 60,
      trend: 'down',
      trendValue: 2,
      categories: [
        { name: 'Writing', rate: 83, completed: 25, total: 30 },
        { name: 'Reading', rate: 73, completed: 22, total: 30 },
      ],
    },
    year: {
      rate: 82,
      completed: 197,
      total: 240,
      trend: 'up',
      trendValue: 3,
      categories: [
        { name: 'Writing', rate: 85, completed: 102, total: 120 },
        { name: 'Reading', rate: 79, completed: 95, total: 120 },
      ],
    },
  };

  const currentData = successData[timeRange];

  return (
    <div className="container mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Success Rate</h1>
        
        <div className="flex space-x-2 bg-white rounded-lg shadow-sm">
          <button
            className={`px-4 py-2 rounded-lg ${timeRange === 'month' ? 'bg-yellow-100 text-yellow-800' : 'text-gray-700'}`}
            onClick={() => setTimeRange('month')}
          >
            Month
          </button>
          <button
            className={`px-4 py-2 rounded-lg ${timeRange === 'quarter' ? 'bg-yellow-100 text-yellow-800' : 'text-gray-700'}`}
            onClick={() => setTimeRange('quarter')}
          >
            Quarter
          </button>
          <button
            className={`px-4 py-2 rounded-lg ${timeRange === 'year' ? 'bg-yellow-100 text-yellow-800' : 'text-gray-700'}`}
            onClick={() => setTimeRange('year')}
          >
            Year
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold mb-2">Overall Success Rate</h2>
          <div className="flex items-center">
            <div className="relative w-24 h-24">
              <svg className="w-full h-full" viewBox="0 0 100 100">
                <circle
                  className="text-gray-200 stroke-current"
                  strokeWidth="10"
                  cx="50"
                  cy="50"
                  r="40"
                  fill="transparent"
                ></circle>
                <circle
                  className="text-yellow-500 stroke-current"
                  strokeWidth="10"
                  strokeLinecap="round"
                  cx="50"
                  cy="50"
                  r="40"
                  fill="transparent"
                  strokeDasharray={`${currentData.rate * 2.51} 251`}
                  strokeDashoffset="0"
                  transform="rotate(-90 50 50)"
                ></circle>
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-2xl font-bold">{currentData.rate}%</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm text-gray-600">
                You've completed {currentData.completed} out of {currentData.total} missions.
              </p>
              <div className="flex items-center mt-2">
                {currentData.trend === 'up' ? (
                  <>
                    <FiTrendingUp className="text-green-500 mr-1" />
                    <span className="text-green-500 text-sm">+{currentData.trendValue}% from previous period</span>
                  </>
                ) : (
                  <>
                    <FiTrendingDown className="text-red-500 mr-1" />
                    <span className="text-red-500 text-sm">-{currentData.trendValue}% from previous period</span>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold mb-2">Writing Missions</h2>
          <div className="flex items-center">
            <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
              <FiTarget className="text-blue-500 text-xl" />
            </div>
            <div>
              <div className="flex items-center">
                <p className="text-3xl font-bold">{currentData.categories[0].rate}%</p>
                <span className="ml-2 text-sm text-gray-600">Success Rate</span>
              </div>
              <p className="text-sm text-gray-600">
                {currentData.categories[0].completed}/{currentData.categories[0].total} missions completed
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-lg font-semibold mb-2">Reading Missions</h2>
          <div className="flex items-center">
            <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mr-4">
              <FiTarget className="text-purple-500 text-xl" />
            </div>
            <div>
              <div className="flex items-center">
                <p className="text-3xl font-bold">{currentData.categories[1].rate}%</p>
                <span className="ml-2 text-sm text-gray-600">Success Rate</span>
              </div>
              <p className="text-sm text-gray-600">
                {currentData.categories[1].completed}/{currentData.categories[1].total} missions completed
              </p>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-lg font-semibold mb-4">Mission Completion by Category</h2>
        
        <div className="space-y-4">
          {currentData.categories.map((category, index) => (
            <div key={index}>
              <div className="flex justify-between items-center mb-1">
                <span className="font-medium">{category.name}</span>
                <span className="text-sm text-gray-600">{category.rate}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <div 
                  className={`h-2.5 rounded-full ${index === 0 ? 'bg-blue-500' : 'bg-purple-500'}`}
                  style={{ width: `${category.rate}%` }}
                ></div>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {category.completed} completed out of {category.total} missions
              </p>
            </div>
          ))}
        </div>
      </div>
      
      <div className="mt-8 bg-white rounded-lg shadow-md p-6">
        <h2 className="text-lg font-semibold mb-4">Tips to Improve Your Success Rate</h2>
        
        <div className="space-y-4">
          <div className="flex">
            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
              <span className="font-bold text-yellow-700">1</span>
            </div>
            <div>
              <h3 className="font-medium">Set Realistic Goals</h3>
              <p className="text-gray-600">Start with achievable missions and gradually increase difficulty.</p>
            </div>
          </div>
          
          <div className="flex">
            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
              <span className="font-bold text-yellow-700">2</span>
            </div>
            <div>
              <h3 className="font-medium">Establish a Routine</h3>
              <p className="text-gray-600">Consistency is key. Set aside specific times for your missions.</p>
            </div>
          </div>
          
          <div className="flex">
            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
              <span className="font-bold text-yellow-700">3</span>
            </div>
            <div>
              <h3 className="font-medium">Track Your Progress</h3>
              <p className="text-gray-600">Regularly review your success rate and adjust your approach as needed.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuccessRatePage;
