import { Formik, Form } from 'formik';
import { Icon } from '@iconify/react';
import React, { useState } from 'react';

const EditableSection = ({ title, initialValues, onSubmit, fields }) => {
  const [isEditing, setIsEditing] = useState(false);

  const handleCancel = () => {
    setIsEditing(false);
  };

  const renderField = (field, values, isEditing) => (
    <div key={field.name}>
      <label className="block text-sm text-gray-600 mb-1">
        {field.label}
      </label>
      {isEditing ? (
        <input
          type={field.type || 'text'}
          name={field.name}
          className="w-full p-2 border rounded-md"
        />
      ) : (
        <input
          type="text"
          value={values[field.name] || ''}
          className="w-full p-2 border rounded-md"
          disabled
        />
      )}
    </div>
  );

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h4 className="font-medium">{title}</h4>
        {!isEditing && (
          <button
            type="button"
            onClick={() => setIsEditing(true)}
            className="p-2 hover:bg-gray-100 rounded-full"
          >
            <Icon icon="material-symbols:edit-outline" className="w-5 h-5" />
          </button>
        )}
      </div>

      <Formik
        initialValues={initialValues}
        onSubmit={async (values, { setSubmitting }) => {
          await onSubmit(values);
          setIsEditing(false);
          setSubmitting(false);
        }}
      >
        {({ values, isSubmitting }) => (
          <Form>
            <div className="space-y-4">
              {fields.map((field) => renderField(field, values, isEditing))}
            </div>

            {isEditing && (
              <div className="flex gap-2 mt-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
                >
                  {isSubmitting ? 'Saving...' : 'Save'}
                </button>
                <button
                  type="button"
                  onClick={handleCancel}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                >
                  Cancel
                </button>
              </div>
            )}
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default EditableSection;