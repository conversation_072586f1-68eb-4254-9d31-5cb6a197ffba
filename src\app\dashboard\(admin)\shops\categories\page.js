'use client';
import BasicTablePage from '@/components/form/BasicTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';

const ShopCategories = () => {
  const router = useRouter();
  const [page, setPage] = useState(1);
  const [limit] = useState(10);

  const {
    data,
    isLoading,
    refetch,
  } = useDataFetch({
    queryKey: ['shop-categories', page, limit],
    endPoint: '/admin/shop/categories',
    params: { page, limit }
  });
  const categories = data?.items || [];

  const tableData = categories?.map((item, index) => {
    return {
      index: (page - 1) * limit + index + 1,
      name: item.name,
      description: item?.description
        ? item.description?.length > 50
          ? item.description?.slice(0, 50) + '...'
          : item.description
        : '--',
      status: item?.isActive ? (
        <span className="text-green-600 px-5 py-1.5 rounded bg-green-100 shadow">
          Active
        </span>
      ) : (
        <span className="text-red-600 px-5 rounded bg-red-100 shadow">
          Inactive
        </span>
      ),
    };
  });

  const columns = [
    {
      label: '#',
      field: 'index',
    },
    {
      label: 'Category Name',
      field: 'name',
    },
    {
      label: 'Description',
      field: 'description',
    },
    {
      label: 'Active Status',
      field: 'status',
    },
    {
      label: 'Action',
      field: '',
    },
  ];

  const actions = [
    {
      name: 'edit',
      icon: 'material-symbols:edit-outline',
      className: 'text-gray-600',
      onClick: (val) => {
        router.push(`/dashboard/shops/categories/edit/${categories[val]?.id}`);
      },
    },
    {
      name: 'delete',
      icon: 'heroicons-outline:trash',
      className: 'text-red-600',
      onClick: (val) => {
        handleCategoryDelete(categories[val]?.id);
      },
    },
  ];

  const handleCategoryDelete = async (categoryId) => {
    try {
      const response = await api.delete(`/admin/shop/categories/${categoryId}`);
      refetch();
      console.log(response);
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className="">
      <BasicTablePage
        title={'All Categories'}
        createButton={'Add Category'}
        createBtnLink={'/dashboard/shops/categories/add'}
        actions={actions}
        columns={columns}
        data={tableData}
        loading={isLoading}
        currentPage={page}
        changePage={setPage}
        totalItems={data?.totalCount || 0}
        rowsPerPage={limit}
      />
    </div>
  );
};

export default ShopCategories;
