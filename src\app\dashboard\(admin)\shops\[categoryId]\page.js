'use client';

import useDataFetch from '@/hooks/useDataFetch';
import { Icon } from '@iconify/react';
import { useParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import React, { useState } from 'react';
import RegularGoBack from '@/components/shared/RegularGoBack';
import { IMAGE_BASE_URL } from '@/lib/config';
import { useQueryClient } from '@tanstack/react-query';
import api from '@/lib/api';
import Pagination from '@/components/Pagination';

const CategoryShops = () => {
  const { categoryId } = useParams();
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const router = useRouter();
  const queryClient = useQueryClient();

  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['shops-by-category', categoryId, page, limit, search],
    endPoint: `/admin/shop/categories/${categoryId}/items`,
    params: { 
      page, 
      limit,
      ...(search.length > 2 && { title: search })
    },
    enabled: search.length === 0 || search.length > 2
  });

  const categoryData = data?.items;
  const totalItems = data?.totalCount || 0;

  const handleDelete = async (id) => {
    try {
      const response = await api.delete(`/admin/shop/items/${id}`);
      console.log(response);
      refetch();
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className="shadow-md rounded-lg">
      <div className="flex justify-between items-center p-5 bg-gray-50">
        <h2 className="sm:text-xl font-[500]">Shop Management</h2>
      </div>

      <div className="p-5">
        <RegularGoBack className={'pb-5 max-w-32'} />

        <div className="max-w-72 relative mb-3">
          <input
            type="text"
            onChange={(e) => {
              const value = e.target.value;
              if (value.length > 2 || value.length === 0) {
                setSearch(value);
              }
            }}
            placeholder="Search items..."
            className="w-full px-4 py-2 pr-8 border rounded-lg focus:outline-none focus:ring-1 focus:ring-yellow-400"
          />
          <Icon
            icon="stash:search"
            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5"
          />
        </div>

        {isLoading ? (
          <div className="text-center py-8 text-gray-500">
            Loading...
          </div>
        ) : categoryData?.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No items found
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {categoryData?.map((item) => (
              <div
                key={item.id}
                onClick={() => router.push(`/dashboard/shops/details/${item?.id}`)}
                className="bg-[#F6F6F6] rounded-lg shadow-sm p-4 border bg-gray-100 cursor-pointer"
              >
                <div className="relative h-48 mb-4">
                  <Image
                    src={
                      item?.filePath
                        ? `${item.filePath}`
                        : '/assets/images/all-img/noImage.png'
                    }
                    alt={item.title}
                    fill
                    className="rounded-lg object-cover"
                  />
                  {item.type === 'free' && (
                    <span className="absolute -top-4 -left-4 bg-green-500 text-white text-xs px-3 py-1 rounded-br-lg rounded-tl-lg">
                      New
                    </span>
                  )}
                </div>

                <div className="">
                  <h4 className="font-medium text-gray-800">{item.title}</h4>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <span className="font-[600]">
                        {item.price === 0 ? 'Free' : `${item.price} ₩`}
                      </span>
                      {item.isOnSale && (
                        <span className="ml-2 text-sm text-gray-500 line-through">
                          {item.discountedPrice} ₩
                        </span>
                      )}
                    </div>

                    <div className="flex items-center space-x-2 mt-2">
                      <button onClick={() => router.push(`/dashboard/shops/edit/${item?.id}`)} className="p-1 hover:shadow bg-yellow-100 border border-yellow-300 rounded">
                        <Icon
                          icon="material-symbols:edit-outline"
                          className="w-5 h-5"
                        />
                      </button>
                      <button onClick={() => handleDelete(item?.id)} className="p-1 hover:shadow bg-yellow-100 rounded text-red-500 border border-red-300">
                        <Icon
                          icon="solar:trash-bin-trash-linear"
                          className="w-5 h-5"
                        />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {!isLoading && categoryData?.length > 0 && (
          <Pagination
            changePage={setPage}
            currentPage={page}
            totalItems={totalItems}
            rowsPerPage={limit}
          />
        )}
      </div>
    </div>
  );
};

export default CategoryShops;
