import { useField } from 'formik';

const FormSelect = ({ label, options, required, placeholder = 'Select an option', ...props }) => {
  const [field, meta] = useField(props);

  return (
    <div className="">
      <label className="block text-base font-medium mb-1 text-gray-700">
        {label}{required && <span className="text-red-500">*</span>}
      </label>
      <select
        {...field}
        // {...props}
        className={`w-full px-3 py-2.5 border ${meta.touched && meta.error ? 'border-red-500' : 'border-gray-300'} rounded-lg focus:outline-none focus:ring-1 focus:ring-yellow-500 max-h-60`}
      >
        <option value="" className='text-gray-500'>{placeholder}</option>
        {options.map((option, idx) => (
          <option key={idx} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-xs mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export default FormSelect;
