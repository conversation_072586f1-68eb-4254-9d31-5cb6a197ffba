'use client';

import NewTablePage from "@/components/form/NewTablePage";
import useDataFetch from "@/hooks/useDataFetch";
import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import api from '@/lib/api';
import { useRouter } from 'next/navigation';

const approvalLists = () => {
  const router = useRouter();
  
  // Handle back button click - moved inside the component
  const handleBackClick = () => {
    router.back();
  };
  
  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  
  // Set a reasonable limit that the API accepts and filter by approved status
  const [queryParams, setQueryParams] = useState({
    page: 1,
    limit: 10,
    status: 'approved'
  });
  
  const [itemStatuses, setItemStatuses] = useState({});
  
  // Update query params when pagination changes
  useEffect(() => {
    setQueryParams(prev => ({
      ...prev,
      page: currentPage,
      limit: rowsPerPage
    }));
  }, [currentPage, rowsPerPage]);
  
  // Fetch tutor approval data using the custom hook
  const {
    data: response,
    isLoading,
    error,
    refetch
  } = useDataFetch({
    queryKey: ['tutor-approval', queryParams],
    endPoint: '/tutor-approval',
    params: queryParams
  });

  // Extract items data
  const items = response?.items || [];
  
  // Function to get total count handling different response structures
  const getTotalCount = () => {
    if (!response) return 0;
    
    if (response.meta?.totalItems !== undefined) {
      return response.meta.totalItems;
    }
    
    if (response.totalCount !== undefined) {
      return response.totalCount;
    }
    
    return (response.items || []).length;
  };
  
  // Extract metadata for pagination
  const totalItems = getTotalCount();
  const totalPages = response?.meta?.totalPages || Math.ceil(totalItems / rowsPerPage) || 1;
  
  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Log the response to debug
  useEffect(() => {
    console.log("API Response:", response);
  }, [response]);

  // Initialize statuses from fetched data
  useEffect(() => {
    if (items.length > 0) {
      const initialStatuses = {};
      items.forEach(item => {
        initialStatuses[item.id] = item.status || 'pending';
      });
      setItemStatuses(prev => ({...prev, ...initialStatuses}));
    }
  }, [items]);
  
  const handleAssign = (row) => {
    // Store the tutor data in localStorage before navigating
    localStorage.setItem('selectedTutor', JSON.stringify({
      id: row.id,
      name: row.user?.name || row.name || 'N/A',
      email: row.user?.email || row.email || 'N/A',
      phoneNumber: row.user?.phoneNumber || row.phoneNumber || 'N/A'
      // Add any other fields you need
    }));
    
    console.log("Storing tutor data:", row);
    
    // Navigate to the assign task page
    router.push('/dashboard/member-management/assign-task');
  };
  
  const preparedData = items.map(item => {
    // Extract user fields to the top level for the table display
    const preparedItem = { 
      ...item,
      name: item.user?.name || 'N/A',
      email: item.user?.email || 'N/A',
      phoneNumber: item.user?.phoneNumber || 'N/A'
    };
    
    // FIXED: Pass the original item to handleAssign, not preparedItem
    preparedItem.action = (
      <button 
        className="bg-[#FFDE34] text-white px-4 py-1 rounded text-sm font-bold"
        onClick={() => handleAssign(item)} // Pass the original item, not preparedItem
      >
        Assign
      </button>
    );
    
    return preparedItem;
  });

  const tutorColumns = [
    {
      label: 'TUTOR NAME',
      field: 'name',
    },
    {
      label: 'USER ID',
      field: 'userId',
    },
    {
      label: 'EMAIL ADDRESS',
      field: 'email',
    },
    {
      label: 'PHONE NUMBER',
      field: 'phoneNumber',
    },
    {
      label: 'ACTIONS',
      field: 'action',
    }
  ];

  if (error) {
    return (
      <div className="p-4 text-red-600">
        Error loading data: {error.message}
      </div>
    );
  }

  return (
    <div className="w-full px-4">
      <div className="overflow-auto max-h-[80vh]">
        <NewTablePage
          showSearch={false}
          showNameFilter={false}
          showSortFilter={false}
          showCreateButton={false}
          title="Approval Tutors"
          data={preparedData}
          columns={tutorColumns}
          loading={isLoading}
          onBack={handleBackClick}
          
          // Pagination props
          currentPage={currentPage}
          totalPages={totalPages}
          changePage={handlePageChange}
          totalItems={totalItems}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          hideTableFooter={false}
        />
      </div>
    </div>
  );
};

export default approvalLists;