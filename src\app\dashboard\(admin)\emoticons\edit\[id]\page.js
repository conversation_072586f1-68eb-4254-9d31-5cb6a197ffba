'use client';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { Icon } from '@iconify/react';
import FormSelect from '@/components/form/FormSelect';
import GenerateID from '@/components/form/GenerateID';
import FormInput from '@/components/form/FormInput';
import useDataFetch from '@/hooks/useDataFetch';
import FormRadio from '@/components/form/FormRadio';
import NumberInput from '@/components/form/NumberInput';
import api from '@/lib/api';
import RegularGoBack from '@/components/shared/RegularGoBack';
import { useParams, useRouter } from 'next/navigation';
import { queryClient } from '@/lib/queryClient';

const validationSchema = Yup.object().shape({
  itemNumber: Yup.string().required('Please enter an item number'),
  title: Yup.string().required('Please enter a title'),
  description: Yup.string().required('Please enter a description'),
  categoryId: Yup.string().required('Please select a category'),
  type: Yup.string().required('Please select an item type'),
  price: Yup.number().when('type', {
      is: 'in_app_purchase',
      then: (schema) => schema.required('Please enter a price'),
      otherwise: (schema) => schema.notRequired(),
    }),
  filePath: Yup.mixed().required('Please upload an image'),
  isActive: Yup.boolean().required('Please select status'),
  isFeatured: Yup.boolean().required('Featured status is required'),
  promotionId: Yup.string().matches(
    /^[0-9a-f]{8}-[0-9a-f]{4}-[4][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
    'Must be a valid UUID'
  ),
  discountedPrice: Yup.number(),
  metadata: Yup.object().shape({
    tags: Yup.array().of(Yup.string()),
    colors: Yup.array().of(Yup.string()),
  }),
});

const itemTypeOptions = [
  { value: 'in_app_purchase', label: 'In app purchase' },
  { value: 'free', label: 'Free' },
];

const activeStatusOptions = [
  { value: true, label: 'Yes' },
  { value: false, label: 'No' },
];

const featuredItemOptions = [
  { value: true, label: 'Yes' },
  { value: false, label: 'No' },
];

const promotionalItemOptions = [
  { value: true, label: 'Yes' },
  { value: false, label: 'No' },
];

const EditEmoticon = () => {
  const { id } = useParams();
  const router = useRouter();


  const { data: shopDetails, isLoading } = useDataFetch({
    queryKey: 'shop-details',
    endPoint: `/admin/shop/items/${id}`,
  });

  const [imagePreview, setImagePreview] = useState(null);
  const [tagInput, setTagInput] = useState('');

  useEffect(() => {
    if (shopDetails?.filePath) {
      setImagePreview(shopDetails.filePath);
    }
  }, [shopDetails]);

  const { data: categories, isLoading: isCategoriesLoading } = useDataFetch({
    queryKey: 'shop-all-categories',
    endPoint: '/admin/shop/categories',
  });

  // First, add state to fetch promotions
  const { data: promotions } = useDataFetch({
    queryKey: 'promotions',
    endPoint: '/promotions/admin',
  });

  //   console.log(categories);

  const handleImageChange = (event, setFieldValue) => {
    const file = event.target.files[0];
    if (file) {
      setFieldValue('filePath', file);
      setImagePreview(URL.createObjectURL(file));
    }
  };

  const removeImage = (setFieldValue) => {
    setFieldValue('filePath', null);
    setImagePreview(null);
  };

  const handleAddTag = (setFieldValue, values) => {
    if (tagInput.trim() && !values.tags.includes(tagInput.trim())) {
      setFieldValue('tags', [...values.tags, tagInput.trim()]);
      setTagInput('');
    }
  };

  const handleKeyDown = (e, setFieldValue, values) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag(setFieldValue, values);
    }
  };

  const removeTag = (tagToRemove, setFieldValue, values) => {
    setFieldValue(
      'tags',
      values.tags.filter((tag) => tag !== tagToRemove)
    );
  };

  return (
    <div className="shadow-md rounded-lg">
      <div className="flex justify-between items-center p-5 bg-gray-50">
        <h2 className="sm:text-xl font-[500]">Emoticon Management</h2>
      </div>

      <div className="p-5">
        <RegularGoBack className={'pb-5 text-lg max-w-32'} />

        <Formik
          enableReinitialize={true}
          initialValues={{
            itemNumber: shopDetails?.itemNumber || '',
            title: shopDetails?.title || '',
            description: shopDetails?.description || '',
            categoryId: shopDetails?.categoryId || '',
            type: shopDetails?.type || 'in_app_purchase',
            price: shopDetails?.price || '',
            filePath: shopDetails?.filePath || null,
            isActive: shopDetails?.isActive || true,
            isFeatured: shopDetails?.isFeatured || false,
            promotionalItem: shopDetails?.promotionalItem || false,
            promotionId: shopDetails?.promotionId || '',
            discountedPrice: shopDetails?.discountedPrice || '',
            metadata: {
              tags: shopDetails?.metadata?.tags || [],
              colors: shopDetails?.metadata?.colors || [],
            },
          }}
          validationSchema={validationSchema}
          onSubmit={async (values, { setSubmitting }) => {
            const formData = new FormData();

            // Handle file upload
            if (values.filePath instanceof File) {
              formData.append('file', values.filePath);
            }

            // Handle metadata if it has any values
            if (
              values.metadata?.tags?.length > 0 ||
              values.metadata?.colors?.length > 0
            ) {
              formData.append('metadata', JSON.stringify(values.metadata));
            }

            // Handle other fields - exclude promotionalItem from being sent
            const { promotionalItem, filePath, metadata, ...submitValues } =
              values;

            // Only append fields that have values (not null, undefined, or empty string)
            Object.entries(submitValues).forEach(([key, value]) => {
              if (value !== null && value !== undefined && value !== '') {
                formData.append(key, value);
              }
            });

            try {
              const response = await api.patch(
                `/admin/shop/items/${id}`,
                formData,
                {
                  headers: {
                    'Content-Type': 'multipart/form-data',
                  },
                }
              );
              console.log(response);
              router.push('/dashboard/emoticons');
              queryClient.invalidateQueries('/admin/shop/items');
            } catch (error) {
              console.log(error);
            } finally {
              setSubmitting(false);
            }
          }}
        >
          {({ setFieldValue, values, errors, isSubmitting }) => (
            <Form className="space-y-6 flex gap-10">
              <div className="space-y-2 flex-1">
                <div className="border-2 border-dashed border-gray-200 rounded-lg p-8 ">
                  {/* Hidden file input */}
                  <input
                    type="file"
                    name="filePath"
                    accept="image/*"
                    onChange={(e) => handleImageChange(e, setFieldValue)}
                    className="hidden"
                    id="fileInput"
                  />

                  {/* Preview Area */}
                  <div
                    className={`border-2 ${
                      !imagePreview ? 'border-red-500' : 'border-gray-200'
                    } rounded-lg p-8 min-h-60 max-h-96 flex items-center justify-center text-center bg-[#FFFCF5]`}
                  >
                    {!imagePreview ? (
                      <div
                        className="flex flex-col items-center cursor-pointer"
                        onClick={() =>
                          document.getElementById('fileInput').click()
                        }
                      >
                        <Icon
                          icon="solar:gallery-wide-linear"
                          className="w-12 h-12 text-gray-400 mb-2"
                        />
                        <div>
                          <span className="text-yellow-500">Upload a file</span>
                          <span> or drag and drop</span>
                        </div>
                        <p className="text-sm text-gray-500 mt-1">
                          PNG, JPG, SVG up to 10MB
                        </p>
                      </div>
                    ) : (
                      <div className="relative">
                        <Image
                          src={imagePreview}
                          alt="Preview"
                          width={300}
                          height={200}
                          className="mx-auto"
                        />
                      </div>
                    )}
                  </div>
                  {errors?.filePath && (
                    <p className="text-red-500 text-sm mt-1">
                      {errors.filePath}
                    </p>
                  )}

                  {/* Image Actions */}
                  <div className="flex items-center justify-between gap-2 mt-2">
                    <span className="text-sm text-gray-500">
                      Maximum file size: 10MB
                    </span>
                    <div className="flex gap-2">
                      <button
                        type="button"
                        className="px-3 py-1 text-sm bg-yellow-400 rounded-md flex items-center gap-1"
                        onClick={() =>
                          document.getElementById('fileInput').click()
                        }
                      >
                        <Icon
                          icon="garden:reload-stroke-12"
                          className="w-4 h-4"
                        />
                        Change
                      </button>
                      <button
                        type="button"
                        className="px-3 py-1 text-sm bg-red-100 text-red-600 rounded-md flex items-center gap-1"
                        onClick={() => removeImage(setFieldValue)}
                      >
                        <Icon
                          icon="solar:trash-bin-trash-linear"
                          className="w-4 h-4"
                        />
                        Remove
                      </button>
                    </div>
                  </div>
                </div>

                {/* Tags Section */}
                <div className="space-y-2">
                  <label className="text-sm">Tags</label>
                  <div className="border-2 border-dashed border-gray-200 rounded-lg p-3">
                    <div className="flex flex-wrap gap-2">
                      {values?.metadata?.tags?.map((tag) => (
                        <span
                          key={tag}
                          className="bg-yellow-100 px-3 py-1.5 rounded-md flex items-center gap-2"
                        >
                          {tag}
                          <Icon
                            icon="bitcoin-icons:cross-filled"
                            className="w-4 h-4 cursor-pointer hover:text-red-500"
                            onClick={() => {
                              setFieldValue(
                                'metadata.tags',
                                values.metadata.tags.filter((t) => t !== tag)
                              );
                            }}
                          />
                        </span>
                      ))}
                      <input
                        type="text"
                        value={tagInput}
                        onChange={(e) => setTagInput(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            if (
                              tagInput.trim() &&
                              !values.metadata.tags.includes(tagInput.trim())
                            ) {
                              setFieldValue('metadata.tags', [
                                ...values.metadata.tags,
                                tagInput.trim(),
                              ]);
                              setTagInput('');
                            }
                          }
                        }}
                        placeholder="Write tags"
                        className="border-none outline-none text-sm flex-grow min-w-[100px]"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-6 flex-1">
                <div className="grid gap-6">
                  {/* Left Column */}
                  <div className="space-y-4">
                    <h2 className="text-lg font-semibold text-center">
                      Dummy Data
                    </h2>
                    {/* <div>
                      <FormSelect
                        label="Item Category"
                        name="categoryId"
                        options={
                          categories?.map((category) => ({
                            value: category.id,
                            label: category.name,
                          })) || []
                        }
                        required
                      />
                    </div>

                    <div>
                      <GenerateID
                        name="itemNumber"
                        categoryId={values?.categoryId}
                      />
                    </div> */}

                    <div>
                      <FormInput
                        label="Item Title"
                        name="title"
                        placeholder="Write shop title"
                        required
                      />
                    </div>
                  </div>

                  {/* Right Column */}
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <FormRadio
                        label="Item Type"
                        name="type"
                        options={itemTypeOptions}
                        isHorizontal={true}
                        required
                      />

                      <FormRadio
                        label="Active Status"
                        name="isActive"
                        options={activeStatusOptions}
                        isHorizontal={true}
                        required
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <FormRadio
                        label="Featured Item?"
                        name="isFeatured"
                        options={featuredItemOptions}
                        isHorizontal={true}
                      />

                      <FormRadio
                        label="Promotional Item?"
                        name="promotionalItem"
                        options={promotionalItemOptions}
                        isHorizontal={true}
                      />
                    </div>

                    {values?.promotionalItem && (
                      <FormSelect
                        label="Select Promotion"
                        name="promotionId"
                        options={
                          promotions?.map((promo) => ({
                            value: promo.id,
                            label: promo.name,
                          })) || []
                        }
                        placeholder="Select a promotion"
                        required
                      />
                    )}
                  </div>
                </div>

                {values.type === 'in_app_purchase' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <NumberInput
                      label="Regular Price"
                      name="price"
                      placeholder="Enter regular price"
                      required
                    />
                    <NumberInput
                      label="Sale Price"
                      name="discountedPrice"
                      placeholder="Enter sale price"
                      // required
                    />
                  </div>
                )}

                <div>
                  <FormInput
                    isTextarea={true}
                    label="Item Descriptions"
                    name="description"
                    placeholder="Write item descriptions"
                    required
                  />
                </div>

                {/* Form Actions */}
                <div className="flex justify-end gap-3">
                  <button
                    type="button"
                    className="px-6 py-2 bg-gray-200 rounded-lg"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-6 py-2 bg-yellow-400 rounded-lg"
                  >
                    {isSubmitting ? 'Submitting...' : 'Submit'}
                  </button>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default EditEmoticon;
