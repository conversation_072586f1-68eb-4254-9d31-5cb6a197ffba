'use client';

import React from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { Icon } from '@iconify/react';

/**
 * HecSubmissionLayout - A common layout component for HEC submissions (Diary, Essay, Q&A, etc.)
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render in the content area
 * @param {string} props.activeTab - The currently active tab
 * @param {Array} props.tabs - Array of tab objects with name and value properties
 * @param {string} props.title - The title to display in the header
 * @param {string} props.basePath - The base path to navigate to when tabs are clicked or back button is pressed
 */
const HecSubmissionLayout = ({ 
  children, 
  activeTab, 
  tabs = [],
  title = 'HEC Submission',
  basePath = '/dashboard'
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const handleTabChange = (tab) => {
    // If we're already on a review page, navigate back to the main page
    if (pathname.includes('/review/')) {
      const params = new URLSearchParams();
      params.set('tab', tab);
      router.push(`${basePath}?${params.toString()}`);
    } else {
      // Otherwise, just update the tab parameter
      const params = new URLSearchParams(searchParams);
      params.set('tab', tab);
      router.push(`${pathname}?${params.toString()}`);
    }
  };

  const handleBack = () => {
    router.push(basePath);
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header with Back Button */}
      <div className="flex items-center mb-6">
        <button
          onClick={handleBack}
          className="flex items-center text-gray-600 hover:text-yellow-600 mr-4"
        >
          <Icon icon="eva:arrow-back-fill" className="w-6 h-6" />
        </button>
        <h1 className="text-2xl font-semibold">{title}</h1>
      </div>

      {/* Main Content Area with Sidebar and Content */}
      <div className="flex flex-1">
        {/* Sidebar */}
        <div className="w-64 bg-[#FEFCE8] p-4 flex flex-col rounded-lg border border-[#FFDE34]">
          {tabs.map((tab, index) => (
            <button
              key={index}
              className={`mb-2 p-2 text-left rounded-md ${
                activeTab === tab.value ? 'bg-[#FFDE34] text-black' : 'hover:bg-gray-200'
              }`}
              onClick={() => handleTabChange(tab.value)}
            >
              {tab.name}
            </button>
          ))}
        </div>

        {/* Content Area */}
        <div className="flex-1 px-6">
          {children}
        </div>
      </div>
    </div>
  );
};

export default HecSubmissionLayout;
