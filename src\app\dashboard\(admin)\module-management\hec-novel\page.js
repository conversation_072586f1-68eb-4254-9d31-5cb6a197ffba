'use client';

import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import CreateNoveLTopic from './novel-topic/page';
import CreateOpenProject from './create-open-project/page';
import NovelTopicList from './suggested-novel-topic-list/page';
import OpenProjectList from './project-list/page';
import AwardList from '../hec-essay/award-list/page';
import { useSearchParams } from 'next/navigation';


const HecNovel = () => {
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState('novelTopicList'); // Default tab



  const handleBackClick = () => {
    window.history.back();
  };

  const handleTabChange = (tabName) => {
    setActiveTab(tabName);
    
    const newUrl = `${window.location.pathname}?tab=${tabName}`;
    window.history.pushState({ path: newUrl }, '', newUrl);
  };

  const renderContent = () => {
    switch(activeTab) {
      case 'createNovelTopic':
        return <CreateNoveLTopic />;
      case 'novelSubmissionList':
        return <NovelSubmissionList />;
      case 'createOpenProject':
        return <CreateOpenProject />;
      case 'novelTopicList':
        return <NovelTopicList />;
      case 'openProjectList':
        return <OpenProjectList />;
      case 'awardList':
        return <AwardList />;
      default:
        return <div>Select a tab</div>;
    }
  };

  return (
    <div className="flex flex-col h-screen">
      <div className="p-4 bg-white shadow-sm flex items-center">
        <button
          onClick={handleBackClick}
          className="p-2 rounded-full hover:bg-gray-200 mr-4"
        >
          <Icon icon="mdi:arrow-left" className="text-xl" />
        </button>
        <div className="text-xl">
          HEC NOVEL
        </div>
      </div>

      <div className="flex flex-1">
        {/* Sidebar */}
        <div className="w-64 bg-[#FEFCE8] p-4 flex flex-col rounded-lg">
          <button
            className={`mb-2 p-2 text-left ${activeTab === 'novelTopicList' ? 'bg-[#FFDE34] text-black' : 'hover:bg-gray-200'}`}
            onClick={() => handleTabChange('novelTopicList')}
          >
           Suggested  Novel Topic List
          </button>
          
          <button
            className={`mb-2 p-2 text-left ${activeTab === 'createNovelTopic' ? 'bg-[#FFDE34] text-black' : 'hover:bg-gray-200'}`}
            onClick={() => handleTabChange('createNovelTopic')}
          >
            Create Novel Topic
          </button>
          
         
          
          <button
            className={`mb-2 p-2 text-left ${activeTab === 'openProjectList' ? 'bg-[#FFDE34] text-black' : 'hover:bg-gray-200'}`}
            onClick={() => handleTabChange('openProjectList')}
          >
            Project List
          </button>
          
          <button
            className={`mb-2 p-2 text-left ${activeTab === 'createOpenProject' ? 'bg-[#FFDE34] text-black' : 'hover:bg-gray-200'}`}
            onClick={() => handleTabChange('createOpenProject')}
          >
            Create Project
          </button>

          <button
            className={`mb-2 p-2 text-left ${activeTab === 'awardList' ? 'bg-[#FFDE34] text-black' : 'hover:bg-gray-200'}`}
            onClick={() => handleTabChange('awardList')}
          >
            Award List
          </button>
        </div>
        
        {/* Content Area */}
        <div className="flex-1 p-6">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default HecNovel;