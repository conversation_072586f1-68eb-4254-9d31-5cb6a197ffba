'use client';
import React from 'react';
import { DraggableOption } from '../../../waterfall/play/WaterfallDragDrop';
import Image from 'next/image';
import {
  DndContext,
  KeyboardSensor,
  PointerSensor,
  rectIntersection,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { restrictToWindowEdges } from '@dnd-kit/modifiers';

const WordBlock = ({
  data,
  id,
  option,
  isActive,
  index,
  totalOptions,
  activeId,
  setActiveId,
  setActiveOption,
}) => {
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor)
  );

  const handleDragStart = (event) => {
    const { active } = event;
    setActiveId(active.id);

    // If dragging from options
    if (active.id.startsWith('option-')) {
      const optionIndex = parseInt(active.id.split('-')[1]);
      setActiveOption(options[optionIndex].text);
    }
    // If dragging from blanks (allowing rearrangement)
    else if (active.id.startsWith('blank-')) {
      const blankIndex = parseInt(active.id.split('-')[1]);
      if (blanks[blankIndex]) {
        setActiveOption(blanks[blankIndex]);
      }
    }
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      // If dropping onto a blank
      if (over.id.startsWith('blank-')) {
        const blankIndex = parseInt(over.id.split('-')[1]);
        const newBlanks = [...blanks];

        // If dragging from options
        if (active.id.startsWith('option-')) {
          const optionIndex = parseInt(active.id.split('-')[1]);
          const newOptions = [...options];

          // Check if the blank already has a value
          const currentBlankValue = newBlanks[blankIndex];

          // If the blank already has a value, we need to free up that option first
          if (currentBlankValue !== null) {
            const currentOptionIndex = newOptions.findIndex(
              (opt) => opt.text === currentBlankValue
            );

            if (currentOptionIndex !== -1) {
              newOptions[currentOptionIndex] = {
                ...newOptions[currentOptionIndex],
                used: false,
              };
            }
          }

          // Count how many blanks are already filled
          const filledBlanksCount = blanks.filter(
            (blank) => blank !== null
          ).length;

          // If this is a new option (not replacing an existing one in a blank)
          // and we've already reached the maximum number of blanks, don't allow it
          if (
            currentBlankValue === null &&
            filledBlanksCount >= blanks.length
          ) {
            // Don't allow more selections than blanks
            console.log('Maximum number of selections reached');
            setActiveId(null);
            setActiveOption(null);
            return;
          }

          // Update the blank with the option text
          newBlanks[blankIndex] = newOptions[optionIndex].text;

          // Mark the option as used
          newOptions[optionIndex] = {
            ...newOptions[optionIndex],
            used: true,
          };

          setOptions(newOptions);
        }
        // If dragging from another blank (swap)
        else if (active.id.startsWith('blank-')) {
          const sourceBlankIndex = parseInt(active.id.split('-')[1]);
          const temp = newBlanks[sourceBlankIndex];
          newBlanks[sourceBlankIndex] = newBlanks[blankIndex];
          newBlanks[blankIndex] = temp;
        }

        setBlanks(newBlanks);

        // Check if all blanks are filled
        const allFilled = newBlanks.every((blank) => blank !== null);
        setIsComplete(allFilled);

        // Check if answers are correct
        if (allFilled) {
          const isAnswerCorrect = checkAnswers(newBlanks);
          setIsCorrect(isAnswerCorrect);
        }
      }
      // If dropping back to options area (to unuse an option)
      else if (
        over.id === 'options-container' &&
        active.id.startsWith('blank-')
      ) {
        const blankIndex = parseInt(active.id.split('-')[1]);
        const blankValue = blanks[blankIndex];

        // Find the option that matches this value and mark it as unused
        const newOptions = [...options];
        const optionIndex = newOptions.findIndex(
          (opt) => opt.text === blankValue
        );

        if (optionIndex !== -1) {
          newOptions[optionIndex] = {
            ...newOptions[optionIndex],
            used: false,
          };
          setOptions(newOptions);
        }

        // Clear the blank
        const newBlanks = [...blanks];
        newBlanks[blankIndex] = null;
        setBlanks(newBlanks);
        setIsComplete(false);
      }
    }

    setActiveId(null);
    setActiveOption(null);
  };

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={rectIntersection} // Use rectIntersection for more precise collision detection
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      modifiers={[restrictToWindowEdges]}
    >
      <div className="bg-[#E7AB40] p-4 rounded-lg rounded-tl-2xl relative ">
        <Image
          src={'/assets/images/all-img/animalIcon1.png'}
          alt={'block'}
          width={300}
          height={300}
          className="max-w-[100px] absolute -top-12 -left-12"
        />

        <div className="bg-white rounded-lg min-h-20 flex items-center gap-10">
          {data?.map((item, idx) => (
            <DraggableOption
              key={item?.id}
              id={item?.id}
              option={item?.word || 'Demo Text'}
              isActive={activeId === item?.id}
              totalOptions={totalOptions}
            />
          ))}
        </div>
      </div>
    </DndContext>
  );
};

export default WordBlock;
