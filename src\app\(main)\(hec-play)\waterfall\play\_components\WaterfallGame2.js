'use client';
import React, { useRef } from 'react';
import { CSS } from '@dnd-kit/utilities';
import { useDraggable, useDroppable } from '@dnd-kit/core';
import { motion } from 'framer-motion';
import { Icon } from '@iconify/react';
import Image from 'next/image';

// Draggable option component
const DraggableOption = ({ id, option, isActive, index, totalOptions }) => {
  // Calculate position based on a grid layout with better spacing
  const calculatePosition = () => {
    // Create a more spaced out grid to avoid overlaps
    const columns = Math.ceil(Math.sqrt(totalOptions + 2)); // Add buffer
    const rows = Math.ceil(totalOptions / columns);

    // Calculate which row and column this option should be in
    const row = Math.floor(index / columns);
    const col = index % columns;

    // Define cell dimensions with more spacing
    const cellWidth = 90 / columns; // Leave some margin
    const cellHeight = 180 / rows;

    // Add minimal randomness to avoid perfect alignment but prevent overlaps
    const xOffset = (Math.random() * 0.3 - 0.15) * cellWidth;
    const yOffset = (Math.random() * 0.3 - 0.15) * cellHeight;

    // Calculate final position with margins to prevent edge overlaps
    const x = 5 + col * cellWidth + cellWidth * 0.5 + xOffset; // 5% margin from left
    const y = 30 + row * cellHeight + cellHeight * 0.5 + yOffset; // Start from 30px down

    return { x, y };
  };

  const stableValues = useRef({
    position: calculatePosition(),
    delay: index * 0.15,
    duration: 1.5 + Math.random() * 1,
    rotation: Math.random() * 10 - 5,
    hasAnimated: false,
  });

  const { attributes, listeners, setNodeRef, transform, isDragging } =
    useDraggable({
      id: id,
      data: { option },
    });

  const style = transform
    ? {
        transform: CSS.Translate.toString(transform),
        touchAction: 'none',
        zIndex: 10,
      }
    : {
        touchAction: 'none',
      };

  if (isDragging || isActive) {
    return null; // Hide the original when dragging
  }

  return (
    <motion.div
      initial={{
        y: -50,
        opacity: 0,
        rotate: stableValues.current.rotation * 2,
      }}
      animate={{
        y: stableValues.current.position.y,
        opacity: 1,
        rotate: stableValues.current.rotation,
      }}
      transition={{
        type: 'spring',
        duration: stableValues.current.duration,
        delay: stableValues.current.delay,
        bounce: 0.2,
        repeat: 0,
      }}
      className="absolute"
      style={{
        left: `${stableValues.current.position.x}%`,
        position: 'absolute',
      }}
    >
      <div
        ref={setNodeRef}
        style={style}
        {...listeners}
        {...attributes}
        className="bg-yellow-100 border border-yellow-300 rounded-md px-3 py-1 cursor-grab shadow-sm hover:shadow-md transition-shadow text-base whitespace-nowrap"
      >
        {option}
      </div>
    </motion.div>
  );
};

// Droppable blank component
const DroppableBlank = ({ id, value, index, onReset, correctAnswers }) => {
  const { isOver, setNodeRef, active } = useDroppable({
    id: id,
    data: { index },
  });

  // Determine if the currently dragged option is correct for this blank
  const isCorrectOption = () => {
    if (!active || !correctAnswers) return null;

    // Extract the option index from the active id (format: "option-X")
    const draggedOptionIndex = active.id.startsWith('option-')
      ? parseInt(active.id.split('-')[1])
      : null;

    if (draggedOptionIndex === null) return null;

    // Check if the option being dragged matches the correct answer for this blank
    return correctAnswers[index] === active.data.current?.option;
  };

  // Determine border color based on validation
  const getBorderColor = () => {
    if (!isOver) {
      return value
        ? 'border-yellow-300 bg-yellow-100'
        : 'border-yellow-300 border-dashed bg-yellow-50';
    }

    const validationResult = isCorrectOption();

    // If we're dragging but can't determine correctness, use default hover style
    if (validationResult === null) {
      return 'border-yellow-500 bg-yellow-100';
    }

    // Show green for correct, red for incorrect
    return validationResult
      ? 'border-green-500 bg-green-50'
      : 'border-red-500 bg-red-50';
  };

  return (
    <div className="inline-flex items-center relative">
      <div
        ref={setNodeRef}
        className={`inline-flex items-center justify-center min-w-16 h-8 mx-1 border-2 ${getBorderColor()} rounded-md px-2 transition-colors duration-200`}
      >
        {value || ''}
      </div>

      {/* Reset button for individual blank */}
      {value && onReset && (
        <button
          onClick={() => onReset(index)}
          className="absolute -right-2 -top-2 bg-white rounded-full w-5 h-5 flex items-center justify-center text-gray-500 hover:text-red-500 shadow-sm border border-gray-200"
          title="Remove this answer"
        >
          <Icon icon="mdi:close" width={12} />
        </button>
      )}
    </div>
  );
};

// Drag overlay component
const DragOverlayContent = ({ option }) => {
  return (
    <div className="bg-yellow-100 border border-yellow-300 rounded-md px-3 py-1 shadow-md text-sm">
      {option}
    </div>
  );
};

// Feedback component
const FeedbackComponent = ({
  button,
  isCorrect,
  correctAnswer,
  showFeedback,
}) => {
  return (
    <div>
      {/* {showFeedback && isCorrect && (
        <div>
          <Image
            src={'/assets/images/all-img/successGIF.gif'}
            alt={'cat'}
            width={200}
            height={200}
            className="mx-auto"
          />
        </div>
      )} */}

      <div
        className={`w-full min-h-32 bg-pink-50 p-4 rounded-lg border-pink-200 flex items-center ${
          showFeedback ? 'justify-between' : 'justify-center'
        } [box-shadow:-2px_-2px_8px_0px_#B7212626_inset] [box-shadow:2px_2px_12px_0px_#A92E0024_inset]`}
      >
        {showFeedback && (
          <div className="flex items-center">
            <div className="mr-4">
              <div className="w-28 h-28 bg-yellow-200 rounded-full flex items-center justify-center">
                <Image
                  src={
                    isCorrect
                      ? '/assets/images/all-img/correctImg.png'
                      : '/assets/images/all-img/wrongImg.png'
                  }
                  alt={'cat'}
                  width={200}
                  height={200}
                />
              </div>
            </div>
            <div>
              {isCorrect ? (
                <p className="text-green-600 text-2xl font-medium">
                  Great job! That's correct!
                </p>
              ) : (
                <div>
                  <p className="text-orange-500 font-medium">
                    Practice make you perfect !!!
                  </p>
                  <p className="text-gray-700 mt-1">
                    <span className="font-medium">Correct Answer: </span>
                    {correctAnswer}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
        {button}
      </div>
    </div>
  );
};

export {
  DraggableOption,
  DroppableBlank,
  DragOverlayContent,
  FeedbackComponent,
};
