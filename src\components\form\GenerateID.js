import api from '@/lib/api';
import React, { useEffect } from 'react';
import { useField, useFormikContext } from 'formik';

const GenerateID = ({ categoryId, name }) => {
  const { setFieldValue } = useFormikContext();
  const [field] = useField(name);
  
  const generateNumber = async () => {
    try {
      const response = await api.post('/admin/shop/items/generate-number', { categoryId}, {showSuccessToast: false});
      setFieldValue(name, response.data);
    } catch (error) {
      console.error('Error generating number:', error);
    }
  };

  useEffect(() => {
    if (categoryId && !field.value) {
      generateNumber();
    }
  }, [categoryId]);

  return (
    <div>
      <label className="block text-base font-[500] mb-1">ID</label>
      <div className="flex relative">
        <input
          type="text"
          {...field}
          className="w-full px-3 py-2 border rounded-lg bg-gray-100 focus:outline-none focus:ring-none"
          readOnly
        />
        <button 
          onClick={() => categoryId ? generateNumber() : console.log('please select a category')} 
          type="button" 
          className="absolute right-2 top-1/2 -translate-y-1/2 px-4 py-1 bg-yellow-400 rounded-lg min-w-32"
        >
          Re - Generate
        </button>
      </div>
    </div>
  );
};

export default GenerateID;
