'use client';
import React from 'react';
import { Icon } from '@iconify/react';
import Image from 'next/image';

const ViewModal = ({ isOpen, onClose, mission }) => {
  if (!isOpen || !mission) return null;

  // Format date to readable format
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[85vh] overflow-hidden">
        {/* Header */}
        <div className="bg-[#FFF9FB] py-4 px-6 border-b border-amber-200 relative">
          {/* Close Button */}
          <div className="absolute top-4 right-4 z-10">
                   <button
                     type="button"
                     onClick={onClose}
                     className="focus:outline-none"
                     aria-label="Close"
                   >
                     <Image 
                       src="/assets/images/all-img/cross-bg.png" 
                       alt="Close" 
                       width={40} 
                       height={40} 
                       className="w-10 h-10" 
                     />
                   </button>
                 </div>

          <div className="flex justify-center">
            <div className="relative w-full max-w-sm">
              <Image
                src="/assets/images/all-img/missiondiary-bg.png"
                alt="Mission Details"
                width={400}
                height={150}
                className="w-full h-auto"
                priority
              />
            </div>
          </div>
        </div>

        {/* Content Section - Scrollable */}
        <div className="px-6 py-4 max-h-[65vh] overflow-y-auto">
          <h1 className="text-xl font-semibold mb-4 text-gray-800 text-center">
            {mission.title}
          </h1>
          
          <div className="space-y-4">
            {/* Status Section */}
            <div className="flex items-center justify-between py-2 border-b border-gray-100">
              <div className="flex items-center">
                <Icon
                  icon={mission.isActive ? 'mdi:check-circle' : 'mdi:cancel'}
                  className={`w-5 h-5 mr-3 ${
                    mission.isActive ? 'text-green-500' : 'text-red-500'
                  }`}
                />
                <span className="font-medium text-gray-700">Status</span>
              </div>
              <span
                className={`px-3 py-1 rounded-full text-sm font-medium ${
                  mission.isActive
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}
              >
                {mission.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>

            {/* Word Count Section */}
            <div className="space-y-2">
              <h3 className="font-medium text-gray-700 flex items-center">
                <Icon icon="mdi:format-text" className="w-4 h-4 mr-2 text-blue-500" />
                Word Count Requirements
              </h3>
              <div className="ml-6 space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Minimum Words:</span>
                  <span className="font-medium text-gray-900">
                    {mission.targetWordCount || 'Not specified'}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Maximum Words:</span>
                  <span className="font-medium text-gray-900">
                    {mission.targetMaxWordCount || 'Not specified'}
                  </span>
                </div>
              </div>
            </div>

            {/* Timeline Section */}
            <div className="space-y-2">
              <h3 className="font-medium text-gray-700 flex items-center">
                <Icon icon="mdi:calendar-clock" className="w-4 h-4 mr-2 text-purple-500" />
                Timeline
              </h3>
              <div className="ml-6 space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Publish Date:</span>
                  <span className="font-medium text-gray-900">
                    {formatDate(mission.publishDate)}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Expiry Date:</span>
                  <span className="font-medium text-gray-900">
                    {formatDate(mission.expiryDate)}
                  </span>
                </div>
              </div>
            </div>

            {/* Score Section (if available) */}
            {mission.score !== undefined && (
              <div className="space-y-2">
                <h3 className="font-medium text-gray-700 flex items-center">
                  <Icon icon="mdi:star" className="w-4 h-4 mr-2 text-yellow-500" />
                  Score
                </h3>
                <div className="ml-6">
                  <span className="font-medium text-gray-900">
                    {mission.score}
                  </span>
                </div>
              </div>
            )}

            {/* Description Section */}
            <div className="space-y-2">
              <h3 className="font-medium text-gray-700 flex items-center">
                <Icon icon="mdi:text-box" className="w-4 h-4 mr-2 text-green-500" />
                Description
              </h3>
              <div className="ml-6">
                <p className="text-gray-900 leading-relaxed text-sm">
                  {mission.description || 'No description provided'}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewModal;