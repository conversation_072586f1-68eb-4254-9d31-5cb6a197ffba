import localFont from 'next/font/local';

// Define the Open Sans font with all necessary font files
export const openSans = localFont({
  src: [
    {
      path: './open-sans/OpenSans-Light.woff2',
      weight: '300',
      style: 'normal',
    },
    {
      path: './open-sans/OpenSans-LightItalic.woff2',
      weight: '300',
      style: 'italic',
    },
    {
      path: './open-sans/OpenSans-Regular.woff2',
      weight: '400',
      style: 'normal',
    },
    {
      path: './open-sans/OpenSans-Italic.woff2',
      weight: '400',
      style: 'italic',
    },
    {
      path: './open-sans/OpenSans-Medium.woff2',
      weight: '500',
      style: 'normal',
    },
    {
      path: './open-sans/OpenSans-MediumItalic.woff2',
      weight: '500',
      style: 'italic',
    },
    {
      path: './open-sans/OpenSans-SemiBold.woff2',
      weight: '600',
      style: 'normal',
    },
    {
      path: './open-sans/OpenSans-SemiBoldItalic.woff2',
      weight: '600',
      style: 'italic',
    },
    {
      path: './open-sans/OpenSans-Bold.woff2',
      weight: '700',
      style: 'normal',
    },
    {
      path: './open-sans/OpenSans-BoldItalic.woff2',
      weight: '700',
      style: 'italic',
    },
    {
      path: './open-sans/OpenSans-ExtraBold.woff2',
      weight: '800',
      style: 'normal',
    },
    {
      path: './open-sans/OpenSans-ExtraBoldItalic.woff2',
      weight: '800',
      style: 'italic',
    },
  ],
  display: 'swap',
  variable: '--font-open-sans',
});
