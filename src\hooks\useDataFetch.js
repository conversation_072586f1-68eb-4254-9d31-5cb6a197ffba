import api from '../lib/api';
import { useQuery } from '@tanstack/react-query';

// Updated useDataFetch hook
const useDataFetch = ({ queryKey, endPoint, params = {}, enabled = true, method = 'GET' }) => {
  return useQuery({
    queryKey: [queryKey, params],
    queryFn: async () => {
      const { id, ...otherParams } = params;

      try {
        const response = method === 'POST' 
          ? await api.post(endPoint, otherParams)
          : id
            ? await api.get(`${endPoint}/${id}`)
            : await api.get(endPoint, { params: otherParams });

        return response?.data;
      } catch (error) {
        throw new Error(
          error?.response?.data?.message || 'Data fetching error'
        );
      }
    },
    enabled,
  });
};

export default useDataFetch;
