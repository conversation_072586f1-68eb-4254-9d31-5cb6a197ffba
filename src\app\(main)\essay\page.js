'use client';

import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useSearchParams } from 'next/navigation';
import SkinPreview from '@/components/skin/SkinPreview';
import useDataFetch from '@/hooks/useDataFetch';

export default function WriteEssay() {
  const dispatch = useDispatch();
  const essayId = useSearchParams().get('essayId');

  // State for subject, body, and date
  const [subject, setSubject] = useState('my custom');
  const [body, setBody] = useState('my custom body content');
  const [date, setDate] = useState(() => new Date().toISOString().slice(0, 10)); // YYYY-MM-DD

  const { data: skinInfo, isLoading } = useDataFetch({
    queryKey: ['essay-skin-info', essayId],
    endPoint: `/student-essay/skins/${essayId}`,
    enabled: !!essayId,
  });

  if (isLoading) return <div>Loading...</div>;

  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0 my-8">
      {/* Example inputs for subject, body, and date */}
      <div className="mb-4">
        <input
          type="text"
          value={subject}
          onChange={(e) => setSubject(e.target.value)}
          placeholder="Subject"
          className="border p-2 mr-2"
        />
        <input
          type="date"
          value={date}
          onChange={(e) => setDate(e.target.value)}
          className="border p-2 mr-2"
        />
      </div>
      <textarea
        value={body}
        onChange={(e) => setBody(e.target.value)}
        placeholder="Body"
        className="border p-2 w-full mb-4"
        rows={4}
      />
      <SkinPreview
        skin={skinInfo?.moduleDefaultSkin.skin.templateContent}
        contentData={{
          subject,
          body,
          date,
        }}
      />
    </div>
  );
}
