'use client';
import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import api from '@/lib/api';

const DeleteModal = ({ isOpen, onClose, data, onSuccess, endPoint }) => {
  const [isDeleting, setIsDeleting] = useState(false);

  // Handle delete confirmation
  const handleDelete = async () => {
    if (!data?.id) return;

    try {
      setIsDeleting(true);
      await api.delete(endPoint);
      onSuccess && onSuccess();
      onClose();
    } catch (error) {
      console.error('Error deleting mission:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md overflow-hidden">

        <div className="p-6 text-center space-y-3">
          <div className="flex flex-col items-center text-red-500 text-center">
            <Icon icon="mdi:alert-circle" className="w-10 h-10 mr-3 " />
            <span className="text-lg lg:text-xl font-medium">Are you sure?</span>
          </div>

          <p className="text-gray-600 mb-6">
             Once you delete
            <span className="font-semibold">"{data?.title || 'this item'}"</span>,
            This action cannot be undone and all associated data will be permanently removed.
          </p>

          <div className="flex justify-center space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md"
              disabled={isDeleting}
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleDelete}
              className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-md flex items-center"
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <Icon icon="eos-icons:loading" className="animate-spin mr-2" />
                  Deleting...
                </>
              ) : (
                <>
                  <Icon icon="mdi:delete" className="mr-2" />
                  Delete
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeleteModal;