'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import NewTablePage from '@/components/form/NewTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import { Icon } from '@iconify/react';

const TodaysDiary = () => {
  const router = useRouter();

  const [searchTerm, setSearchTerm] = useState('');
  const [searchField, setSearchField] = useState('studentName');
  const [sortField, setSortField] = useState('createdAt');
  const [sortDirection, setSortDirection] = useState('DESC');
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const {
    data: diaryData,
    isLoading: loading,
    error
  } = useDataFetch({
    queryKey: ['tutor-diary-pending-reviews', currentPage, rowsPerPage, sortField, sortDirection],
    endPoint: '/tutor/diary/pending-reviews',
    params: {
      page: currentPage,
      limit: rowsPerPage,
      sortBy: sortField,
      sortDirection: sortDirection
    }
  });

  const submissions = diaryData?.items || [];
  const totalItems = diaryData?.totalCount || diaryData?.totalItems || 0;
  const totalPages = diaryData?.totalPages || 1;

  const handlePageChange = (page) => setCurrentPage(page);
  const handleSearch = (term) => {
    setSearchTerm(term);
    setCurrentPage(1);
  };
  const handleSearchFieldChange = (field) => {
    setSearchField(field);
    setCurrentPage(1);
  };
  const handleSort = (field, direction) => {
    setSortField(field);
    setSortDirection(direction);
  };
  const handleViewSubmission = (submission) => {
    router.push(`/dashboard/submission-management/hec-diary/review/${submission.id}?status=${submission.status}`);
  };

  const columns = [
    { field: '#', sortable: false },
    {
      field: 'studentName',
      label: 'USER NAME',
      sortable: false,
      cellRenderer: (value) => (
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-gray-200 mr-3 overflow-hidden">
            <div className="w-full h-full flex items-center justify-center text-gray-500">
              {value?.charAt(0)?.toUpperCase() || 'U'}
            </div>
          </div>
          <span>{value}</span>
        </div>
      )
    },
    {
      field: 'status',
      label: 'REVIEW STATUS',
      sortable: false,
      cellRenderer: (_, row) => {
        const isReviewed = row.status === 'reviewed';
        const isSubmitted = row.status === 'submit';
        const isNew = row.status === 'new';

        return (
          <div className="flex items-center">
            <span
              className={`px-3 py-1 rounded-full text-xs font-medium ${
                isReviewed
                  ? 'bg-green-100 text-green-800'
                  : isSubmitted
                  ? 'bg-red-100 text-red-800'
                  : 'bg-yellow-100 text-yellow-800'
              }`}
            >
              {isReviewed ? 'Reviewed' : isSubmitted ? 'Not Reviewed Yet' : 'Incomplete'}
            </span>
            {isSubmitted && <span className="ml-2 text-red-500">✕</span>}
            {isNew && <span className="ml-2 text-yellow-500">⏱</span>}
            {isReviewed && <span className="ml-2 text-green-500">✓</span>}
          </div>
        );
      }
    },
    {
      field: 'title',
      label: 'MESSAGE',
      sortable: false,
      cellRenderer: (value) => <div className="truncate max-w-xs">{value || 'No message'}</div>
    },
    {
      field: 'moduleLevel',
      label: 'STUDENT ATTEMPT',
      sortable: false,
      cellRenderer: (value) => <div className="text-center">{value || 0}</div>
    }
  ];

  const actions = [
    {
      icon: 'heroicons-outline:eye',
      className: (row) =>
        row.status !== 'new'
          ? 'text-blue-600 hover:text-blue-700 cursor-pointer'
          : 'text-gray-400 cursor-not-allowed',
      onClick: (row) => handleViewSubmission(row),
      condition: (row) => row.status !== 'new'
    }
  ];

  const searchFilterOptions = [
    { label: 'Student Name', value: 'studentName' },
    { label: 'Title', value: 'title' }
  ];

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      {error && (
        <div className="mb-4 p-4 bg-red-100 text-red-700 rounded-md">
          Error loading data: {error.message}
        </div>
      )}

      <NewTablePage
        columns={columns}
        data={submissions}
        actions={actions}
        loading={loading}
        title="Todays Diary"
        currentPage={currentPage}
        totalPages={totalPages}
        changePage={handlePageChange}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        showSearch={false}
        showNameFilter={false}
        showSortFilter={false}
        searchTerm={searchTerm}
        searchField={searchField}
        sortField={sortField}
        sortDirection={sortDirection}
        onSearch={handleSearch}
        onNameFilterChange={handleSearchFieldChange}
        onSort={handleSort}
        nameFilterOptions={searchFilterOptions}
      />
    </div>
  );
};

export default TodaysDiary;
