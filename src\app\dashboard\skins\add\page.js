'use client';
import { useRef, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import html2canvas from 'html2canvas';
import Canvas from '@/components/skin/Canvas';
import { setPreviewMode } from '@/store/features/canvasSlice';

export default function CreateSkin() {
  const canvasRef = useRef(null);
  const dispatch = useDispatch();

  // Ensure preview mode is off when component mounts
  useEffect(() => {
    dispatch(setPreviewMode(false));
  }, [dispatch]);

  // Function to capture the canvas as an image
  const captureCanvas = async () => {
    if (!canvasRef.current?.canvasRef?.current) return null;

    const canvasElement = canvasRef.current.canvasRef.current;

    try {
      // Hide selection border if any element is selected
      const selectedElements = canvasElement.querySelectorAll('.selected');
      const originalBorders = [];

      selectedElements.forEach(el => {
        originalBorders.push(el.style.border);
        el.style.border = 'none';
      });

      // Capture the canvas
      const canvas = await html2canvas(canvasElement, {
        backgroundColor: null,
        scale: 1,
        useCORS: true,
        allowTaint: true,
        logging: false
      });

      // Restore selection borders
      selectedElements.forEach((el, index) => {
        el.style.border = originalBorders[index];
      });

      // Convert to blob
      return new Promise(resolve => {
        canvas.toBlob(blob => {
          if (!blob) {
            console.error('Failed to create blob from canvas');
            resolve(null);
            return;
          }

          // Create a File object from the blob
          const file = new File([blob], 'skin-preview.png', { type: 'image/png' });
          resolve(file);
        }, 'image/png');
      });
    } catch (error) {
      console.error('Error capturing canvas:', error);
      return null;
    }
  };

  // Make the captureCanvas function available globally
  useEffect(() => {
    // Attach the function to the window object so it can be accessed by Topbar
    window.captureCanvasImage = captureCanvas;

    // Cleanup function
    return () => {
      delete window.captureCanvasImage;
    };
  }, []);

  return (
    <>
      <Canvas ref={canvasRef} />
    </>
  );
}
