'use client';
import { useRef, useEffect } from 'react';
import { Icon } from '@iconify/react';
import Image from 'next/image';

/**
 * ViewAwardModal Component
 *
 * A modal component for displaying award details.
 * Styled to match the wooden sign design pattern.
 *
 * @param {Object} props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to call when closing the modal
 * @param {Object} props.awardData - The award object containing details
 * @returns {JSX.Element|null} The ViewAwardModal component or null if not shown
 */
const ViewAwardModal = ({ isOpen, onClose, awardData }) => {
  const modalRef = useRef(null);

  useEffect(() => {
    if (!isOpen) return;

    // Handle escape key press
    const handleEscapeKey = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    // Handle click outside
    const handleClickOutside = (e) => {
      if (modalRef.current && !modalRef.current.contains(e.target)) {
        onClose();
      }
    };

    // Add event listeners
    document.addEventListener('keydown', handleEscapeKey);
    document.addEventListener('mousedown', handleClickOutside);

    // Prevent body scrolling when modal is open
    document.body.style.overflow = 'hidden';

    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.removeEventListener('mousedown', handleClickOutside);
      // Restore body scrolling when modal is closed
      document.body.style.overflow = 'auto';
    };
  }, [isOpen, onClose]);

  if (!isOpen || !awardData) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div
        ref={modalRef}
        className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden relative"
      >
        {/* Close button - Round orange/amber with X */}
        <div className="absolute top-4 right-4 z-10">
          <button
            type="button"
            onClick={onClose}
            className="focus:outline-none"
            aria-label="Close"
          >
            <Image
              src="/assets/images/all-img/cross-bg.png"
              alt="Close"
              width={40}
              height={40}
              className="w-10 h-10"
            />
          </button>
        </div>

        {/* Wooden Sign Header */}
        <div className="bg-[#FFF9FB] pt-6 pb-8 px-6 border-b border-amber-200">
          <div className="flex justify-center mb-2">
            <div className="relative w-full max-w-md">
              {/* Wooden sign background with award text overlay */}
              <div className="relative">
                <Image
                  src="/assets/images/all-img/awarddiary-bg.png"
                  alt="Award Details"
                  width={600}
                  height={200}
                  className="w-full h-auto"
                  priority
                />
              </div>
            </div>
          </div>
        </div>

        {/* Award Details Content */}
        <div className="px-8 py-6 overflow-y-auto max-h-[calc(90vh-300px)]">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
            <div className="flex flex-col space-y-1">
              <p className="text-amber-700 text-sm font-semibold uppercase tracking-wide">
                Award Title
              </p>
              <p className="text-gray-800 text-lg font-medium">
                {awardData.name}
              </p>
            </div>

            <div className="flex flex-col space-y-1">
              <p className="text-amber-700 text-sm font-semibold uppercase tracking-wide">
                Reward Points
              </p>
              <div className="flex items-center space-x-2">
                <Icon
                  icon="material-symbols:star"
                  className="text-yellow-500 text-xl"
                />
                <p className="text-gray-800 text-lg font-medium">
                  {awardData.rewardPoints}
                </p>
              </div>
            </div>

            <div className="flex flex-col space-y-1">
              <p className="text-amber-700 text-sm font-semibold uppercase tracking-wide">
                Created Date
              </p>
              <div className="flex items-center space-x-2">
                <Icon
                  icon="material-symbols:calendar-today"
                  className="text-amber-600 text-lg"
                />
                <p className="text-gray-800 text-lg">{awardData.createdAt}</p>
              </div>
            </div>

            <div className="flex flex-col space-y-1">
              <p className="text-amber-700 text-sm font-semibold uppercase tracking-wide">
                Last Updated
              </p>
              <div className="flex items-center space-x-2">
                <Icon
                  icon="material-symbols:update"
                  className="text-amber-600 text-lg"
                />
                <p className="text-gray-800 text-lg">{awardData.updatedAt}</p>
              </div>
            </div>
          </div>

          {/* Description and Criteria - Full width */}
          <div className="mt-8 space-y-6">
            <div className="bg-amber-50 rounded-lg p-4 border border-amber-200">
              <div className="flex items-center mb-3">
                <Icon
                  icon="material-symbols:description"
                  className="text-amber-600 text-xl mr-2"
                />
                <p className="text-amber-700 text-sm font-semibold uppercase tracking-wide">
                  Description
                </p>
              </div>
              <p className="text-gray-800 leading-relaxed">
                {awardData.description}
              </p>
            </div>
          </div>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-200 mx-8"></div>
      </div>
    </div>
  );
};

export default ViewAwardModal;
