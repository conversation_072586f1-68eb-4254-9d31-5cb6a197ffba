import { createSlice } from '@reduxjs/toolkit';
import api from '@/lib/api';
import Cookies from 'js-cookie';

const initialState = {
  isAuth: false,
  user: null,
  token: null,
  isLoading: false,
  error: null,
  goBackStep: 'login-role', // or 'register-role' depending on the page
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    loginStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    loginSuccess: (state, action) => {
      state.isLoading = false;
      state.isAuth = true;
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.error = null;
      // Set token in cookie
      if (action.payload.token) {
        Cookies.remove('token');
        Cookies.set('token', action.payload.token, { expires: 7, path: '/' });
      }
    },
    loginFailure: (state, action) => {
      state.isLoading = false;
      state.error = action.payload;
    },
    logout: (state) => {
      // Reset auth state
      state.user = null;
      state.token = null;
      state.error = null;
      state.isAuth = false;
      // Clear auth cookie
      Cookies.remove('token', { path: '/' });
    },
    setGoBackStep: (state, action) => {
      state.goBackStep = action.payload;
    },
  },
});

export const { loginStart, loginSuccess, loginFailure, logout, setGoBackStep } =
  authSlice.actions;
export default authSlice.reducer;

// Thunk for handling login
export const loginUser = (credentials) => async (dispatch) => {
  try {
    dispatch(loginStart());
    const response = await api.post('/login', credentials);

    dispatch(
      loginSuccess({
        user: response.user,
        token: response.token,
      })
    );
    return response;
  } catch (error) {
    dispatch(loginFailure(error?.response?.data?.message || 'Login failed'));
    throw error;
  }
};
