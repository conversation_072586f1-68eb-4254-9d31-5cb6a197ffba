import { io } from 'socket.io-client';
import { store } from '@/store/store';
import Cookies from 'js-cookie';
import {
  addNotification,
  updateNotificationReadStatus,
  setUnreadCount
} from '@/store/features/notificationSlice';

// Socket.io server URL
const SOCKET_URL = 'http://103.209.40.213:3010';

let socket = null;

/**
 * Initialize the Socket.io connection
 * @param {string} token - JWT token for authentication
 */
export const initializeSocket = (token) => {
  if (socket) {
    // If socket exists but disconnected, reconnect
    if (!socket.connected) {
      socket.connect();
    }
    return socket;
  }

  // Create new socket connection
  const authToken = token || Cookies.get('token');

  socket = io(SOCKET_URL, {
    auth: {
      token: authToken,
    },
    transports: ['websocket', 'polling'],
    reconnection: true,
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
  });

  // Set up event listeners
  setupSocketListeners();

  return socket;
};

/**
 * Set up event listeners for the socket
 */
const setupSocketListeners = () => {
  if (!socket) return;

  // Handle connection events
  socket.on('connect', () => {
    console.log('Socket connected');
  });

  socket.on('disconnect', (reason) => {
    console.log(`Socket disconnected: ${reason}`);
  });

  socket.on('connect_error', (error) => {
    console.error('Socket connection error:', error);
  });

  // Handle notification events
  socket.on('notification', (notification) => {
    console.log('New notification received:', notification);
    store.dispatch(addNotification(notification));

    // Show toast notification
    if (typeof window !== 'undefined' && window.Sooner) {
      window.Sooner.success({
        title: notification.title,
        description: notification.message,
        position: 'top-right',
        duration: 5000,
      });
    }
  });

  socket.on('notification_read', ({ notificationId, read }) => {
    console.log('Notification read status updated:', notificationId, read);
    store.dispatch(updateNotificationReadStatus({ notificationId, read }));
  });

  socket.on('notification_count', ({ count }) => {
    console.log('Unread notification count updated:', count);
    store.dispatch(setUnreadCount(count));
  });
};

/**
 * Disconnect the socket
 */
export const disconnectSocket = () => {
  if (socket) {
    socket.disconnect();
    console.log('Socket disconnected');
  }
};

/**
 * Get the socket instance
 * @returns {Object|null} Socket instance or null if not initialized
 */
export const getSocket = () => socket;

const socketService = {
  initializeSocket,
  disconnectSocket,
  getSocket,
};

export default socketService;
