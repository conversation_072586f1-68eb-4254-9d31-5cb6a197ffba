'use client';
import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import GoBack from '@/components/shared/GoBack';
import api from '@/lib/api';
import { toast } from 'sonner';
import useDataFetch from '@/hooks/useDataFetch';

const Friends = () => {
    const [activeTab, setActiveTab] = useState('friends');
    const [searchQuery, setSearchQuery] = useState('');
    const [searchResults, setSearchResults] = useState([]);
    const [isSearching, setIsSearching] = useState(false);
    const [hasSearched, setHasSearched] = useState(false);

    // Fetch my friends
    const {
        data,
        isLoading: isLoadingFriends,
        refetch: refetchFriends
    } = useDataFetch({
        queryKey: ['my-friends'],
        endPoint: '/student/friends',
    });

    const myFriends = data?.items || [];

    // Fetch friend requests
    const {
        data: requests,
        isLoading: isLoadingRequests,
        refetch: refetchRequests
    } = useDataFetch({
        queryKey: ['friend-requests'],
        endPoint: '/student/friends/pending',
    });
    const friendRequests = requests?.incomingRequests || [];

    // Handle search input change
    const handleSearchChange = (e) => {
        setSearchQuery(e.target.value);
    };

    // Handle search form submission
    const handleSearchSubmit = async (e) => {
        e.preventDefault();
        setHasSearched(true);

        if (searchQuery.length === 0) {
            setSearchResults([]);
            return;
        }

        if (searchQuery.length < 3) {
            toast.error('Please enter at least 3 characters');
            return;
        }

        try {
            setIsSearching(true);
            const response = await api.get(`student/friends/search?query=${searchQuery}`);
            const results = response?.data?.items || [];
            setSearchResults(results);
        } catch (error) {
            console.error('Error searching friends:', error);
            setSearchResults([]);
        } finally {
            setIsSearching(false);
        }
    };

    // Handle sending friend request
    const handleSendRequest = async (userId) => {
        try {
            await api.post('/student/friends/request', {
                requestedId: userId,
                requestMessage: '',
            });
            // Refresh search results to update status
            if (searchQuery.length >= 3) {
                const response = await api.get(`student/friends/search?query=${searchQuery}`);
                const results = response?.data?.items || [];
                setSearchResults(results);
            }
        } catch (error) {
            console.error('Error sending friend request:', error);
        }
    };

    // Handle responding to friend request
    const handleRespondToRequest = async (requestId, action) => {
        try {
            await api.post(`/student/friends/request/${requestId}/respond`, {
                status: action // 'accept' or 'reject'
            });

            // Refresh friend requests and friends list
            refetchRequests();
            if (action === 'accepted') {
                refetchFriends();
            }
        } catch (error) {
            console.error(`Error ${action}ing friend request:`, error);
        }
    };

    return (
        <div className="max-w-7xl mx-auto px-5 xl:px-0 py-5">
            <GoBack title="Friends" />

            <div className="shadow-lg border border-sky-200 max-sm:p-5 p-10 mt-5 rounded-lg h-full">
                <div className="max-w-4xl mx-auto">
                    {/* Tabs */}
                    <div className="flex space-x-2 bg-white rounded-lg shadow-sm p-3 border mb-6">
                        <button
                            className={`px-4 py-2 rounded-lg ${activeTab === 'friends' ? 'bg-yellow-100 text-yellow-800' : 'text-gray-700'}`}
                            onClick={() => setActiveTab('friends')}
                        >
                            My Friends
                        </button>
                        <button
                            className={`px-4 py-2 rounded-lg ${activeTab === 'requests' ? 'bg-yellow-100 text-yellow-800' : 'text-gray-700'}`}
                            onClick={() => setActiveTab('requests')}
                        >
                            Friend Requests
                            {friendRequests.length > 0 && (
                                <span className="ml-2 bg-red-500 text-white text-xs rounded-full px-2 py-1">
                                    {friendRequests.length}
                                </span>
                            )}
                        </button>
                        <button
                            className={`px-4 py-2 rounded-lg ${activeTab === 'search' ? 'bg-yellow-100 text-yellow-800' : 'text-gray-700'}`}
                            onClick={() => setActiveTab('search')}
                        >
                            Find Friends
                        </button>
                    </div>

                    {/* Tab Content */}
                    {activeTab === 'friends' && (
                        <div>
                            <h2 className="text-xl font-semibold mb-4">My Friends</h2>

                            {isLoadingFriends ? (
                                <div className="flex justify-center items-center py-20">
                                    <Icon
                                        icon="eos-icons:loading"
                                        width="48"
                                        height="48"
                                        className="animate-spin text-yellow-500"
                                    />
                                </div>
                            ) : myFriends.length === 0 ? (
                                <div className="flex flex-col items-center justify-center py-20">
                                    <Icon
                                        icon="mingcute:user-4-line"
                                        width="64"
                                        height="64"
                                        className="text-gray-400 mb-4"
                                    />
                                    <h3 className="text-xl font-medium text-gray-600">No friends yet</h3>
                                    <p className="text-gray-500">Start adding friends to see them here</p>
                                    <button
                                        className="mt-4 bg-yellow-100 text-gray-700 px-4 py-2 rounded-lg text-sm hover:bg-yellow-200 border border-gray-500"
                                        onClick={() => setActiveTab('search')}
                                    >
                                        Find Friends
                                    </button>
                                </div>
                            ) : (
                                <div className="space-y-4 max-h-[600px] overflow-y-auto">
                                    {myFriends?.map((friend) => (
                                        <div key={friend.id} className="flex items-center justify-between p-3 rounded-lg border border-gray-200">
                                            <div className="flex items-center">
                                                <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                                                    <Image
                                                        src={friend.profilePicture || '/assets/images/all-img/avatar.png'}
                                                        alt={friend.name}
                                                        width={100}
                                                        height={100}
                                                        className="w-full h-full object-cover"
                                                    />
                                                </div>
                                                <div>
                                                    <p className="font-medium">{friend.requestedName}</p>
                                                    <p className="text-sm text-gray-500">{friend?.requestedEmail}</p>
                                                </div>
                                            </div>
                                            <div className="flex space-x-2">
                                                <button className="text-yellow-500 hover:text-yellow-600">
                                                    <Icon icon="bx:chat" width="24" height="24" />
                                                </button>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    )}

                    {activeTab === 'requests' && (
                        <div>
                            <h2 className="text-xl font-semibold mb-4">Friend Requests</h2>

                            {isLoadingRequests ? (
                                <div className="flex justify-center items-center py-20">
                                    <Icon
                                        icon="eos-icons:loading"
                                        width="48"
                                        height="48"
                                        className="animate-spin text-yellow-500"
                                    />
                                </div>
                            ) : friendRequests.length === 0 ? (
                                <div className="flex flex-col items-center justify-center py-20">
                                    <Icon
                                        icon="mdi:email-outline"
                                        width="64"
                                        height="64"
                                        className="text-gray-400 mb-4"
                                    />
                                    <h3 className="text-xl font-medium text-gray-600">No pending friend requests</h3>
                                    <p className="text-gray-500">When someone sends you a friend request, it will appear here</p>
                                </div>
                            ) : (
                                <div className="space-y-4 max-h-[600px] overflow-y-auto">
                                    {friendRequests?.map((request) => (
                                        <div key={request.id} className="flex items-center justify-between p-3 rounded-lg shadow-lg border-gray-200 border">
                                            <div className="flex items-center">
                                                <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                                                    <Image
                                                        src={request.sender?.profilePicture || '/assets/images/all-img/avatar.png'}
                                                        alt={request.sender?.name}
                                                        width={100}
                                                        height={100}
                                                        className="w-full h-full object-cover"
                                                    />
                                                </div>
                                                <div>
                                                    <p className="font-medium">{request?.requesterName}</p>
                                                    <p className="text-sm text-gray-500">{request.sender?.email}</p>
                                                </div>
                                            </div>
                                            <div className="flex space-x-2">
                                                <button
                                                    className="bg-green-100 text-green-700 px-3 py-1 rounded-lg text-sm border border-green-500 hover:bg-green-200"
                                                    onClick={() => handleRespondToRequest(request.id, 'accepted')}
                                                >
                                                    Accept
                                                </button>
                                                <button
                                                    className="bg-red-100 text-red-700 px-3 py-1 rounded-lg text-sm border border-red-500 hover:bg-red-200"
                                                    onClick={() => handleRespondToRequest(request.id, 'rejected')}
                                                >
                                                    Reject
                                                </button>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    )}

                    {activeTab === 'search' && (
                        <div>
                            <h2 className="text-xl font-semibold mb-4">Find Friends</h2>

                            <div className="rounded-lg shadow-md p-6 mb-4 bg-[#FCF8EF]">
                                <form onSubmit={handleSearchSubmit} className="flex items-center gap-2">
                                    <div className="flex-1 relative">
                                        <input
                                            type="text"
                                            placeholder="Search by name or email"
                                            value={searchQuery}
                                            onChange={handleSearchChange}
                                            className="w-full pl-9 px-4 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-yellow-400"
                                        />
                                        <Icon
                                            icon="material-symbols:search"
                                            className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 w-5 h-5"
                                        />
                                    </div>
                                    <button
                                        type="submit"
                                        className="text-white tex-lg bg-gray-700 px-4 py-2 rounded-full flex items-center gap-2"
                                        disabled={isSearching}
                                    >
                                        <Icon icon="iconoir:search" width="24" height="24" />
                                        <span>{isSearching ? 'Searching...' : 'Search'}</span>
                                    </button>
                                </form>
                            </div>

                            {isSearching ? (
                                <div className="flex justify-center items-center py-20">
                                    <Icon
                                        icon="eos-icons:loading"
                                        width="48"
                                        height="48"
                                        className="animate-spin text-yellow-500"
                                    />
                                </div>
                            ) : !hasSearched ? (
                                <div className="flex flex-col items-center justify-center py-10">
                                    <Image
                                        src={'/assets/images/all-img/friends.png'}
                                        alt={'friends'}
                                        width={400}
                                        height={400}
                                    />
                                    <p className="text-gray-500 mt-4">Search for friends by name or email</p>
                                </div>
                            ) : searchResults.length === 0 ? (
                                <div className="flex flex-col items-center justify-center py-20">
                                    <Icon
                                        icon="mingcute:user-x-line"
                                        width="64"
                                        height="64"
                                        className="text-gray-400 mb-4"
                                    />
                                    <h3 className="text-xl font-medium text-gray-600">No users found</h3>
                                    <p className="text-gray-500">Try with a different name or email</p>
                                </div>
                            ) : (
                                <div className="space-y-4 max-h-[600px] overflow-y-auto">
                                    {searchResults.map((user) => (
                                        <div key={user.id} className="flex items-center justify-between py-3 border-b border-gray-100">
                                            <div className="flex items-center">
                                                <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                                                    <Image
                                                        src={user.profilePicture || '/assets/images/all-img/avatar.png'}
                                                        alt={user.name}
                                                        width={100}
                                                        height={100}
                                                        className="w-full h-full object-cover"
                                                    />
                                                </div>
                                                <div>
                                                    <p className="font-medium">{user.name}</p>
                                                    <p className="text-sm text-gray-500">{user.email}</p>
                                                </div>
                                            </div>
                                            {user.isFriend ? (
                                                <button
                                                    className="bg-green-100 text-green-700 px-3 py-1 rounded-lg text-sm border border-green-500"
                                                    disabled
                                                >
                                                    <span>
                                                        <Icon
                                                            icon="mdi:check"
                                                            width="24"
                                                            height="24"
                                                            className="inline-block mb-0.5"
                                                        />{' '}
                                                        Friends
                                                    </span>
                                                </button>
                                            ) : user.friendshipStatus === "pending" ? (
                                                <button
                                                    className="bg-blue-100 text-blue-700 px-3 py-1 rounded-lg text-sm border border-blue-500"
                                                    disabled
                                                >
                                                    <span>
                                                        <Icon
                                                            icon="mdi:clock-outline"
                                                            width="24"
                                                            height="24"
                                                            className="inline-block mb-0.5"
                                                        />{' '}
                                                        Pending
                                                    </span>
                                                </button>
                                            ) : (
                                                <button
                                                    className="bg-yellow-100 text-gray-700 px-3 py-1 rounded-lg text-sm hover:bg-yellow-200 border border-gray-500"
                                                    onClick={() => handleSendRequest(user.id)}
                                                >
                                                    <span>
                                                        <Icon
                                                            icon="mdi:account-plus"
                                                            width="24"
                                                            height="24"
                                                            className="inline-block mb-0.5"
                                                        />{' '}
                                                        Add Friend
                                                    </span>
                                                </button>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default Friends;