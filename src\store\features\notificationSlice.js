import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '@/lib/api';

// Async thunks for API calls
export const fetchNotifications = createAsyncThunk(
  'notifications/fetchNotifications',
  async ({ page = 1, limit = 20, read, type }, { rejectWithValue }) => {
    try {
      const params = { page, limit };
      if (read !== undefined) params.read = read;
      if (type) params.type = type;

      const response = await api.get('/notifications', { params });
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: error.message });
    }
  }
);

export const fetchUnreadCount = createAsyncThunk(
  'notifications/fetchUnreadCount',
  async (_, { rejectWithValue }) => {
    try {
      // Calculate unread count from notifications with isRead=false
      const response = await api.get('/notifications', { params: { isRead: false } });
      return { data: { count: response.data?.totalCount || response.data?.totalItems || 0 } };
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: error.message });
    }
  }
);

export const fetchNotificationById = createAsyncThunk(
  'notifications/fetchNotificationById',
  async (notificationId, { rejectWithValue }) => {
    try {
      const response = await api.get(`/notifications/${notificationId}`);
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: error.message });
    }
  }
);

export const markAsRead = createAsyncThunk(
  'notifications/markAsRead',
  async (notificationId, { rejectWithValue }) => {
    try {
      const response = await api.post(`/notifications/${notificationId}/read`, {
        isRead: true
      });
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: error.message });
    }
  }
);

export const markAllAsRead = createAsyncThunk(
  'notifications/markAllAsRead',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.post('/notifications/read-all', {});
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: error.message });
    }
  }
);

export const fetchNotificationPreferences = createAsyncThunk(
  'notifications/fetchPreferences',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/notifications/preferences');
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: error.message });
    }
  }
);

export const updateNotificationPreferences = createAsyncThunk(
  'notifications/updatePreferences',
  async (preferences, { rejectWithValue }) => {
    try {
      const response = await api.patch('/notifications/preferences', preferences);
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data || { message: error.message });
    }
  }
);

const initialState = {
  notifications: [],
  unreadCount: 0,
  preferences: {
    email: {},
    push: {},
    inApp: {},
  },
  meta: {
    totalItems: 0,
    itemsPerPage: 20,
    currentPage: 1,
    totalPages: 0,
  },
  loading: false,
  error: null,
  isOpen: false, // For notification dropdown
};

const notificationSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    toggleNotificationPanel: (state) => {
      state.isOpen = !state.isOpen;
    },
    closeNotificationPanel: (state) => {
      state.isOpen = false;
    },
    addNotification: (state, action) => {
      state.notifications.unshift(action.payload);
      // Check both isRead and read properties
      const isUnread = action.payload.isRead === false || action.payload.read === false;
      if (isUnread) {
        state.unreadCount += 1;
      }
    },
    updateNotificationReadStatus: (state, action) => {
      const { notificationId, read } = action.payload;
      const notification = state.notifications.find((n) => n.id === notificationId);
      if (notification) {
        // Check if the notification was previously unread
        const wasUnread = notification.isRead === false || notification.read === false;

        // Update both read and isRead properties for compatibility
        notification.read = read;
        notification.isRead = read;

        // If the notification was unread and is now being marked as read, decrement the counter
        if (wasUnread && read) {
          state.unreadCount = Math.max(0, state.unreadCount - 1);
        }
      }
    },
    setUnreadCount: (state, action) => {
      state.unreadCount = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch notifications
      .addCase(fetchNotifications.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchNotifications.fulfilled, (state, action) => {
        state.loading = false;
        state.notifications = action.payload.data.items;
        // Update meta information from the response
        state.meta = {
          totalItems: action.payload.data.totalItems || action.payload.data.totalCount || 0,
          itemsPerPage: action.payload.data.itemsPerPage || 10,
          currentPage: action.payload.data.currentPage || 1,
          totalPages: action.payload.data.totalPages || 1
        };
      })
      .addCase(fetchNotifications.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || 'Failed to fetch notifications';
      })
      // Fetch unread count
      .addCase(fetchUnreadCount.fulfilled, (state, action) => {
        state.unreadCount = action.payload.data.count;
      })
      // Fetch notification by ID
      .addCase(fetchNotificationById.fulfilled, (state, action) => {
        // If the notification is not already in the list, add it
        const notification = action.payload.data;
        const existingIndex = state.notifications.findIndex(n => n.id === notification.id);
        if (existingIndex === -1) {
          state.notifications.unshift(notification);
        } else {
          // Update the existing notification
          state.notifications[existingIndex] = notification;
        }
      })
      // Mark as read
      .addCase(markAsRead.fulfilled, (state, action) => {
        const notificationId = action.payload.data?.id || action.meta?.arg;
        const notification = state.notifications.find(n => n.id === notificationId);

        if (notification && (!notification.isRead || !notification.read)) {
          // Update both read and isRead properties for compatibility
          notification.read = true;
          notification.isRead = true;

          // Decrement unread count when marking as read
          state.unreadCount = Math.max(0, state.unreadCount - 1);
        }
      })
      // Mark all as read
      .addCase(markAllAsRead.fulfilled, (state) => {
        state.notifications.forEach((notification) => {
          // Update both read and isRead properties for compatibility
          notification.read = true;
          notification.isRead = true;
        });
        state.unreadCount = 0;
      })
      // Fetch preferences
      .addCase(fetchNotificationPreferences.fulfilled, (state, action) => {
        state.preferences = action.payload.data;
      })
      // Update preferences
      .addCase(updateNotificationPreferences.fulfilled, (state, action) => {
        state.preferences = action.payload.data.preferences;
      });
  },
});

export const {
  toggleNotificationPanel,
  closeNotificationPanel,
  addNotification,
  updateNotificationReadStatus,
  setUnreadCount,
} = notificationSlice.actions;

// Note: Async thunks are already exported above

// Selectors
export const selectNotifications = (state) => state.notifications.notifications;
export const selectUnreadCount = (state) => state.notifications.unreadCount;
export const selectNotificationMeta = (state) => state.notifications.meta;
export const selectNotificationPreferences = (state) => state.notifications.preferences;
export const selectNotificationPanelState = (state) => state.notifications.isOpen;
export const selectNotificationLoading = (state) => state.notifications.loading;

export default notificationSlice.reducer;
