'use client';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import { Icon } from '@iconify/react';

const Plans = () => {
  const router = useRouter();
  const [page, setPage] = useState(1);
  const [limit] = useState(10);

  const {
    data,
    isLoading,
    refetch,
  } = useDataFetch({
    queryKey: ['subscription-plans', page, limit],
    endPoint: '/plans',
    params: { page, limit }
  });

  // The API returns plans directly in the data property
  const plans = data?.items || [];

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this plan?')) {
      try {
        await api.delete(`/plans/${id}`);
        refetch();
      } catch (error) {
        console.log(error);
        alert('Failed to delete plan. Please try again.');
      }
    }
  };

  const toggleStatus = async (id, currentStatus) => {
    try {
      await api.patch(`/plans/${id}`, { isActive: !currentStatus });
      refetch();
    } catch (error) {
      console.log(error);
      alert('Failed to update plan status. Please try again.');
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-xl font-semibold">Plan List</h1>
        <button
          onClick={() => router.push('/dashboard/plans/add')}
          className="bg-yellow-400 hover:bg-yellow-500 text-black px-4 py-2 rounded-md"
        >
          Create Plan
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="p-4 border-b">
          <div className="relative">
            <input
              type="text"
              placeholder="Search by plan name..."
              className="w-full pl-10 pr-4 py-2 border rounded-md"
            />
            <Icon icon="material-symbols:search" className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Plan Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Most Popular Plan
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Active Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {isLoading ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-yellow-500 mx-auto"></div>
                  </td>
                </tr>
              ) : plans.length > 0 ? (
                plans.map((plan) => (
                  <tr key={plan.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      {plan.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {plan.subscriptionType.charAt(0).toUpperCase() + plan.subscriptionType.slice(1)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      ₩{parseFloat(plan.price).toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex justify-center">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-yellow-500 focus:ring-yellow-500 border-gray-300 rounded"
                          checked={plan.isPopular || false}
                          onChange={() => {
                            // Toggle popular status
                            api.patch(`/plans/${plan.id}`, { isPopular: !plan.isPopular })
                              .then(() => refetch())
                              .catch(err => console.error(err));
                          }}
                        />
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex justify-center">
                        <button
                          className={`relative inline-flex h-6 w-11 items-center rounded-full ${plan.isActive ? 'bg-green-500' : 'bg-gray-300'}`}
                          onClick={() => toggleStatus(plan.id, plan.isActive)}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${plan.isActive ? 'translate-x-6' : 'translate-x-1'}`}
                          />
                        </button>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex space-x-2 justify-center">
                        <button
                          onClick={() => router.push(`/dashboard/plans/edit/${plan.id}`)}
                          className="p-1 rounded-full text-gray-600 hover:text-gray-900"
                        >
                          <Icon icon="material-symbols:edit-outline" className="w-5 h-5" />
                        </button>
                        <button
                          onClick={() => handleDelete(plan.id)}
                          className="p-1 rounded-full text-red-600 hover:text-red-900"
                        >
                          <Icon icon="heroicons-outline:trash" className="w-5 h-5" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                    No plans found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {/* Handle pagination for both response formats */}
        {(data?.totalCount > limit || (data?.data && data?.data.length >= limit)) && (
          <div className="px-6 py-3 flex items-center justify-between border-t">
            <div className="text-sm text-gray-500">
              Showing {((page - 1) * limit) + 1} to {Math.min(page * limit, data?.totalCount || plans.length)} of {data?.totalCount || 'all'} results
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setPage(Math.max(1, page - 1))}
                disabled={page === 1}
                className="px-3 py-1 border rounded-md disabled:opacity-50"
              >
                Previous
              </button>
              <button
                onClick={() => setPage(page + 1)}
                disabled={(data?.totalCount && page * limit >= data.totalCount) || (data?.data && data.data.length < limit)}
                className="px-3 py-1 border rounded-md disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Plans;
