import Image from 'next/image';
import Link from 'next/link';

const Footer = () => {

  return (
    <footer className="w-full text-gray-800 text-center relative">
      <div className="relative hidden xl:block">
        <div className="absolute top-0 left-0 z-0">
          <Image
            src="/assets/images/all-img/introduction/footerBg.png"
            alt="icon"
            width={1800}
            height={300}
            className="w-full h-full"
            priority
          />
        </div>
      </div>

      <div className="bg-[#FCF8EF] pt-10">
        <div className="max-w-7xl mx-auto">
          <div className="text-gray-800 max-w-7xl mx-auto relative grid grid-cols-1 lg:grid-cols-12 gap-5 text-start items-start h-full py-10 lg:pt-20 lg:pb-16 px-5 xl:px-0">
            <div className="space-y-1 lg:col-span-4">
              <h3 className="font-semibold text-xl">EduFSS Co, Ltd.</h3>
              <p className="text-gray-600">CEO <PERSON>, <PERSON><PERSON><PERSON> Il-Do</p>
              <p className="text-gray-600 font-semibold">
                Personal Information Manager: Moon Il-Do
              </p>
              <p className="text-gray-600">
                Email: <EMAIL>
              </p>
            </div>

            {/* Contact Us */}
            <div className="space-y-2 lg:col-span-3">
              <h3 className="font-semibold text-lg">Contact Us</h3>
              <p className="text-gray-600">
                Email: <EMAIL>
              </p>
              <p className="text-gray-600">
                Operating hours: Monday 09:00 - 18:00
              </p>
              <p className="text-gray-600">
                Lunch Time: Weekdays 12:00 - 13:00
              </p>
            </div>

            {/* Quick Links */}
            <div className="space-y-2 lg:col-span-2">
              <h3 className="font-semibold text-lg">Quick Links</h3>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="/introduction"
                    className="text-gray-600 hover:text-gray-900"
                  >
                    Introduction
                  </Link>
                </li>
                <li>
                  <Link
                    href="/blog"
                    className="text-gray-600 hover:text-gray-900"
                  >
                    BLOG
                  </Link>
                </li>
                <li>
                  <Link
                    href="/contact"
                    className="text-gray-600 hover:text-gray-900"
                  >
                    Contact Us
                  </Link>
                </li>
              </ul>
            </div>

            {/* SNS */}
            <div className="space-y-2 lg:col-span-1">
              <h3 className="font-semibold text-lg">SNS</h3>
              <ul className="space-y-">
                <li>
                  <Link href="#" className="text-gray-600 hover:text-gray-900">
                    Facebook
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-gray-600 hover:text-gray-900">
                    Twitter
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-gray-600 hover:text-gray-900">
                    Instagram
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-gray-600 hover:text-gray-900">
                    KakaoTalk
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          {/* Copyright */}
          <div className="text-center border-t relative z-10 py-5 text-gray-600">
            © 2024 Hello English Coaching. All rights reserved.
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
