import React from 'react';
import Image from 'next/image';

const ImageGroup = ({ thumbImg, diaryImg, title, description, index, featureData }) => {
  return (
    <div className="relative flex flex-row items-center justify-between max-w-7xl mx-auto px-4 xl:px-0 py-12 lg:py-24">
      {/* Left Content Section */}
      {index % 2 === 0 ? (
        <div className="w-full md:w-1/2 space-y-6">
          <div className="relative">
            <Image
              src={diaryImg}
              alt="Feature"
              width={600}
              height={400}
              className="rounded-2xl shadow-lg w-full h-auto"
            />
            <Image
              src="/assets/images/all-img/introduction/headerpart3three.png"
              alt="Decoration"
              width={80}
              height={80}
              className="absolute -left-4 -top-5 lg:-left-6 lg:-top-8 xl:-left-10 xl:-top-12 max-sm:max-w-10 md:max-w-12 xl:max-w-20"
            />
          </div>
          <div className="ml-8 space-y-4 pt-4">
            <h2 className="text-3xl font-semibold text-gray-800">{title}</h2>
            <p className="text-gray-600 leading-relaxed">{description}</p>
          </div>
        </div>
      ) : (
        <div className="w-full md:w-1/2 relative">
          <Image
            src={thumbImg}
            alt="Feature"
            width={700}
            height={700}
            className="rounded-2xl w-full h-auto absolute -top-40 lg:top-1/2 lg:-translate-y-1/2 z-10 -right-5 md:-right-10"
          />
        </div>
      )}

      {/* Right Content Section */}
      {index % 2 === 0 ? (
        <div className="w-full md:w-1/2 relative">
          <Image
            src={thumbImg}
            alt="Feature"
            width={700}
            height={700}
            className="rounded-2xl w-full h-auto z-10"
          />
        </div>
      ) : (
        <div className="w-full md:w-1/2 space-y-6">
          <div className="relative">
            <Image
              src={diaryImg}
              alt="Feature"
              width={600}
              height={400}
              className="rounded-2xl shadow-lg w-full h-auto"
            />
            <Image
              src="/assets/images/all-img/introduction/headerpart3nine.png"
              alt="Decoration"
              width={80}
              height={80}
              className="absolute -right-4 -top-4 lg:-right-3 lg:-top-8 xl:-right-10 xl:-top-12 max-sm:max-w-10 md:max-w-12 xl:max-w-20"
            />
          </div>
          <div className="ml-8 space-y-4 pt-4">
            <h2 className="text-3xl font-semibold text-gray-800">{title}</h2>
            <p className="text-gray-600 leading-relaxed">{description}</p>
          </div>
        </div>
      )}

      {/* Decorative elements based on index - hidden for last item */}
      {index !== featureData.length - 1 && (
        <div className="">
          {index % 2 === 0 ? (
            <Image
              src="/assets/images/all-img/introduction/headerpart3seven.png"
              alt="Background decoration"
              width={800}
              height={200}
              className="absolute z-0 left-1/2 -translate-x-1/2 top-[70%] lg:top-[82%] max-sm:rotate-[160deg] lg:h-60 w-auto"
            />
          ) : (
            <Image
              src="/assets/images/all-img/introduction/headerpart3six.png"
              alt="Background decoration"
              width={800}
              height={200}
              className="absolute z-0 left-1/2 -translate-x-1/2 top-[82%] max-sm:rotate-[200deg] lg:h-60 w-auto"
            />
          )}
        </div>
      )}


    </div>
  );
};

export default ImageGroup;
