'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Icon } from '@iconify/react';

// Import or create components for each tab
import Waterfall from './_components/Waterfall';
import Block from './_components/Block';
import Storymaker from './_components/Storymaker';

const HecPlay = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');

  // Set default active tab or use the one from URL
  const [activeTab, setActiveTab] = useState(tabParam || 'waterfall');

  // Update URL when tab changes
  useEffect(() => {
    router.push(`/dashboard/submission-management/hec-play?tab=${activeTab}`, { scroll: false });
  }, [activeTab, router]);

  // Function to handle back button click
  const handleBackClick = () => {
    window.history.back();
  };

  // Function to render the correct content based on active tab
  const renderContent = () => {
    switch(activeTab) {
      case 'waterfall':
        return <Waterfall />;
      case 'block':
        return <Block />;
      case 'storymaker':
        return <Storymaker />;
      default:
        return <div>Select a tab</div>;
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header with Back Button */}
      <div className="flex items-center mb-6">
        <button
          onClick={handleBackClick}
          className="flex items-center text-gray-600 hover:text-yellow-600 mr-4"
        >
          <Icon icon="eva:arrow-back-fill" className="w-6 h-6" />
        </button>
        <h1 className="text-2xl font-semibold">HEC Play Management</h1>
      </div>

      {/* Main Content Area with Sidebar and Content */}
      <div className="flex flex-1">
        {/* Sidebar */}
        <div className="w-64 bg-[#FEFCE8] p-4 flex flex-col rounded-lg">
          <button
            className={`mb-2 p-2 text-left rounded-md ${activeTab === 'waterfall' ? 'bg-[#FFDE34] text-black' : 'hover:bg-gray-200'}`}
            onClick={() => setActiveTab('waterfall')}
          >
            Waterfall
          </button>
          <button
            className={`mb-2 p-2 text-left rounded-md ${activeTab === 'block' ? 'bg-[#FFDE34] text-black' : 'hover:bg-gray-200'}`}
            onClick={() => setActiveTab('block')}
          >
            Block
          </button>
          <button
            className={`mb-2 p-2 text-left rounded-md ${activeTab === 'storymaker' ? 'bg-[#FFDE34] text-black' : 'hover:bg-gray-200'}`}
            onClick={() => setActiveTab('storymaker')}
          >
            Storymaker
          </button>
        </div>

        {/* Content Area */}
        <div className="flex-1 p-6">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default HecPlay;
