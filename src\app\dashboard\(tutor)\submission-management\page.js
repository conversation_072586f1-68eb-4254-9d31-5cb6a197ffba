'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

const SubmissionManagement = () => {
  const router = useRouter();

  // Redirect to the HEC Diary page
  useEffect(() => {
    router.push('/dashboard/submission-management/hec-diary');
  }, [router]);

  // Return null or a loading indicator while redirecting
  return (
    <div className="flex items-center justify-center h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500 mx-auto"></div>
        <p className="mt-4 text-gray-600">Redirecting to Submission Management...</p>
      </div>
    </div>
  );
};

export default SubmissionManagement;
