'use client';
import React, { useState } from 'react';
import NewTablePage from "@/components/form/NewTablePage";
import useDataFetch from '@/hooks/useDataFetch';
import Image from 'next/image';
import DeleteModal from '@/components/form/modal/DeleteModal';
import { useRouter } from 'next/navigation';

const Storymaker = ({ onChangeTab }) => {
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [deleteData, setDeleteData] = useState(null);
  const router = useRouter();

  const {data, isLoading, refetch} = useDataFetch({
    queryKey: ['storymaker', currentPage, rowsPerPage],
    endPoint: '/play/story-maker/admin/stories',
    params: { page: currentPage, limit: rowsPerPage },
  });

  const storyItems = data?.items || []

  // Pagination logic
  const totalItems = storyItems.length;
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const paginatedItems = storyItems.slice(startIndex, endIndex);

  // Define columns for the table
  const columns = [
    {
      label: 'QUESTION SET TITLE',
      field: 'title',
    },
    {
      label: 'QUESTION IMAGE',
      field: 'picture',
      cellRenderer: (value) => (
        <Image src={value || '/assets/images/all-img/noImage.png'} width={60} height={60} alt="Question" className="h-10 w-10 object-cover rounded" />
      )
    },
    {
      label: 'TOTAL SCORE',
      field: 'score',
    },
  ];

  // Define actions
  const actions = [
    {
      name: 'view',
      icon: 'material-symbols:visibility',
      className: 'text-blue-600',
      onClick: (row) => router.push(`/dashboard/module-management/hec-play/storymaker/${row?.id}`),
    },
    {
      name: 'edit',
      icon: 'material-symbols:edit',
      className: 'text-gray-600',
      onClick: (row) => router.push(`/dashboard/module-management/hec-play/storymaker/edit/${row?.id}`),
    },
    {
      name: 'delete',
      icon: 'heroicons-outline:trash',
      className: 'text-red-600',
      onClick: (row) => setDeleteData(row),
    },

  ];

  // Handle page change
  const handleChangePage = (page) => {
    setCurrentPage(page);
  };

  return (
    <div className="container mx-auto p-4">
      <NewTablePage
        title="Storymaker Games"
        createButton="Create Question Set"
        createBtnLink="/dashboard/module-management/hec-play/storymaker/add"
        columns={columns}
        data={paginatedItems}
        actions={actions}
        currentPage={currentPage}
        changePage={handleChangePage}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        totalPages={Math.ceil(totalItems / rowsPerPage)}
        showCheckboxes={false}
        showSearch={true}
        showNameFilter={false}
        showSortFilter={false}
      />


      <DeleteModal
        isOpen={!!deleteData}
        onClose={() => setDeleteData(null)}
        onSuccess={refetch}
        data={deleteData}
        endPoint={`/play/story-maker/admin/stories/${deleteData?.id}`}
        itemName="story"
      />
    </div>
  );
};

export default Storymaker;
