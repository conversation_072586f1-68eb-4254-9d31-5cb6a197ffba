'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import NewTablePage from '@/components/form/NewTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import { toast } from 'sonner';
import api from '@/lib/api';

const QuestionBank = () => {
  const router = useRouter();
  
  // State variables
  const [questions, setQuestions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchField, setSearchField] = useState('question');
  const [sortField, setSortField] = useState('createdAt');
  const [sortDirection, setSortDirection] = useState('DESC');
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedQuestion, setSelectedQuestion] = useState(null);
  const [showQuestionModal, setShowQuestionModal] = useState(false);

  // Mock data for development - replace with actual API call
  useEffect(() => {
    const fetchQuestions = async () => {
      setLoading(true);
      try {
        // This would be replaced with an actual API call
        // const response = await api.get('/tutor/qa/questions', {
        //   params: {
        //     page: currentPage,
        //     limit: rowsPerPage,
        //     search: searchTerm,
        //     searchField,
        //     sortField,
        //     sortDirection
        //   }
        // });
        
        // Mock data for development
        const mockData = Array(25).fill(null).map((_, index) => ({
          id: `question-${index + 1}`,
          question: `What is the meaning of ${index + 1}?`,
          category: index % 3 === 0 ? 'Grammar' : index % 3 === 1 ? 'Vocabulary' : 'Comprehension',
          difficulty: index % 4 === 0 ? 'Easy' : index % 4 === 1 ? 'Medium' : index % 4 === 2 ? 'Hard' : 'Expert',
          createdAt: new Date(Date.now() - Math.random() * 10000000000).toISOString(),
          createdBy: 'Admin',
          correctAnswer: `This is the correct answer for question ${index + 1}.`,
          options: [
            `Option A for question ${index + 1}`,
            `Option B for question ${index + 1}`,
            `Option C for question ${index + 1}`,
            `Option D for question ${index + 1}`
          ]
        }));
        
        setQuestions(mockData);
        setTotalItems(mockData.length);
        setTotalPages(Math.ceil(mockData.length / rowsPerPage));
      } catch (error) {
        console.error('Error fetching questions:', error);
        toast.error('Failed to load questions');
      } finally {
        setLoading(false);
      }
    };
    
    fetchQuestions();
  }, [currentPage, rowsPerPage, searchTerm, searchField, sortField, sortDirection]);

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle search
  const handleSearch = (term) => {
    setSearchTerm(term);
    setCurrentPage(1);
  };

  // Handle search field change
  const handleSearchFieldChange = (field) => {
    setSearchField(field);
    setCurrentPage(1);
  };

  // Handle sort
  const handleSort = (field, direction) => {
    setSortField(field);
    setSortDirection(direction);
  };

  // Handle view question
  const handleViewQuestion = (question) => {
    setSelectedQuestion(question);
    setShowQuestionModal(true);
  };

  // Define table columns
  const columns = [
    { field: '#', label: '#', sortable: false },
    { field: 'question', label: 'QUESTION', sortable: true },
    { field: 'category', label: 'CATEGORY', sortable: true },
    { field: 'difficulty', label: 'DIFFICULTY', sortable: true },
    { 
      field: 'createdAt', 
      label: 'CREATED AT', 
      sortable: true,
      cellRenderer: (value) => new Date(value).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }
  ];

  // Define actions for table rows
  const actions = [
    {
      label: 'View',
      onClick: handleViewQuestion
    }
  ];

  // Define search filter options
  const searchFilterOptions = [
    { label: 'Question', value: 'question' },
    { label: 'Category', value: 'category' },
    { label: 'Difficulty', value: 'difficulty' }
  ];

  // Question Modal Component
  const QuestionModal = ({ isOpen, onClose, question }) => {
    if (!isOpen || !question) return null;
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-auto">
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Question Details</h2>
              <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="grid grid-cols-2 gap-4 bg-gray-50 p-4 rounded-lg mb-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Category</p>
                <p className="font-medium">{question.category}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Difficulty</p>
                <p className="font-medium">{question.difficulty}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Created At</p>
                <p className="font-medium">{new Date(question.createdAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                })}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Created By</p>
                <p className="font-medium">{question.createdBy}</p>
              </div>
            </div>
            
            <div className="mb-4">
              <h3 className="text-lg font-semibold mb-2">Question</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="whitespace-pre-wrap">{question.question}</p>
              </div>
            </div>
            
            <div className="mb-4">
              <h3 className="text-lg font-semibold mb-2">Options</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <ul className="list-disc pl-5 space-y-2">
                  {question.options.map((option, index) => (
                    <li key={index}>{option}</li>
                  ))}
                </ul>
              </div>
            </div>
            
            <div className="mb-4">
              <h3 className="text-lg font-semibold mb-2">Correct Answer</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="whitespace-pre-wrap">{question.correctAnswer}</p>
              </div>
            </div>
            
            <div className="flex justify-end">
              <button
                onClick={onClose}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Question Bank</h1>
      </div>
      
      {/* Table */}
      <NewTablePage
        columns={columns}
        data={questions}
        actions={actions}
        loading={loading}
        
        // Pagination props
        currentPage={currentPage}
        totalPages={totalPages}
        changePage={handlePageChange}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        
        // Search and filter props
        showSearch={true}
        showNameFilter={true}
        showSortFilter={true}
        
        // Pass current state values
        searchTerm={searchTerm}
        searchField={searchField}
        sortField={sortField}
        sortDirection={sortDirection}
        
        // Pass handlers for search, filter and sort
        onSearch={handleSearch}
        onNameFilterChange={handleSearchFieldChange}
        onSort={handleSort}
        
        // Pass name filter options
        nameFilterOptions={searchFilterOptions}
      />
      
      {/* Question Modal */}
      <QuestionModal
        isOpen={showQuestionModal}
        onClose={() => setShowQuestionModal(false)}
        question={selectedQuestion}
      />
    </div>
  );
};

export default QuestionBank;
