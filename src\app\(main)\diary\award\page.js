'use client';
import React from 'react';
import Image from 'next/image';
import { FiAward } from 'react-icons/fi';

// Award Badge Component
const AwardBadge = ({ points, name, imageSrc = '/assets/diary/award.png' }) => {
  return (
    <div className="flex flex-col items-center bg-[#FFFDF5] rounded-lg border border-yellow-200 p-4 shadow-sm max-w-[200px]">
      <div className="relative">
        {/* Trophy icon for weekly/monthly winners */}
        {points !== '60' && (
          <div className="absolute -top-6 -left-6">
            <Image
              src="/assets/diary/trophy.png"
              alt="Trophy"
              width={30}
              height={30}
            />
          </div>
        )}

        {/* Profile image */}
        <div className="w-24 h-24 rounded-full overflow-hidden">
          <Image
            src={imageSrc}
            alt="Profile"
            width={96}
            height={96}
            className="object-cover"
          />
        </div>
      </div>

      <div className="mt-4 text-center">
        <p className="font-bold text-sm">Best Performer</p>
        <p className="text-xs text-gray-600 mt-1">{name}</p>
        <p className="text-sm font-bold text-yellow-600 mt-2">
          Point: {points}
        </p>
      </div>
    </div>
  );
};

const AwardPage = () => {
  // Sample data for award winners
  const myAward = { points: '60', name: 'Abu Mohammad Maksudu' };

  const weeklyWinners = [
    { id: 1, points: '82', name: 'Abu Mohammad Maksudu' },
    { id: 2, points: '66', name: 'Abu Mohammad Maksudu' },
    { id: 3, points: '60', name: 'Abu Mohammad Maksudu' },
  ];

  const monthlyWinners = [
    { id: 1, points: '82', name: 'Abu Mohammad Maksudu' },
    { id: 2, points: '66', name: 'Abu Mohammad Maksudu' },
    { id: 3, points: '60', name: 'Abu Mohammad Maksudu' },
  ];

  return (
    <div className="container p-6 space-y-8 bg-[#EDFDFD]">
      {/* My Achieve Award Section */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-bold text-left text-brown-700 mb-6">
          My Achieve Award
        </h2>
        <div className="flex justify-start">
          <AwardBadge points={myAward.points} name={myAward.name} />
        </div>
      </div>

      {/* This Week Award Winners */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-bold text-left text-brown-700 mb-6">
          This Week Award Winners
        </h2>
        <div className="flex flex-wrap gap-6">
          {weeklyWinners.map((winner) => (
            <AwardBadge
              key={winner.id}
              points={winner.points}
              name={winner.name}
            />
          ))}
        </div>
      </div>

      {/* This Month Award Winners */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-bold text-left text-brown-700 mb-6">
          This Month Award Winners
        </h2>
        <div className="flex flex-wrap gap-6">
          {monthlyWinners.map((winner) => (
            <AwardBadge
              key={winner.id}
              points={winner.points}
              name={winner.name}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default AwardPage;
