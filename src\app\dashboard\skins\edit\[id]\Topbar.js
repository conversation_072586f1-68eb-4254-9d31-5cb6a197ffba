'use client';
import { useState, useRef, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import Button from '@/components/Button';
import Modal from '@/components/Modal';
import api from '@/lib/api';
import { useParams, useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import {
  selectCanvasItems,
  selectCanvasBackground,
  selectCanvasWidth,
  selectCanvasHeight,
  clearSelection
} from '@/store/features/canvasSlice';

const Topbar = () => {
  const { id } = useParams();
  const router = useRouter();
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const canvasItems = useSelector(selectCanvasItems);
  const canvasBackground = useSelector(selectCanvasBackground);
  const canvasWidth = useSelector(selectCanvasWidth);
  const canvasHeight = useSelector(selectCanvasHeight);
  const formikRef = useRef(null);

  // State for save dialog
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);
  const [saveError, setSaveError] = useState('');
  const [initialValues, setInitialValues] = useState({
    name: '',
    description: '',
    isActive: true
  });

  // Validation schema for the form
  const validationSchema = Yup.object({
    name: Yup.string().required('Name is required'),
    description: Yup.string().required('Description is required'),
    isActive: Yup.boolean()
  });

  // Function to update form values from parent component
  const updateSkinFormValues = (values) => {
    setInitialValues({
      name: values.name || '',
      description: values.description || '',
      isActive: values.isActive ?? true
    });

    // If the form is already open, update the values
    if (formikRef.current) {
      const { setValues } = formikRef.current;
      setValues({
        name: values.name || '',
        description: values.description || '',
        isActive: values.isActive ?? true
      });
    }
  };

  // Make the updateSkinFormValues function available globally
  useEffect(() => {
    window.updateSkinFormValues = updateSkinFormValues;

    return () => {
      delete window.updateSkinFormValues;
    };
  }, []);

  const handleUpdateSkin = () => {
    // First, deselect any selected element
    dispatch(clearSelection());
    // Then open the save dialog
    setIsSaveDialogOpen(true);
  };

  const closeDialog = () => {
    setIsSaveDialogOpen(false);
    setSaveError('');
  };

  const handleSaveSkin = async (values, { setSubmitting }) => {
    setSaveError('');

    try {
      // Generate template content as a string
      const templateContent = JSON.stringify({
        items: canvasItems,
        background: canvasBackground,
        width: canvasWidth,
        height: canvasHeight
      });

      // Create FormData object for multipart/form-data
      const formData = new FormData();
      formData.append('name', values.name);
      formData.append('description', values.description);
      formData.append('isActive', values.isActive);
      formData.append('templateContent', templateContent);

      // Capture the canvas as an image and use it as the preview image
      if (window.captureCanvasImage) {
        const previewImage = await window.captureCanvasImage();
        if (previewImage) {
          formData.append('previewImage', previewImage);
        } else {
          throw new Error('Failed to generate preview image. Please try again.');
        }
      } else {
        throw new Error('Canvas capture function not available. Please refresh the page and try again.');
      }

      // Call the API using the api utility
      const response = await api.patch(`/admin/diary/skins/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        showSuccessMessage: true,
        successMessage: 'Skin updated successfully!'
      });

      // First invalidate the queries
      await queryClient.invalidateQueries(['diary-skins']);

      // Then close dialog and navigate
      closeDialog();
      router.push('/dashboard/skins');

      console.log('Skin updated successfully:', response);
    } catch (error) {
      console.error('Error updating skin:', error);
      setSaveError(error.response?.data?.message || error.message || 'Failed to update skin. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="w-full bg-[#8B4513] text-white py-3 px-6 flex items-center justify-between">
      {/* Left side - Title */}
      <div className="text-xl font-medium tracking-wide">Edit Skin</div>

      {/* Right side - Buttons */}
      <div className="flex items-center space-x-3">
        <Button
          buttonText="Update Skin"
          onClick={handleUpdateSkin}
          className="w-auto"
        />
      </div>

      {/* Save Dialog */}
      <Modal
        isOpen={isSaveDialogOpen}
        onClose={closeDialog}
        position="center"
        title="Update Skin"
        width="md"
      >
        <div className="p-4">
          {saveError && (
            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
              {saveError}
            </div>
          )}

          <Formik
            innerRef={formikRef}
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={handleSaveSkin}
            enableReinitialize={true}
          >
            {({ isSubmitting, touched, errors }) => (
              <Form>
                <div className="mb-4">
                  <label htmlFor="name" className="block text-sm font-medium mb-1 text-gray-700">
                    Name <span className="text-red-500">*</span>
                  </label>
                  <Field
                    id="name"
                    name="name"
                    type="text"
                    className={`w-full p-2 border rounded-md ${touched.name && errors.name ? 'border-red-500' : 'border-gray-300'}`}
                    placeholder="Enter skin name"
                  />
                  <ErrorMessage name="name" component="p" className="mt-1 text-sm text-red-500" />
                </div>

                <div className="mb-4">
                  <label htmlFor="description" className="block text-sm font-medium mb-1 text-gray-700">
                    Description <span className="text-red-500">*</span>
                  </label>
                  <Field
                    as="textarea"
                    id="description"
                    name="description"
                    rows="3"
                    className={`w-full p-2 border rounded-md ${touched.description && errors.description ? 'border-red-500' : 'border-gray-300'}`}
                    placeholder="Enter skin description"
                  />
                  <ErrorMessage name="description" component="p" className="mt-1 text-sm text-red-500" />
                </div>

                <div className="mb-4">
                  <div className="flex items-center justify-between mb-1">
                    <p className="text-sm text-gray-700">
                      Preview Image
                    </p>
                    <span className="text-xs text-gray-500">Will be automatically updated from the canvas</span>
                  </div>
                  <div className="w-full p-3 border border-gray-300 rounded-md bg-gray-50 text-center">
                    <p className="text-sm text-gray-600">A new preview image will be generated when you update</p>
                  </div>
                </div>

                <div className="mb-6">
                  <label className="flex items-center">
                    <Field
                      type="checkbox"
                      name="isActive"
                      className="mr-2 h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded"
                    />
                    <span className="text-sm text-gray-700">Active</span>
                  </label>
                </div>

                <div className="flex justify-end space-x-3">
                  <Button
                    buttonText="Cancel"
                    onClick={closeDialog}
                    className="w-auto bg-gray-200 text-gray-800 hover:bg-gray-300"
                    type="button"
                  />
                  <Button
                    buttonText={isSubmitting ? 'Updating...' : 'Update'}
                    type="submit"
                    className="w-auto"
                    disabled={isSubmitting}
                  />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </Modal>
    </div>
  );
};

export default Topbar;
