'use client';
import FormInput from '@/components/form/FormInput';
import FormRadio from '@/components/form/FormRadio';
import RegularGoBack from '@/components/shared/RegularGoBack';
import useDataFetch from '@/hooks/useDataFetch';
import { Form, Formik } from 'formik';
import { useParams, useRouter } from 'next/navigation';
import React from 'react';
import * as Yup from 'yup';
import api from '@/lib/api';
import { useQueryClient } from '@tanstack/react-query';

const validationSchema = Yup.object().shape({
  name: Yup.string().required('Please enter category name'),
  isActive: Yup.string().required('Please select status'),
});

const EditCategory = () => {
  const { id } = useParams();
  const router = useRouter();
  const queryClient = useQueryClient();

  const { data: categoryDetails, isLoading, refetch } = useDataFetch({
    queryKey: 'shop-category-details',
    endPoint: `/admin/shop/categories/${id}`,
  });

  const handleSubmit = async (values, { resetForm, setFieldError }) => {
    console.log(values);
    try {
      const response = await api.patch(`/admin/shop/categories/${id}`, values);
      router.push('/dashboard/shops/categories');
      queryClient.invalidateQueries('/admin/shop/categories');
      resetForm();
    } catch (error) {
      console.log(error);
      if (error?.response?.data?.validationErrors) {
        const validationErrors = error.response.data.validationErrors;
        Object.keys(validationErrors).forEach((field) => {
          setFieldError(`${field}`, validationErrors[field][0]);
        });
      }
    }
  };

  const activeStatusOptions = [
    { label: 'Yes', value: true },
    { label: 'No', value: false },
  ];

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="">
      <RegularGoBack className={'pb-5 max-w-32'} />
      <Formik
        enableReinitialize={true}
        initialValues={{
          name: categoryDetails?.name || '',
          description: categoryDetails?.description || '',
          isActive: categoryDetails?.isActive || true,
        }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, values }) => (
          <Form className="space-y-6 ">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 items-start gap-6 bg-gray-100 p-7 rounded-lg border">
              <FormInput
                label="Category Name"
                name="name"
                type="text"
                placeholder="Write here"
                required
              />

              <FormInput
                label="Category Descriptions"
                name="description"
                type="text"
                placeholder="Write category descriptions"
              />

              <div className="mt-3">
                <FormRadio
                  label="Active Status"
                  name="isActive"
                  isHorizontal={true}
                  options={activeStatusOptions}
                  required
                />
              </div>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-gray-300"
              >
                {isSubmitting ? 'Updating...' : 'Update Category'}
              </button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default EditCategory;
