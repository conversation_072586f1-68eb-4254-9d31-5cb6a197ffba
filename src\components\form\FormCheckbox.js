import { useField } from 'formik';

const FormCheckbox = ({ label, options, isHorizontal, required, ...props }) => {
  const [field, meta, helpers] = useField(props);
  const { setValue } = helpers;

  const handleChange = (value) => {
    if (Array.isArray(field.value)) {
      // Handle multiple checkboxes
      const newValue = field.value.includes(value)
        ? field.value.filter((item) => item !== value)
        : [...field.value, value];
      setValue(newValue);
    } else {
      // Handle single checkbox
      setValue(!field.value);
    }
  };

  if (options) {
    return (
      <div className="">
        <label className="block text-sm sm:text-base font-[500] text-gray-700 mb-2 ">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <div className={`${isHorizontal ? 'flex space-x-4 flex-wrap' : 'space-y-2'}`}>
          {options.map((option) => (
            <div key={option.value} className="flex items-center">
              <input
                type="checkbox"
                checked={field.value.includes(option.value)}
                onChange={() => handleChange(option.value)}
                className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded cursor-pointer"
              />
              <label className="ml-2 text-sm text-gray-700 ">
                {option.label}
              </label>
            </div>
          ))}
        </div>
        {meta.touched && meta.error ? (
          <div className="text-red-500 text-xs mt-1">{meta.error}</div>
        ) : null}
      </div>
    );
  }

  return (
    <div className="flex items-center">
      <input
        type="checkbox"
        {...field}
        {...props}
        checked={field.value}
        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
      />
      <label className="ml-2 text-sm text-gray-700 ">
        {label}
      </label>
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-xs mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export default FormCheckbox;