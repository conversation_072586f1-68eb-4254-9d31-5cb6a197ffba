'use client'
import { useRouter } from 'next/navigation';
import React from 'react';
import { ButtonIcon } from '../Button';

const GoBack = ({title, linkClass}) => {
  const router = useRouter();
  return (
    <div
      onClick={() => router.back()}
      className={`flex justify-start items-center gap-2 group cursor-pointer ${linkClass}`} 
    >
      <ButtonIcon
        icon="famicons:arrow-back-outline"
        innerBtnCls="h-12 w-12 text-white"
        btnIconCls="h-5 w-5 text-white"
      />

      <h3 className="text-xl font-[500] group-hover:text-[#FFDE34]">
        {title}
      </h3>
    </div>
  );
};

export default GoBack;
