'use client';
import CommonSidebar from '@/components/dashboard/CommonSidebar';

const HecPlayLayout = ({ children }) => {

  // Define the sidebar data with routes
  const sidebarData = [
    {
      name: 'Waterfall',
      value: 'waterfall',
      path: '/dashboard/module-management/hec-play/waterfall',
      children: [
        {
          name: 'Question Bank',
          value: 'waterfall',
          path: '/dashboard/module-management/hec-play/waterfall',
        },
      ],
    },
    {
      name: 'Block',
      value: 'block',
      path: '/dashboard/module-management/hec-play/block',
    },
    {
      name: 'Storymaker',
      value: 'storymaker',
      path: '/dashboard/module-management/hec-play/storymaker',
    },
  ];

  return (
    <div className="">

      {/* Main Content Area with Sidebar and Content */}
      <CommonSidebar data={sidebarData} title="HEC Play">
        {children}
      </CommonSidebar>
    </div>
  );
};

export default HecPlayLayout;
