'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Icon } from '@iconify/react';

// Import or create components for each tab
import QuestionBank from './_components/QuestionBank';
import CreateQuestions from './_components/CreateQuestions';
import MissionQAList from './_components/MissionQAList';
import CreateMissionQA from './_components/CreateMissionQA';
import QASubmissions from './_components/QASubmissions';

const HecQA = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');

  // Set default active tab or use the one from URL
  const [activeTab, setActiveTab] = useState(tabParam || 'questionBank');

  // Update URL when tab changes
  useEffect(() => {
    router.push(`/dashboard/submission-management/hec-qa?tab=${activeTab}`, { scroll: false });
  }, [activeTab, router]);

  // Function to handle back button click
  const handleBackClick = () => {
    window.history.back();
  };

  // Function to render the correct content based on active tab
  const renderContent = () => {
    switch(activeTab) {
      case 'questionBank':
        return <QuestionBank />;
      case 'createQuestions':
        return <CreateQuestions />;
      case 'missionQAList':
        return <MissionQAList />;
      case 'createMissionQA':
        return <CreateMissionQA />;
      case 'qaSubmissions':
        return <QASubmissions />;
      default:
        return <div>Select a tab</div>;
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header with Back Button */}
      <div className="flex items-center mb-6">
        <button
          onClick={handleBackClick}
          className="flex items-center text-gray-600 hover:text-yellow-600 mr-4"
        >
          <Icon icon="eva:arrow-back-fill" className="w-6 h-6" />
        </button>
        <h1 className="text-2xl font-semibold">HEC Q&A Management</h1>
      </div>

      {/* Main Content Area with Sidebar and Content */}
      <div className="flex flex-1">
        {/* Sidebar */}
        <div className="w-64 bg-[#FEFCE8] p-4 flex flex-col rounded-lg">
          <button
            className={`mb-2 p-2 text-left rounded-md ${activeTab === 'questionBank' ? 'bg-[#FFDE34] text-black' : 'hover:bg-gray-200'}`}
            onClick={() => setActiveTab('questionBank')}
          >
            Question Bank
          </button>
          <button
            className={`mb-2 p-2 text-left rounded-md ${activeTab === 'createQuestions' ? 'bg-[#FFDE34] text-black' : 'hover:bg-gray-200'}`}
            onClick={() => setActiveTab('createQuestions')}
          >
            Create Questions
          </button>
          <button
            className={`mb-2 p-2 text-left rounded-md ${activeTab === 'missionQAList' ? 'bg-[#FFDE34] text-black' : 'hover:bg-gray-200'}`}
            onClick={() => setActiveTab('missionQAList')}
          >
            Mission Q&A List
          </button>
          <button
            className={`mb-2 p-2 text-left rounded-md ${activeTab === 'createMissionQA' ? 'bg-[#FFDE34] text-black' : 'hover:bg-gray-200'}`}
            onClick={() => setActiveTab('createMissionQA')}
          >
            Create Mission Q&A
          </button>
          <button
            className={`mb-2 p-2 text-left rounded-md ${activeTab === 'qaSubmissions' ? 'bg-[#FFDE34] text-black' : 'hover:bg-gray-200'}`}
            onClick={() => setActiveTab('qaSubmissions')}
          >
            Q&A Submissions
          </button>
        </div>

        {/* Content Area */}
        <div className="flex-1 p-6">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default HecQA;
