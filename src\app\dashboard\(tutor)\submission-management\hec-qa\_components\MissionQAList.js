'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import NewTablePage from '@/components/form/NewTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import { toast } from 'sonner';
import api from '@/lib/api';

const MissionQAList = () => {
  const router = useRouter();
  
  // State variables
  const [missions, setMissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchField, setSearchField] = useState('title');
  const [sortField, setSortField] = useState('createdAt');
  const [sortDirection, setSortDirection] = useState('DESC');
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedMission, setSelectedMission] = useState(null);
  const [showMissionModal, setShowMissionModal] = useState(false);
  const [activeTab, setActiveTab] = useState(0); // 0 for Weekly, 1 for Monthly

  // Define tabs
  const tabs = [
    { name: 'Weekly Missions', value: 'weekly' },
    { name: 'Monthly Missions', value: 'monthly' }
  ];

  // Mock data for development - replace with actual API call
  useEffect(() => {
    const fetchMissions = async () => {
      setLoading(true);
      try {
        // This would be replaced with an actual API call
        // const response = await api.get('/tutor/qa/missions', {
        //   params: {
        //     page: currentPage,
        //     limit: rowsPerPage,
        //     search: searchTerm,
        //     searchField,
        //     sortField,
        //     sortDirection,
        //     type: tabs[activeTab].value
        //   }
        // });
        
        // Mock data for development
        const mockData = Array(20).fill(null).map((_, index) => ({
          id: `mission-${index + 1}`,
          title: `Mission ${index + 1}`,
          description: `Description for mission ${index + 1}`,
          type: index % 2 === 0 ? 'weekly' : 'monthly',
          startDate: new Date(Date.now() + Math.random() * 1000000000).toISOString(),
          endDate: new Date(Date.now() + Math.random() * 10000000000).toISOString(),
          questionCount: Math.floor(Math.random() * 10) + 5,
          status: index % 3 === 0 ? 'active' : index % 3 === 1 ? 'upcoming' : 'completed',
          createdAt: new Date(Date.now() - Math.random() * 10000000000).toISOString(),
          createdBy: 'Admin'
        }));
        
        // Filter based on active tab
        const filteredData = mockData.filter(item => 
          (activeTab === 0 && item.type === 'weekly') || 
          (activeTab === 1 && item.type === 'monthly')
        );
        
        setMissions(filteredData);
        setTotalItems(filteredData.length);
        setTotalPages(Math.ceil(filteredData.length / rowsPerPage));
      } catch (error) {
        console.error('Error fetching missions:', error);
        toast.error('Failed to load missions');
      } finally {
        setLoading(false);
      }
    };
    
    fetchMissions();
  }, [currentPage, rowsPerPage, searchTerm, searchField, sortField, sortDirection, activeTab]);

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle search
  const handleSearch = (term) => {
    setSearchTerm(term);
    setCurrentPage(1);
  };

  // Handle search field change
  const handleSearchFieldChange = (field) => {
    setSearchField(field);
    setCurrentPage(1);
  };

  // Handle sort
  const handleSort = (field, direction) => {
    setSortField(field);
    setSortDirection(direction);
  };

  // Handle tab change
  const handleTabChange = (index) => {
    setActiveTab(index);
    setCurrentPage(1);
  };

  // Handle view mission
  const handleViewMission = (mission) => {
    setSelectedMission(mission);
    setShowMissionModal(true);
  };

  // Define table columns
  const columns = [
    { field: '#', label: '#', sortable: false },
    { field: 'title', label: 'TITLE', sortable: true },
    { field: 'questionCount', label: 'QUESTIONS', sortable: true },
    { 
      field: 'startDate', 
      label: 'START DATE', 
      sortable: true,
      cellRenderer: (value) => new Date(value).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    },
    { 
      field: 'endDate', 
      label: 'END DATE', 
      sortable: true,
      cellRenderer: (value) => new Date(value).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    },
    { 
      field: 'status', 
      label: 'STATUS', 
      sortable: true,
      cellRenderer: (value) => {
        let bgColor = '';
        let textColor = '';
        
        switch(value) {
          case 'active':
            bgColor = 'bg-green-100';
            textColor = 'text-green-800';
            break;
          case 'upcoming':
            bgColor = 'bg-blue-100';
            textColor = 'text-blue-800';
            break;
          case 'completed':
            bgColor = 'bg-gray-100';
            textColor = 'text-gray-800';
            break;
          default:
            bgColor = 'bg-gray-100';
            textColor = 'text-gray-800';
        }
        
        return (
          <span className={`${bgColor} ${textColor} px-3 py-1 rounded-full text-xs font-medium capitalize`}>
            {value}
          </span>
        );
      }
    }
  ];

  // Define actions for table rows
  const actions = [
    {
      label: 'View',
      onClick: handleViewMission
    }
  ];

  // Define search filter options
  const searchFilterOptions = [
    { label: 'Title', value: 'title' },
    { label: 'Status', value: 'status' }
  ];

  // Mission Modal Component
  const MissionModal = ({ isOpen, onClose, mission }) => {
    if (!isOpen || !mission) return null;
    
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-auto">
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Mission Details</h2>
              <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="grid grid-cols-2 gap-4 bg-gray-50 p-4 rounded-lg mb-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Title</p>
                <p className="font-medium">{mission.title}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Type</p>
                <p className="font-medium capitalize">{mission.type}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Start Date</p>
                <p className="font-medium">{new Date(mission.startDate).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                })}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">End Date</p>
                <p className="font-medium">{new Date(mission.endDate).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric'
                })}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Question Count</p>
                <p className="font-medium">{mission.questionCount}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Status</p>
                <p className="font-medium">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${
                    mission.status === 'active' 
                      ? 'bg-green-100 text-green-800' 
                      : mission.status === 'upcoming'
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {mission.status}
                  </span>
                </p>
              </div>
            </div>
            
            <div className="mb-4">
              <h3 className="text-lg font-semibold mb-2">Description</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="whitespace-pre-wrap">{mission.description}</p>
              </div>
            </div>
            
            <div className="flex justify-end">
              <button
                onClick={onClose}
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Mission Q&A List</h1>
      </div>
      
      {/* Tab buttons */}
      <div className="flex border-b border-gray-200 mb-6">
        {tabs.map((tab, index) => (
          <button
            key={index}
            className={`py-3 px-6 font-medium text-sm focus:outline-none ${
              activeTab === index 
                ? 'text-black border-b-2 border-yellow-500 hover:bg-[#FEFCE8]' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => handleTabChange(index)}
          >
            {tab.name}
          </button>
        ))}
      </div>
      
      {/* Table */}
      <NewTablePage
        columns={columns}
        data={missions}
        actions={actions}
        loading={loading}
        
        // Pagination props
        currentPage={currentPage}
        totalPages={totalPages}
        changePage={handlePageChange}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        
        // Search and filter props
        showSearch={true}
        showNameFilter={true}
        showSortFilter={true}
        
        // Pass current state values
        searchTerm={searchTerm}
        searchField={searchField}
        sortField={sortField}
        sortDirection={sortDirection}
        
        // Pass handlers for search, filter and sort
        onSearch={handleSearch}
        onNameFilterChange={handleSearchFieldChange}
        onSort={handleSort}
        
        // Pass name filter options
        nameFilterOptions={searchFilterOptions}
      />
      
      {/* Mission Modal */}
      <MissionModal
        isOpen={showMissionModal}
        onClose={() => setShowMissionModal(false)}
        mission={selectedMission}
      />
    </div>
  );
};

export default MissionQAList;
