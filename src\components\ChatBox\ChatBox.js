'use client';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import React, { useState } from 'react';
import './ChatBox.css';
import ContactInfo from './ContactInfo';

const ChatBox = () => {
  const [chatOpen, setChatOpen] = useState(false);
  const [showContactInfo, setShowContactInfo] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: 'Hello! Welcome To Hello English Coaching. How Are You?',
      sender: 'tutor',
      time: '2:34 PM',
    },
    {
      id: 2,
      text: 'Thank you for inviting me on this amazing platform. Please change my profile picture. I am sharing with you.',
      sender: 'user',
      time: '2:34 PM',
    },
    {
      id: 3,
      image: '/assets/images/all-img/avatar.png',
      sender: 'user',
      time: '2:40 PM',
    },
    {
      id: 4,
      text: 'Thank you for your instant reply. I am updating you image asap.',
      sender: 'tutor',
      time: '2:45 PM',
    },
    {
      id: 5,
      text: 'I have changed your profile picture. You can also do that by yourself from your profile section.',
      sender: 'tutor',
      time: '2:48 PM',
    },
    {
      id: 6,
      text: 'Thank you so much...',
      sender: 'user',
      time: '2:54 PM',
    },
  ]);
  const contact = {
    name: 'Himawari',
    email: '<EMAIL>',
    profileImage: '/assets/images/all-img/avatar.png',
    files: [
      {
        name: 'file-name.jpeg',
        date: '2/13/2023',
        size: '3.5 MB',
      },
      {
        name: 'file-name.jpeg',
        date: '2/13/2023',
        size: '3.5 MB',
      },
      {
        name: 'file-name.jpeg',
        date: '2/13/2023',
        size: '3.5 MB',
      },
      {
        name: 'file-name.jpeg',
        date: '2/13/2023',
        size: '3.5 MB',
      },
      {
        name: 'file-name.jpeg',
        date: '2/13/2023',
        size: '3.5 MB',
      },
    ],
  };

  const [newMessage, setNewMessage] = useState('');

  const handleSendMessage = (e) => {
    e.preventDefault();
    if (newMessage.trim()) {
      const newMsg = {
        id: messages.length + 1,
        text: newMessage,
        sender: 'user',
        time: new Date().toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: 'numeric',
          hour12: true,
        }),
      };
      setMessages([...messages, newMsg]);
      setNewMessage('');
    }
  };

  const toggleContactInfo = () => {
    setShowContactInfo(!showContactInfo);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  return (
    <div className={`fixed ${chatOpen ? 'max-sm:right-1/2 max-sm:translate-x-1/2 max-sm:bottom-2' : 'max-sm:right-5 max-sm:bottom-5'} bottom-5 right-5 z-50`}>
      {!chatOpen ? (
        <button
          onClick={() => setChatOpen(true)}
          className="bg-yellow-400 hover:bg-yellow-500 text-black p-4 rounded-full shadow-lg flex items-center justify-center transition-all hover:scale-105"
          aria-label="Open chat"
        >
          <Icon icon="material-symbols:chat" width="28" height="28" />
        </button>
      ) : (
        <div
          className={`flex bg-white shadow-xl border overflow-hidden no-select ${
            isFullscreen
              ? 'fixed inset-0 z-50'
              : 'rounded-lg max-w-[95vw] w-[500px] max-h-[80vh] h-[600px]'
          }`}
        >
          {/* Main Chat Area */}
          <div className="flex flex-col w-full">
            {/* Chat Header */}
            <header className="bg-yellow-100 p-4 py-2 flex justify-between items-center border-b">
              <div
                onClick={toggleContactInfo}
                className="flex items-center gap-3 cursor-pointer"
              >
                <div className="relative object-cover rounded-full">
                  <Image
                    src={'/assets/images/all-img/avatar.png'}
                    alt={'profile-image'}
                    width={50}
                    height={50}
                    className="rounded-full"
                  />
                </div>
                <div>
                  <h3 className="font-semibold">Tutor Name</h3>
                  <p className="text-sm flex items-center gap-1 text-gray-600">
                    <span className="w-2 h-2 bg-green-500 rounded-full"></span>{' '}
                    Online
                  </p>
                </div>
              </div>

              <div className="flex gap-2">
                <button
                  onClick={toggleContactInfo}
                  className={`p-2 rounded-full ${
                    showContactInfo ? 'bg-yellow-200' : 'hover:bg-yellow-200'
                  }`}
                >
                  <Icon
                    icon="material-symbols:info-outline"
                    width="20"
                    height="20"
                  />
                </button>
                <button
                  onClick={toggleFullscreen}
                  className="p-2 rounded-full hover:bg-yellow-200"
                  aria-label="Toggle fullscreen"
                >
                  <Icon
                    icon={
                      isFullscreen
                        ? 'material-symbols:fullscreen-exit'
                        : 'material-symbols:fullscreen'
                    }
                    width="20"
                    height="20"
                  />
                </button>
                <button
                  onClick={() => setChatOpen(false)}
                  className="p-2 rounded-full hover:bg-yellow-200"
                >
                  <Icon icon="material-symbols:close" width="20" height="20" />
                </button>
              </div>
            </header>

            {/* Chat Messages */}
            <div className="flex-1 overflow-y-auto p-2 bg-gray-50 scrollbar-hide">
              <div className="text-center text-xs text-gray-500 mb-4">
                Today
              </div>

              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`mb-3 flex items-end ${
                    message.sender === 'user' ? 'justify-end' : 'justify-start'
                  }`}
                >
                  {message.sender === 'tutor' && (
                    <div className="w-8 h-8 mb-4 rounded-full overflow-hidden mr-2 flex-shrink-0">
                      <Image
                        src={'/assets/images/all-img/avatar.png'}
                        alt="Tutor"
                        width={32}
                        height={32}
                      />
                    </div>
                  )}

                  <div
                    className={`max-w-[70%] ${
                      message.sender === 'user' ? 'order-1' : 'order-2'
                    }`}
                  >
                    {message.text && (
                      <div
                        className={`p-3 py-2 rounded-lg ${
                          message.sender === 'user'
                            ? 'bg-gray-700 text-white rounded-br-none'
                            : 'bg-yellow-100 text-gray-800 rounded-bl-none'
                        }`}
                      >
                        {message.text}
                      </div>
                    )}

                    {message.image && (
                      <div className="rounded-lg overflow-hidden">
                        <Image
                          src={message.image}
                          alt="Shared image"
                          width={200}
                          height={200}
                          className="object-cover"
                        />
                      </div>
                    )}

                    <div
                      className={`text-xs text-gray-500 mt-1 flex items-center ${
                        message.sender === 'user'
                          ? 'justify-end'
                          : 'justify-start'
                      }`}
                    >
                      {message.time}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Message Input */}
            <form
              onSubmit={handleSendMessage}
              className="p-3 border-t flex items-center gap-2 bg-white relative"
            >
              <div className="flex-1 relative w-[90%]">
                <input
                  placeholder="Type a message..."
                  className="w-full p-1.5 px-2 pr-24 border border-gray-300 overflow-y-auto scrollbar-hide rounded-md bg-gray-50 focus:outline-none focus:border-yellow-400 no-focus-outline resize-none"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  rows="1"
                />
                <div className="flex items-center gap-1 absolute right-2 top-1/2 -translate-y-1/2">
                  <button
                    type="button"
                    className="p-1.5 bg-white rounded text-gray-500 hover:text-blue-500"
                  >
                    <Icon
                      icon="fluent:folder-add-28-filled"
                      width="20"
                      height="20"
                    />
                  </button>
                  <button
                    type="button"
                    className="p-1.5 bg-white rounded text-gray-500 hover:text-blue-500"
                  >
                    <Icon
                      icon="bxs:file"
                      width="20"
                      height="20"
                    />
                  </button>
                  <button
                    type="button"
                    className="p-1.5 bg-white rounded text-gray-500 hover:text-blue-500"
                  >
                    <Icon
                      icon="icon-park-solid:voice"
                      width="20"
                      height="20"
                    />
                  </button>
                </div>
              </div>
              <button
                type="submit"
                className=" p-2 bg-gray-100 rounded-md text-gray-600 hover:bg-yellow-400 hover:text-black transition-colors"
              >
                <Icon icon="mynaui:send-solid" width="24" height="24" />
              </button>
            </form>
          </div>

          {/* Contact Info Sidebar */}
          <div
            className={`w-[300px] border-l absolute right-0 top-0 bottom-0 bg-white z-20 shadow-lg transition-transform duration-300 ${
              isFullscreen ? 'h-screen md:w-1/4 md:relative md:shadow-none' : 'max-h-[80vh] h-[530px]'
            } ${showContactInfo ? 'translate-x-0' : 'translate-x-full hidden'}`}
          >
            <div className="flex gap-3 items-center p-3.5 border-b bg-yellow-100">
              <button
                onClick={toggleContactInfo}
                className="p-2 rounded bg-yellow-200 hover:bg-yellow-300 transition-colors"
              >
                <Icon
                  icon="material-symbols:close-rounded"
                  width="20"
                  height="20"
                />
              </button>
              <h3 className="font-semibold">Contact Info</h3>
            </div>
            <ContactInfo
              contact={contact}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatBox;
