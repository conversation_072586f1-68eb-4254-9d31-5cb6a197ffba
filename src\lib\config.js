/**
 * Simple configuration file with base URLs
 */

// Base URL for API requests and images
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'http://**************:3010';

// API Base URL for all API requests
export const API_BASE_URL = BASE_URL;

// Image Base URL for loading images
export const IMAGE_BASE_URL = BASE_URL;

// Host configuration for Next.js image loader (without protocol)
export const HOST_CONFIG = '**************';

// Default export with all config values
const config = {
  API_BASE_URL,
  IMAGE_BASE_URL,
  HOST_CONFIG
};

export default config;
