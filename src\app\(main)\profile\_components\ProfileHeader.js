'use client';
import HeaderCard from '@/components/HeaderCard';
import Image from 'next/image';
import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import GoBack from '@/components/shared/GoBack';
import { ButtonIcon } from '@/components/Button';
import Link from 'next/link';

const ProfileHeader = ({userDetails}) => {
  const [isBioEditing, setIsBioEditing] = useState(false);
  return (
    <div className="min-h-[60vh] bg-[#FFFDF5]">
      <div className="relative">
        <div>
          <HeaderCard text="" bgColor="#FFFDF5" textColor="#333" textClass="" />
        </div>

        <div className="max-w-7xl mx-auto px-5 xl:px-0 pt-5 absolute left-1/2 -translate-x-1/2 z-10 w-full max-sm:top-0 top-5">
          {/* Main Content */}
          <div className="flex flex-col md:flex-row gap-8 md:mt-4">
            {/* Left Content */}
            <div className="w-full md:w-3/5 max-sm:space-y-1 space-y-4">
              {/* Profile Header */}
              <GoBack title={'Profile Details'} />

              <div className="space-y-2">
                <h2 className="max-sm:text-2xl text-4xl font-semibold text-gray-800">
                  {userDetails?.name}
                </h2>
                <p className="max-sm:text-base text-xl font-medium text-gray-700">HEC Diary</p>

                <div className="mt-4 max-w-64">
                  <Link href={'/diary'} className=" flex items-center gap-2 bg-[#FFF189] hover:bg-yellow-300 text-yellow-800 border border-yellow-600 px-7 py-2 rounded-full">
                    Write Today's Diary{' '}
                    <ButtonIcon
                      icon={'majesticons:arrow-right-line'}
                      innerBtnCls={'h-8 w-8'}
                    />
                  </Link>
                </div>
              </div>

              {/* Bio Section */}
              <div className="pt-12">
                <div className="flex items-center justify-between">
                  <p className="font-semibold text-gray-800">Bio</p>

                  <ButtonIcon
                    onClick={() => setIsBioEditing(true)}
                    icon={'iconamoon:edit-fill'}
                    innerBtnCls={'h-10 w-10'}
                  />
                </div>
                <div className="">
                  {isBioEditing ? (
                    <textarea
                      className="w-full min-h-28 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-1 focus:ring-yellow-400"
                      placeholder="Write your bio here..."
                      defaultValue={userDetails?.bio}
                    ></textarea>
                  ) : (
                    <p className={userDetails?.bio ? 'text-gray-700' : 'text-gray-400'}>
                      {userDetails?.bio ? userDetails.bio : "Bio not available"}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Right Content - Profile Image */}
            <div className="w-full md:w-2/5 flex justify-center bg-gray-200 relative rounded-lg">
              <div className="relative">
                <Image
                  src="/assets/images/all-img/avatar.png"
                  alt="Profile"
                  width={500}
                  height={500}
                  className="rounded-lg shadow-md object-cover"
                />
                <div className="absolute -bottom-2 -right-2 bg-yellow-400 rounded-full p-1.5 shadow-sm z-30">
                  <Icon
                    icon="mdi:camera-outline"
                    className="text-white w-4 h-4"
                  />
                </div>
              </div>

              {/* Awards Section */}
              <div className="absolute bottom-5 w-[90%] mx-auto rounded-lg">
                <div className="flex bg-white rounded-lg overflow-hidden shadow-sm">
                  {/* Diary Award */}
                  <div className="flex-1 py-4 px-6 text-center border-r border-gray-200">
                    <h3 className="text-sm font-medium text-gray-700">
                      Diary Award
                    </h3>
                    <p className="text-2xl font-bold text-yellow-500 mt-1">2</p>
                  </div>

                  {/* Essay Award */}
                  <div className="flex-1 py-4 px-6 text-center border-r border-gray-200">
                    <h3 className="text-sm font-medium text-gray-700">
                      Essay Award
                    </h3>
                    <p className="text-2xl font-bold text-yellow-500 mt-1">6</p>
                  </div>

                  {/* Novel Award */}
                  <div className="flex-1 py-4 px-6 text-center">
                    <h3 className="text-sm font-medium text-gray-700">
                      Novel Award
                    </h3>
                    <p className="text-2xl font-bold text-yellow-500 mt-1">6</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="h-[700px] lg:h-[300px]"></div>
      </div>
    </div>
  );
};

export default ProfileHeader;
