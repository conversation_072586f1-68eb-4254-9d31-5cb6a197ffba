'use client';
import React, { useRef, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import Moveable from "react-moveable";
import { updateCanvasItem, deleteCanvasItem, selectPreviewMode } from "../../store/features/canvasSlice";
import "./ResizableWrapper.css";

const ResizableWrapper = ({
  id,
  children,
  styles,
  selectedId,
  onSelect,
  zIndex = 1,
  type
 }) => {
  // Get preview mode directly from Redux
  const previewMode = useSelector(selectPreviewMode);
  // Get the current item from Redux store to access all its styles
  const currentItem = useSelector(state =>
    state.canvas.canvasItems.find(item => item.id === id)
  );

  const elementRef = useRef(null);
  const moveableRef = useRef(null);

  // Use styles or default values
  const defaultStyles = { x: 0, y: 0, width: 100, height: 100 };
  const initialStyles = styles || defaultStyles;

  const [position, setPosition] = useState({ x: initialStyles.x, y: initialStyles.y });
  const [size, setSize] = useState({ width: initialStyles.width, height: initialStyles.height });
  const isSelected = selectedId === id;

  useEffect(() => {
    if (moveableRef.current) {
      moveableRef.current.updateRect();
    }
  }, [position, size]);

  const dispatch = useDispatch();

  // Debounce function to limit how often we update Redux
  const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  };

  // Debounced function to update Redux store
  const updateReduxStore = debounce((newPosition, newSize) => {
    // Create the updated styles object with all preserved properties
    const updatedStyles = {
      ...(currentItem?.styles || {}), // Preserve all existing styles
      x: newPosition.x,
      y: newPosition.y,
      width: newSize.width,
      height: newSize.height
    };

    // Preserve all existing style properties while updating position and size
    dispatch(updateCanvasItem({
      id,
      updates: {
        styles: updatedStyles
      }
    }));
  }, 200);

  const handleDrag = ({ target, beforeTranslate }) => {
    const [x, y] = beforeTranslate;
    // Allow movement outside the canvas
    target.style.transform = `translate(${x}px, ${y}px)`;
    const newPosition = { x, y };
    setPosition(newPosition);

    // Update Redux store with debounce
    updateReduxStore(newPosition, size);
  };

  const handleResize = ({ target, width, height, drag }) => {
    const { beforeTranslate } = drag;
    const [x, y] = beforeTranslate;
    // Allow resizing without constraints
    target.style.width = `${width}px`;
    target.style.height = `${height}px`;
    target.style.transform = `translate(${x}px, ${y}px)`;

    const newPosition = { x, y };
    const newSize = { width, height };

    setPosition(newPosition);
    setSize(newSize);

    // Update Redux store with debounce
    updateReduxStore(newPosition, newSize);
  };

  const handleClick = (e) => {
    e.stopPropagation();
    // Only allow selection when not in preview mode
    if (!previewMode) {
      onSelect(id);
    }
  };

  const handleDelete = (e) => {
    e.stopPropagation();
    dispatch(deleteCanvasItem(id));
  };

  return (
    <>
      <div
        ref={elementRef}
        onClick={handleClick}
        className={`moveable-element ${isSelected && !previewMode ? "selected" : ""}`}
        style={{
          position: "absolute",
          width: size.width,
          height: size.height,
          transform: `translate(${position.x}px, ${position.y}px)`,
          border: isSelected && !previewMode ? "2px solid blue" : "none",
          overflow: "visible",
          zIndex: zIndex,
          cursor: previewMode ? "default" : "pointer"
        }}
      >
        {children}

        {/* Delete button for images - only show when not in preview mode */}
        {isSelected && type === 'image' && !previewMode && (
          <button
            onClick={handleDelete}
            className="delete-icon"
            title="Delete image"
          >
            ×
          </button>
        )}
      </div>

      {isSelected && !previewMode && elementRef.current && (
        <Moveable
          ref={moveableRef}
          target={elementRef.current}
          container={null}
          origin={false}
          draggable={true}
          resizable={true}
          onDrag={handleDrag}
          onResize={handleResize}
          snappable
          // No bounds to allow movement outside canvas
        />
      )}
    </>
  );
};

export default ResizableWrapper;
