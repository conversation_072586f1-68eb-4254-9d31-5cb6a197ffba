
import Image from 'next/image';

const DiaryCard = ({
    imageSrc,
    date,
    text,
    name,
    profileImage,
    clicks,
    datePosition,
    textPosition,
    backgroundImage = "bg-[#FCF8EF]",
    className = "",
    additionalImages = [],
    classNamesForAdditionalImages = [],
    extraButtonText,
    extraButtonAction,
    imagePosition = "mt-0",
    imageClassName = ""
}) => {
    const datePositionClass = datePosition || "left-36 -mt-32";
    const textPositionClass = textPosition || "-mt-28 left-36 right-10";
    const imagePositionClass = imagePosition;

    

    return (
        <div
            className={`card ${backgroundImage} rounded-2xl shadow-md p-4 flex flex-col items-center ${className}`}
        >
            {/* Image Container with animation */}
            <div
                className="image-container mb-4 relative"
            >
                {imageSrc && (
                    <Image
                        src={imageSrc}
                        alt="Diary Image"
                        width={350}
                        height={350}
                        className={`object-cover ${imagePositionClass} ${imageClassName} transition-transform transform hover:scale-105`}
                    />
                )}
                <p className={`absolute ${datePositionClass} font-bold text-gray-600 text-[8px] bg-opacity-70 p-2 rounded-md`}>
                    {date}
                </p>
                <p className={`absolute ${textPositionClass} text-gray-600 text-[8px] bg-opacity-70 p-2 rounded-md`}>{text}</p>
            </div>

            {/* Additional Images */}
            {additionalImages.map((src, index) => (
                src && (
                    <div
                        key={index}
                        className={`additional-image-container mb-4 relative ${classNamesForAdditionalImages[index] || ''}`}
                    >
                        <Image
                            src={src}
                            alt={`Additional Image ${index + 1}`}
                            width={150}
                            height={150}
                            className="object-cover transition-transform transform hover:scale-105"
                        />
                    </div>
                )
            ))}

            {/* Footer with Profile Image and Interactions */}
            <div className="flex items-center space-x-4">
                {profileImage && (
                    <Image
                        src={profileImage}
                        alt="Profile Icon"
                        width={80}
                        height={80}
                        className="rounded-full object-cover -ml-0"
                    />
                )}
                <div className="footer flex flex-col items-center">
                    <p className="text-black font-inter">{name}</p>
                    <p className="text-black font-inter font-bold">{clicks}</p>
                </div>
                <div className="border-l-2 border-gray-300 h-12"></div>

                {/* Main Button */}
                <button
                    className="mt-1 bg-[#FFFAC2] text-[#723F11] px-4 py-1 rounded-full flex items-center justify-center space-x-1 hover:bg-yellow-500 border-2 border-[#723F11] transition-all transform hover:scale-105"
                >
                    <span className="text-lg font-medium">Click Here</span>
                    <Image
                        src="/assets/images/all-img/introduction/Group.png"
                        alt="Click Icon"
                        width={25}
                        height={25}
                        className="rounded-full object-cover mt-1"
                    />
                </button>

                {/* Extra Button */}
                {extraButtonText && (
                    <button
                        onClick={extraButtonAction}
                        className="mt-4 bg-[#FFA500] text-white px-6 py-2 rounded-full flex items-center justify-center space-x-4 hover:bg-orange-700 transition-all transform hover:scale-105"
                    >
                        <span className="text-lg font-medium">{extraButtonText}</span>
                    </button>
                )}
            </div>
        </div>
    );
};

export default DiaryCard;
