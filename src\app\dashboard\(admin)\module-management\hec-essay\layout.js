"use client";

import React from 'react';
import { useRouter } from 'next/navigation';

const EssayLayout = ({ children }) => {
  const router = useRouter();
  
  const data = [
    {
      name: 'HEC Essay List',
      value: 'hecEssayList',
      path: '/dashboard/module-management/hec-essay/hec-essay-list',
    },
    {
      name: 'Mission Essay List',
      value: 'missionEssayList',
      path: '/dashboard/module-management/hec-essay/mission-essay-list',
    },
    {
      name: 'Create Mission Essay',
      value: 'createMissionEssay',
      path: '/dashboard/module-management/hec-essay/mission-essay-editor',
    },
    {
      name: 'Award List',
      value: 'awardList',
      path: '/dashboard/module-management/hec-essay/award-list',
    },
  ];

  const handleBack = () => {
    router.back();
  };

  return (
    <div className="flex">
      {/* Left sidebar */}
      <div className="w-64 bg-yellow-50 h-[900px]">
        {/* Back button and header */}
        <div className="flex items-center p-4 bg-white">
          <button 
            onClick={handleBack} 
            className="mr-2 text-gray-600 hover:text-gray-900"
            aria-label="Go back"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M19 12H5M12 19l-7-7 7-7"/>
            </svg>
          </button>
          <h2 className="text-lg font-medium">HEC Essay</h2>
        </div>
        
        {/* Navigation menu */}
        <nav>
          <ul>
            {data.map((item) => (
              <li key={item.value}>
                <a 
                  href={item.path}
                  className={`block px-4 py-3 transition-colors ${
                    item.highlighted 
                      ? 'bg-yellow-300 font-medium' 
                      : 'hover:bg-yellow-200'
                  }`}
                >
                  {item.name}
                </a>
              </li>
            ))}
          </ul>
        </nav>
      </div>
      
      {/* Main content area */}
      <div className="flex-1 bg-gray-50">
        {children}
      </div>
    </div>
  );
};

export default EssayLayout;