'use client';
import useDataFetch from '@/hooks/useDataFetch';
import { useParams, useRouter } from 'next/navigation';
import React, { useState } from 'react';
import { Icon } from '@iconify/react';
import RegularGoBack from '@/components/shared/RegularGoBack';
import { format } from 'date-fns';
import parse from 'html-react-parser';
import DeleteModal from '@/components/form/modal/DeleteModal';
import api from '@/lib/api';

const QuestionSetDetails = () => {
  const { id } = useParams();
  const router = useRouter();
  const [deleteData, setDeleteData] = useState(null);

  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['question-set-details', id],
    endPoint: `/play/waterfall/admin/sets/${id}/full`,
  });

  const questions = data?.questions || [];

  // Function to render HTML content safely
  const renderHtml = (htmlContent) => {
    try {
      return parse(htmlContent);
    } catch (error) {
      return htmlContent;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white p-5 flex justify-center items-center">
        <div className="flex items-center space-x-2">
          <Icon
            icon="eos-icons:loading"
            className="text-yellow-500 text-3xl animate-spin"
          />
          <span className="text-gray-600">Loading question set details...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white space-y-5">
      {/* <RegularGoBack title="Back to Question Sets" linkClass="" /> */}

      {/* Header Section */}
      <div className="bg-white rounded-lg border shadow-md p-6">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">{data?.title}</h1>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={() =>
                router.push(
                  `/dashboard/module-management/hec-play/waterfall/${id}/add-question`
                )
              }
              className="flex items-center px-4 py-2 bg-yellow-400 hover:bg-yellow-500 text-black rounded-md transition-colors"
            >
              <Icon icon="material-symbols:add" className="mr-1" />
              Add Question
            </button>
            <button
              onClick={() =>
                router.push(
                  `/dashboard/module-management/hec-play/waterfall/edit/${id}`
                )
              }
              className="flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-md transition-colors"
            >
              <Icon icon="material-symbols:edit-outline" className="mr-1" />
              Edit Set
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <div className="text-sm text-gray-500">Total Score</div>
            <div className="text-2xl font-bold text-gray-800">
              {data?.total_score}
            </div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <div className="text-sm text-gray-500">Total Questions</div>
            <div className="text-2xl font-bold text-gray-800">
              {data?.total_questions}
            </div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <div className="text-sm text-gray-500">Score Per Question</div>
            <div className="text-2xl font-bold text-gray-800">
              {data?.total_questions > 0
                ? Math.round((data?.total_score / data?.total_questions) * 10) /
                  10
                : 0}
            </div>
          </div>
        </div>
      </div>

      {/* Questions Section */}
      <div className="bg-white rounded-lg shadow-md border p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Questions</h2>

        {questions?.length < 1 ? (
          <div className="text-center py-8 text-gray-500">
            <Icon
              icon="material-symbols:help-outline"
              className="text-4xl mx-auto mb-2"
            />
            <p>No questions have been added to this set yet.</p>
            <button
              onClick={() =>
                router.push(
                  `/dashboard/module-management/hec-play/waterfall/${id}/add-question`
                )
              }
              className="mt-4 px-4 py-2 bg-yellow-400 hover:bg-yellow-500 text-black rounded-md transition-colors"
            >
              Add First Question
            </button>
          </div>
        ) : (
          <div className="space-y-6">
            {questions.map((question, index) => (
              <div
                key={question.id}
                className="border border-gray-200 rounded-lg overflow-hidden"
              >
                <div className="bg-gray-50 px-4 py-3 border-b border-gray-200 flex justify-between items-center">
                  <h3 className="font-medium text-gray-800">
                    Question {index + 1}
                  </h3>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setDeleteData(question)}
                      className="p-1 text-red-500 hover:text-red-700"
                    >
                      <Icon icon="material-symbols:delete-outline" width={20} />
                    </button>
                  </div>
                </div>

                <div className="p-4">
                  {/* Question Text */}
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-500 mb-1">
                      Question Text:
                    </h4>
                    <div className="p-3 bg-gray-50 rounded-md text-gray-800">
                      {renderHtml(question.question_text)}
                    </div>
                  </div>

                  {/* Correct Answers */}
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-500 mb-1">
                      Correct Answers:
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {question.correct_answers.map((answer, i) => (
                        <span
                          key={i}
                          className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm"
                        >
                          {answer}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Options */}
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-1">
                      Options:
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {question.options.map((option, i) => {
                        const isCorrect =
                          question.correct_answers.includes(option);
                        return (
                          <span
                            key={i}
                            className={`px-3 py-1 rounded-full text-sm ${
                              isCorrect
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}
                          >
                            {option}
                          </span>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <DeleteModal
        isOpen={!!deleteData}
        onClose={() => setDeleteData(null)}
        onSuccess={refetch()}
        data={deleteData}
        endPoint={`/play/waterfall/admin/questions/${deleteData?.id}`}
        itemName="question"
      />
    </div>
  );
};

export default QuestionSetDetails;
