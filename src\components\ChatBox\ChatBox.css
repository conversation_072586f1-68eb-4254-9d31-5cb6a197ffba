/* Hide scrollbars but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* Remove resize handle from textareas */
textarea {
  resize: none;
}

/* Hide other browser controls */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Remove focus outlines */
.no-focus-outline:focus {
  outline: none;
}

/* Hide browser default form styling */
input, textarea, select, button {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

/* Hide video controls */
video::-webkit-media-controls {
  display: none !important;
}

/* Prevent text selection */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
