import { NextResponse } from 'next/server';

export function middleware(request) {
  const token = request.cookies.get('token')?.value;
  const userRole = request.cookies.get('userRole')?.value;
  const isLoginPage = request.nextUrl.pathname === '/login';
  const isRegisterPage = request.nextUrl.pathname === '/register';
  const isAuthPage = isLoginPage || isRegisterPage || request.nextUrl.pathname.startsWith('/password-reset') || request.nextUrl.pathname.startsWith('/verify-email');

  // If trying to access auth pages with token, redirect to home
  if (isAuthPage && token) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  // Protected routes that require authentication
  const protectedPaths = ['/diary', '/dashboard', '/pricing-plans'];
  const isProtectedPath = protectedPaths.some((path) =>
    request.nextUrl.pathname.startsWith(path)
  );

  // If trying to access protected route without token, redirect to login
  if (isProtectedPath && !token) {
    const loginUrl = new URL('/login', request.url);
    // Append the returnTo parameter to the login URL
    loginUrl.searchParams.set('returnTo', request.nextUrl.pathname);
    return NextResponse.redirect(loginUrl);
  }

  // Role-based access control for specific routes
  if (token && userRole) {
    // Define role-based permissions for routes
    const rolePermissions = {
      admin: [
        '/dashboard/admin',
        '/dashboard/settings',
        '/dashboard/users',
        '/diary'
      ],
      tutor: [
        '/dashboard/tutor',
        '/dashboard/qa',
        '/dashboard/essay',
        '/diary'
      ],
      student: [
        '/dashboard/student',
        '/dashboard/qa',
        '/dashboard/essay',
        '/dashboard/novel',
        '/diary'
      ]
    };

    // Check if the current path requires role-based access control
    const currentPath = request.nextUrl.pathname;

    // If accessing dashboard routes, check role permissions
    if (currentPath.startsWith('/dashboard/')) {
      const allowedPaths = rolePermissions[userRole] || [];
      const isPathAllowed = allowedPaths.some(path => currentPath.startsWith(path));

      if (!isPathAllowed) {
        // Redirect to unauthorized page if user doesn't have permission
        return NextResponse.redirect(new URL('/unauthorized', request.url));
      }
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/diary/:path*',
    '/dashboard/:path*',
    '/login',
    '/register',
    '/password-reset/:path*',
    '/verify-email/:path*',
    '/pricing-plans/:path*',
    '/unauthorized'
  ],
};