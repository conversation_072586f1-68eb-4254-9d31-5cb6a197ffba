'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';

/**
 * A custom emoji selector modal that appears near the button that triggers it
 *
 * @param {Object} props
 * @param {boolean} props.isOpen - Whether the modal is open
 * @param {Function} props.onClose - Function to call when the modal is closed
 * @param {Function} props.onSelect - Function to call when an emoji is selected
 * @param {Object} props.triggerRef - Reference to the button that triggers the modal
 * @param {string} props.position - Position of the modal relative to the trigger (top, right, bottom, left)
 */
const EmojiSelectorModal = ({
  isOpen,
  onClose,
  onSelect,
  triggerRef,
  position = 'bottom',
}) => {
  const modalRef = useRef(null);

  // Sample emoji data - replace with actual data from API later
  const emojis = [
    { id: 1, emoji: '💛' },
    { id: 2, emoji: '😊' },
    { id: 3, emoji: '🥑' },
    { id: 4, emoji: '😎' },
    { id: 5, emoji: '💛' },
    { id: 6, emoji: '💛' },
    { id: 7, emoji: '😢' },
    { id: 8, emoji: '😄' },
    { id: 9, emoji: '💛' },
  ];

  // Close when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target) &&
        triggerRef.current &&
        !triggerRef.current.contains(event.target)
      ) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose, triggerRef]);

  // Calculate position based on trigger element
  const getPosition = () => {
    if (!triggerRef.current) return {};

    const rect = triggerRef.current.getBoundingClientRect();

    switch (position) {
      case 'top':
        return {
          bottom: `${window.innerHeight - rect.top + 10}px`,
          left: `${rect.left + rect.width / 2 - 140}px`, // Center the modal
        };
      case 'right':
        return {
          left: `${rect.right + 10}px`,
          top: `${rect.top + rect.height / 2 - 140}px`, // Center the modal
        };
      case 'left':
        return {
          right: `${window.innerWidth - rect.left - 4}px`,
          top: `${rect.top + rect.height / 2 - 155}px`, // Center the modal
        };
      case 'bottom':
      default:
        return {
          top: `${rect.bottom + 10}px`,
          left: `${rect.left + rect.width / 2 - 140}px`, // Center the modal
        };
    }
  };

  // Get arrow position based on modal position
  const getArrowStyle = () => {
    switch (position) {
      case 'top':
        return {
          bottom: '100%',
          left: '50%',
          transform: 'translateX(-50%)',
          borderLeft: '8px solid transparent',
          borderRight: '8px solid transparent',
          borderBottom: '8px solid #FFD54F',
        };
      case 'right':
        return {
          left: '-8px',
          top: '50%',
          transform: 'translateY(-50%)',
          borderTop: '8px solid transparent',
          borderBottom: '8px solid transparent',
          borderRight: '8px solid #FFD54F',
        };
      case 'left':
        return {
          right: '-8px',
          top: '50%',
          transform: 'translateY(-50%)',
          borderTop: '8px solid transparent',
          borderBottom: '8px solid transparent',
          borderLeft: '8px solid #FFD54F',
        };
      case 'bottom':
      default:
        return {
          top: '-8px',
          left: '50%',
          transform: 'translateX(-50%)',
          borderLeft: '8px solid transparent',
          borderRight: '8px solid transparent',
          borderBottom: '8px solid #FFD54F',
          position: 'absolute',
        };
    }
  };

  // Animation variants
  const variants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.8 },
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          ref={modalRef}
          className="fixed z-50 "
          style={getPosition()}
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={variants}
          transition={{ duration: 0.2 }}
        >
          <div className="relative bg-[#FFF8E0] rounded-lg shadow-lg border-2 border-[#FFD54F] p-3 w-[280px]">
            {/* Close button */}
            <button
              className="absolute top-1 right-1 w-10 h-10 hover:opacity-80 transition-opacity"
              onClick={onClose}
              aria-label="Close emoji selector"
            >
              <Image
                src="/assets/images/all-img/cross-bg.png"
                alt="Close"
                width={50}
                height={50}
                className="w-full h-full object-contain"
              />
            </button>

            {/* Arrow pointing to the button */}
            <div className="absolute w-0 h-0 " style={getArrowStyle()}></div>

            {/* Header */}
            <div className="mb-3 text-center ">
              {/* <h3 className="text-sm font-medium text-gray-700">Select an emoji</h3> */}
            </div>

            {/* Grid of emojis */}
            <div className="grid grid-cols-3 gap-2 p-5">
              {emojis.map((item) => (
                <div
                  key={item.id}
                  className="rounded-lg shadow-sm p-3 flex items-center justify-center cursor-pointer hover:shadow-md hover:scale-105 transition-all duration-200 w-full h-[70px]"
                  style={{
                    background: 'linear-gradient(to bottom, #FFFDF5, #FFF8E0)',
                    border: '1px solid #FFD54F',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                  }}
                  onClick={() => {
                    onSelect(item);
                    onClose();
                  }}
                >
                  <span className="text-4xl">{item.emoji}</span>
                </div>
              ))}
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default EmojiSelectorModal;
