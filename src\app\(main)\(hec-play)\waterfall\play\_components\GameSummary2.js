'use client';
import React from 'react';
import { Icon } from '@iconify/react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';

const GameSummary2 = ({ resultData, onRestart }) => {
  // Extract data from API response
  const {
    set_title,
    total_correct_answers,
    total_questions,
    score,
    answers,
    submitted_at
  } = resultData;

  // Calculate percentage score
  const percentage = score || (total_questions > 0 ? Math.round((total_correct_answers / total_questions) * 100) : 0);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white rounded-lg max-w-7xl mx-auto p-5 xl:px-0"
    >
      <div className="flex items-start">
        <div className="w-full pr-8">
          <h2 className="text-2xl font-bold mb-4">Game Completed!</h2>

          {set_title && (
            <p className="text-gray-600 mb-2">Set: {set_title}</p>
          )}

          <div className="bg-yellow-50 p-6 rounded-lg mb-6">
            <h3 className="text-lg font-semibold mb-4">Your Score</h3>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-4xl font-bold text-yellow-500">{percentage}%</div>
                <p className="text-gray-600 mt-1">
                  {total_correct_answers} correct out of {total_questions} questions
                </p>
              </div>
              <div className="flex gap-4">
                <div className="bg-green-100 text-green-800 px-4 py-2 rounded-lg">
                  <div className="text-sm font-medium">Correct</div>
                  <div className="text-2xl font-bold">{total_correct_answers}</div>
                </div>
                <div className="bg-red-100 text-red-800 px-4 py-2 rounded-lg">
                  <div className="text-sm font-medium">Incorrect</div>
                  <div className="text-2xl font-bold">{total_questions - total_correct_answers}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Answer details */}
          {answers && answers.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-3">Answer Details</h3>
              <div className="space-y-3">
                {answers.map((answer, index) => (
                  <div
                    key={answer.question_id}
                    className={`p-4 rounded-lg border ${answer.is_correct ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}
                  >
                    <div className="flex items-start">
                      <div className={`rounded-full p-1 mr-3 ${answer.is_correct ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`}>
                        <Icon icon={answer.is_correct ? 'mdi:check' : 'mdi:close'} className="text-xl" />
                      </div>
                      <div>
                        <p className="font-medium">Question {index + 1}</p>
                        <div className="mt-2">
                          <p className="text-sm">
                            <span className="font-medium">Your answer: </span>
                            {answer?.answers?.join(', ')}
                          </p>
                          {!answer.is_correct && (
                            <p className="text-sm text-green-700 mt-1">
                              <span className="font-medium">Correct answer: </span>
                              {answer?.correct_answers?.join(', ')}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="flex justify-center gap-4 mt-8">
            <button
              onClick={onRestart}
              className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 rounded-full text-black font-medium"
            >
              Play Again
            </button>

            <Link
              href="/waterfall/play"
              className="px-6 py-3 bg-gray-100 hover:bg-gray-200 rounded-full text-gray-700 font-medium"
            >
              Back to Games
            </Link>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default GameSummary2;
