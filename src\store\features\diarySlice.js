// src/store/features/diarySlice.js
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  subject: '',
  message: '',
  selectedSkin: null,
  isSaving: false,
  isLoading: true,
  todayEntry: null,
  isSkinModalOpen: false,
  layoutBackground: '#FFFDF5',
};

const diarySlice = createSlice({
  name: 'diary',
  initialState,
  reducers: {
    setSubject: (state, action) => {
      state.subject = action.payload;
    },
    setMessage: (state, action) => {
      state.message = action.payload;
    },
    setSelectedSkin: (state, action) => {
      state.selectedSkin = action.payload;
    },
    setIsSaving: (state, action) => {
      state.isSaving = action.payload;
    },
    setIsLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    setTodayEntry: (state, action) => {
      state.todayEntry = action.payload;
    },
    setIsSkinModalOpen: (state, action) => {
      state.isSkinModalOpen = action.payload;
    },
    setLayoutBackground: (state, action) => {
      state.layoutBackground = action.payload;
    },
    resetDiaryState: () => initialState,
  },
});

export const {
  setSubject,
  setMessage,
  setSelectedSkin,
  setIsSaving,
  setIsLoading,
  setTodayEntry,
  setIsSkinModalOpen,
  setLayoutBackground,
  resetDiaryState,
} = diarySlice.actions;

export const selectDiarySubject = (state) => state.diary.subject;
export const selectDiaryMessage = (state) => state.diary.message;
export const selectSelectedSkin = (state) => state.diary.selectedSkin;
export const selectIsSaving = (state) => state.diary.isSaving;
export const selectIsLoading = (state) => state.diary.isLoading;
export const selectTodayEntry = (state) => state.diary.todayEntry;
export const selectIsSkinModalOpen = (state) => state.diary.isSkinModalOpen;
export const selectLayoutBackground = (state) => state.diary.layoutBackground;

export default diarySlice.reducer;