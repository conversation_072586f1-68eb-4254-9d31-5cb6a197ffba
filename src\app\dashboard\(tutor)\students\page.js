 'use client';
import NewTablePage from '@/components/form/NewTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React from 'react';

const Students = () => {
    const router = useRouter();
    const {data: students, isLoading} = useDataFetch({
        queryKey: 'tutor-students',
        endPoint: '/tutor/students',
    });


    const columns = [
        {
            label: 'STUDENT NAME',
            field: 'name',
            cellRenderer: (_, row) => {
                console.log('Rendering student name cell for:', row);
                return (
                    <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-yellow-200 flex items-center justify-center mr-2 text-xs overflow-hidden">
                            <Image src={row?.profilePicture || '/assets/images/all-img/avatar.png'} alt={'profile-image'} width={32} height={32} />
                        </div>
                        <span>{row.name}</span>
                    </div>
                );
            }
        },
        {
            label: 'USER ID',
            field: 'userId',
        },
        {
            label: 'EMAIL ADDRESS',
            field: 'email',
        },
        {
            label: 'PHONE NUMBER',
            field: 'phoneNumber',
        },
        {
            label: 'GENDER',
            field: 'gender',
        }
    ];

    const actions = [
        {
            name: 'view',
            icon: 'material-symbols:visibility',
            className: 'text-blue-600',
            onClick: (row) => router.push(`/dashboard/students/profile/${row.id}`),
        },
        {
            name: 'message',
            icon: 'material-symbols:chat',
            className: 'text-green-600',
            onClick: (row) => console.log('Message', row),
        },
    ];

    return (
        <div>
            <NewTablePage
                title="Student List"
                showCreateButton={true}
                createButton="Add Student"
                columns={columns}
                actions={actions}
                data={students}
                loading={isLoading}
                showNameFilter={false}
                showSearch={true}
                showSortFilter={false}
                showCheckboxes={false}
            />
        </div>
    );
};

export default Students;