'use client';
import DeleteModal from '@/components/form/modal/DeleteModal';
import NewTablePage from '@/components/form/NewTablePage';
import StatusSwitcher from '@/components/form/StatusSwitcher';
import useDataFetch from '@/hooks/useDataFetch';
import React, { useState } from 'react';

const PromotionApplications = () => {
  const [deleteData, setDeleteData] = useState(null);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);

  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['promotion-applications', currentPage, rowsPerPage],
    endPoint: '/admin/shop/items/with-promotions',
    params: { page: currentPage, limit: rowsPerPage },
  });

  const columns = [
    {
      label: 'Item Title',
      field: 'title',
    },
    {
      label: 'Category',
      field: 'categoryName',
    },
    {
      label: 'Value',
      field: 'discountDisplay',
    },
    {
      label: 'Status',
      field: 'status',
      cellRenderer: (_, row) => {
        return (
          <div className="flex items-center">
            <StatusSwitcher
              endPoint={`/admin/shop/items/${row?.id}/promotion-activation`}
              status={row?.isPromotionActive}
              onSuccess={refetch}
            />
          </div>
        );
      },
    },
  ];

  const actions = [
    {
      name: 'delete',
      icon: 'heroicons-outline:trash',
      className: 'text-red-600',
      onClick: (val) => {
        setDeleteData(val);
      },
    },
  ];

  return (
    <div>

      <NewTablePage
        title="Applied Promotion List"
        createButton="Apply Promotion"
        createBtnLink="/dashboard/shops/promotions/apply"
        columns={columns}
        data={data?.items || []}
        actions={actions}
        loading={isLoading}
        currentPage={currentPage}
        changePage={setCurrentPage}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        showCreateButton={true}
        showNameFilter={false}
        showSearch={true}
        showSortFilter={true}
        showCheckboxes={false}
        totalPages={Math.ceil(totalItems / rowsPerPage)}
      />

      <DeleteModal
        isOpen={!!deleteData}
        onClose={() => setDeleteData(null)}
        onSuccess={refetch}
        data={deleteData}
        endPoint={`/admin/shop/items/${deleteData?.id}/promotion`}
        itemName="item"
      />
    </div>
  );
};

export default PromotionApplications;
