import React from 'react';
import Image from 'next/image';


const HeaderCard = ({ text = 'About Us', bgColor = '#FFF9FB', textColor = 'black', textClass = '' }) => {
  
  return (
    <div
    className={` relative ${bgColor === '#FFF9FB' ? 'bg-[#FFF9FB]' : 'bg-[#FEFCE8]'} text-white shadow-md min-h-52 lg:min-h-72 flex-col items-center justify-center mx-auto rounded-lg overflow-hidden`}

    >
      <div  className="absolute mt-4 left-20 w-1/3 h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus1.png" alt="icon" width={140} height={140} priority />
      </div>

      <div className="absolute mt-28 left-20 w-1/3 h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus3.png" alt="icon" width={140} height={140} priority />
      </div>

      <div className="absolute mt-52 left-10 w-1/3 h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus4.png" alt="icon" width={40} height={40} priority />
      </div>

      <div className="absolute mt-16 left-80 w-1/3 h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus5.png" alt="icon" width={70} height={70} priority />
      </div>

      <div className="absolute mt-16 left-16 w-1/3 h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus2.png" alt="icon" width={70} height={70} priority />
      </div>

      <div className="absolute mt-56 left-72 w-1/3 h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus2.png" alt="icon" width={70} height={70} priority />
      </div>

      <div className="absolute mt-16 left-[650px] w-1/3 h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus2.png" alt="icon" width={70} height={70} priority />
      </div>

      <div className="absolute mt-24 left-[450px] w-1/3 h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus11.png" alt="icon" width={50} height={50} priority />
      </div>

      <div className="absolute mt-52 left-[700px] w-1/3 h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus12.png" alt="icon" width={120} height={120} priority />
      </div>

      <div className="absolute mt-52 left-[900px] w-1/3 h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus2.png" alt="icon" width={70} height={70} priority />
      </div>

      <div className="absolute mt-14 left-[1100px] w-1/3 h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus2.png" alt="icon" width={70} height={70} priority />
      </div>

      <div className="absolute mt-6 left-[1000px] w-1/3 h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus.png" alt="icon" width={40} height={40} priority />
      </div>

      <div className="absolute mt-56 left-[1050px] w-1/3 h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus.png" alt="icon" width={40} height={40} priority />
      </div>

      <div className="absolute mt-36 left-[1200px] w-1/3 h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus.png" alt="icon" width={40} height={40} priority />
      </div>

      <div className="absolute mt-24 right-[400px] h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus4.png" alt="icon" width={40} height={40} priority />
      </div>

      <div className="absolute mt-14 right-[350px] h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus4.png" alt="icon" width={40} height={40} priority />
      </div>

      <div className="absolute mt-32 right-8 h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus10.png" alt="icon" width={140} height={140} priority />
      </div>

      <div className="absolute mt-52 right-[200px] h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus8.png" alt="icon" width={40} height={40} priority />
      </div>

      <div className="absolute mt-44 right-[300px] h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus2.png" alt="icon" width={70} height={70} priority />
      </div>

      <div className="absolute mt-10 right-[150px] h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus.png" alt="icon" width={40} height={40} priority />
      </div>

      <div className="absolute mt-5 right-[200px] h-full">
        <Image src="/assets/images/all-img/aboutus/aboutus9.png" alt="icon" width={140} height={140} priority />
      </div>

      <div style={{ width: '628px', height: '160px' }}>
        <p
          className={`absolute left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2 text-black ${textClass} flex items-center justify-center text-3xl sm:text-5xl font-medium`}>
          {text}
        </p>
      </div>
    </div>
  );
};

export default HeaderCard;
