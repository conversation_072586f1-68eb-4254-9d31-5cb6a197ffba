'use client';
import React, { useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { Icon } from '@iconify/react';
import RegularGoBack from '@/components/shared/RegularGoBack';

const PlanFeatureDetails = () => {
  const { id } = useParams();
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);

  const { data: feature, isLoading } = useDataFetch({
    queryKey: ['plan-feature-details', id],
    endPoint: `/plan-features/${id}`,
  });

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this feature?')) {
      try {
        setIsDeleting(true);
        await api.delete(`/plan-features/${id}`);
        router.push('/dashboard/plan-features');
      } catch (error) {
        console.log(error);
        alert('Failed to delete feature. It might be in use by one or more plans.');
      } finally {
        setIsDeleting(false);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  return (
    <div className="p-5">
      <RegularGoBack className={'pb-5 max-w-32'} title={'Plan Features'} />

      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-800">{feature?.name}</h1>
            <div className="flex gap-3">
              <button
                onClick={() => router.push(`/dashboard/plan-features/edit/${id}`)}
                className="px-6 py-1 bg-[#FFE924] rounded-md flex items-center gap-2 hover:bg-[#FFE924]/90 transition-colors"
              >
                <Icon icon="solar:pen-2-linear" className="w-4 h-4" />
                Edit Feature
              </button>
              <button 
                onClick={handleDelete} 
                className="px-6 py-1 bg-red-50 text-red-600 rounded-md flex items-center gap-2 hover:bg-red-100 transition-colors"
              >
                <Icon icon="solar:trash-bin-trash-linear" className="w-4 h-4" />
                {isDeleting ? "Deleting..." : "Delete"}
              </button>
            </div>
          </div>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h2 className="text-lg font-semibold mb-4 text-gray-700">Feature Information</h2>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-500">Feature Type</p>
                  <p className="font-medium">{feature?.type}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Status</p>
                  <p className={`font-medium ${feature?.isActive ? 'text-green-600' : 'text-red-600'}`}>
                    {feature?.isActive ? 'Active' : 'Inactive'}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Created At</p>
                  <p className="font-medium">
                    {feature?.createdAt ? new Date(feature.createdAt).toLocaleString() : 'N/A'}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Updated At</p>
                  <p className="font-medium">
                    {feature?.updatedAt ? new Date(feature.updatedAt).toLocaleString() : 'N/A'}
                  </p>
                </div>
              </div>
            </div>

            <div>
              <h2 className="text-lg font-semibold mb-4 text-gray-700">Description</h2>
              <p className="text-gray-700 whitespace-pre-line">{feature?.description}</p>
            </div>
          </div>

          <div className="mt-8">
            <h2 className="text-lg font-semibold mb-4 text-gray-700">Metadata</h2>
            <div className="bg-gray-50 p-4 rounded-lg">
              <pre className="text-sm text-gray-700 overflow-x-auto">
                {JSON.stringify(feature, null, 2)}
              </pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlanFeatureDetails;
