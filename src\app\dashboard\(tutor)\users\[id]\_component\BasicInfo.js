import React, { useState } from 'react';
import { Formik, Form } from 'formik';
import { Icon } from '@iconify/react';

const BasicInfo = () => {
  const [isEditing, setIsEditing] = useState(false);

  const initialValues = {
    firstName: 'John',
    lastName: 'Doe',
    gender: 'Male',
    dateOfBirth: '27th September, 1996',
    phoneNumber: '(+880) 1XXXXXXXXX',
    email: '<EMAIL>',
    tutorId: '123456789',
  };

  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      // Handle form submission
      console.log('Updated Values:', values);
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating info:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const renderField = (label, name, type = 'text', values, options = []) => (
    <div>
      <label className="block text-base font-semibold text-gray-700 mb-1">{label}</label>
      {isEditing ? (
        type === 'select' ? (
          <select
            name={name}
            defaultValue={values[name]}
            className="w-full px-3 py-1 border rounded-md focus:outline focus:outline-yellow-500"
          >
            {options.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        ) : (
          <input
            type={type}
            name={name}
            defaultValue={values[name]}
            className="w-full px-3 py-2 border focus:outline focus:outline-yellow-500 rounded-md"
          />
        )
      ) : (
        <p>{values[name]}</p>
        // <input
        //   type="text"
        //   value={values[name]}
        //   className="w-full p-2 border rounded-md"
        //   disabled
        // />
      )}
    </div>
  );

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-6 border">
      <Formik initialValues={initialValues} onSubmit={handleSubmit}>
        {({ values, isSubmitting }) => (
          <Form>
            <div className="flex justify-end items-center relative">
              {!isEditing && (
                <button
                  type="button"
                  onClick={() => setIsEditing(true)}
                  className="p-2 hover:bg-gray-100 rounded-full absolute right-1 top-1 z-10"
                >
                  <Icon icon="material-symbols:edit-outline" className="w-5 h-5" />
                </button>
              )}
            </div>

            <div className="grid md:grid-cols-2 gap-6 xl:gap-10 relative">
              {/* Personal Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Personal Details</h3>
                {renderField('FIRST NAME', 'firstName', 'text', values)}
                {renderField('LAST NAME', 'lastName', 'text', values)}
                {renderField('GENDER', 'gender', 'select', values, [
                  { value: 'male', label: 'Male' },
                  { value: 'female', label: 'Female' },
                  { value: 'other', label: 'Other' }
                ])}
                {renderField('DATE OF BIRTH', 'dateOfBirth', 'date', values)}
              </div>

              {/* Vertical Divider */}
              <div className="absolute left-1/2 top-0 bottom-0 border-l-2 border-dashed border-gray-300 -translate-x-1/2 hidden lg:block"></div>

              {/* Contact Details */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Contact Details</h3>
                {renderField('PHONE NUMBER', 'phoneNumber', 'text', values)}
                {renderField('EMAIL', 'email', 'email', values)}
                {renderField('TUTOR ID', 'tutorId', 'text', values)}
              </div>
            </div>

            {isEditing && (
              <div className="flex justify-end gap-2 mt-4 lg:mt-0">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
                >
                  {isSubmitting ? 'Saving...' : 'Save'}
                </button>
                <button
                  type="button"
                  onClick={() => setIsEditing(false)}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                >
                  Cancel
                </button>
              </div>
            )}
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default BasicInfo;
