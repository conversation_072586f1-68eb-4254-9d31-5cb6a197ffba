import axios from 'axios';
import Cookies from 'js-cookie';
import { API_BASE_URL } from './config';
import { toast } from 'sonner';

// Base Axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  // timeout: 10000, // Optional, you can enable it if needed
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request Interceptor
api.interceptors.request.use(
  (config) => {
    // Get the token from cookies
    const token = Cookies.get('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    toast.error('Request failed. Please check your connection and try again.');
    return Promise.reject(error);
  }
);

// Response Interceptor for Success
api.interceptors.response.use(
  (response) => {
    // Display success message if the request configuration indicates so
    if (
      response?.data?.success &&
      response.config?.method !== 'get' &&
      response.config?.showSuccessToast !== false
    ) {
      toast.success(
        response?.data?.message ||
          response?.message ||
          'Operation completed successfully!'
      );
    }
    return response.data; // Simplify response handling
  },
  (error) => {
    const errorMessage =
      error?.response?.data?.message ||
      error.message ||
      'Something went wrong!';
    toast.error(errorMessage);
    return Promise.reject(error);
  }
);

export default api;
