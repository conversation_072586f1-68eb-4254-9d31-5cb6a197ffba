'use client';

import RegularGoBack from '@/components/shared/RegularGoBack';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import FormSelect from '@/components/form/FormSelect';
import React from 'react';
import useDataFetch from '@/hooks/useDataFetch';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import api from '@/lib/api';
import SelectItems from '../_components/SelectItem';

// Validation schema
const validationSchema = Yup.object().shape({
  promotionId: Yup.string().required('Promotion campaign is required'),
  applicableType: Yup.string().required('Applied to is required'),
  selectedItems: Yup.array().min(1, 'At least one item must be selected'),
});



const ApplyPromotion = () => {
  const router = useRouter();

  // Fetch promotions
  const { data: promotionsData } = useDataFetch({
    queryKey: ['promotions'],
    endPoint: '/promotions/admin',
  });

  // Format promotions for select dropdown
  const promotionOptions = React.useMemo(() => {
    if (!promotionsData?.items) return [];
    return promotionsData.items.map((promo) => ({
      value: promo.id,
      label: promo.name,
    }));
  }, [promotionsData]);

  // Applied to options
  const appliedToOptions = [
    { value: 'shop_item', label: 'Shop Item' },
    { value: 'shop_item_categories', label: 'Shop Item Categories' },
    { value: 'subscription_plans', label: 'Subscription Plans' },
  ];

  // Handle form submission
  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      setSubmitting(true);

      // Format the payload based on the applicable type
      const payload = {
        promotionId: values.promotionId,
      };

      // Add the appropriate item IDs based on the applicable type
      switch (values.applicableType) {
        case 'shop_item':
          payload.itemIds = values.selectedItems.map((item) => item.id);
          break;
        case 'categoryIds':
          payload.categoryIds = values.selectedItems.map((item) => item.id);
          break;
        case 'planIds':
          payload.planIds = values.selectedItems.map((item) => item.id);
          break;
      }

      console.log(payload)
      // Make the API call
      await api.post('/admin/shop/items/apply-promotion', payload);

      router.push('/dashboard/shops/promotions');
    } catch (error) {
      console.error('Error applying promotion:', error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div>
      <RegularGoBack className={'pb-5 max-w-60'} title={'Apply Promotion'} />

      <div className="bg-gray-50 border p-6 rounded-lg ">
        <Formik
          initialValues={{
            promotionId: '',
            applicableType: '',
            selectedItems: [],
          }}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting, values, setFieldValue }) => {
            // Handle removing an item from selected items
            const handleRemoveItem = (itemId) => {
              const updatedItems = values.selectedItems.filter(
                (item) => item.id !== itemId
              );
              setFieldValue('selectedItems', updatedItems);
            };

            return (
              <Form className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 items-center">
                  <FormSelect
                    label="Select Promotion Campaign"
                    name="promotionId"
                    options={promotionOptions}
                    placeholder="Select a promotion"
                    required
                  />

                  <FormSelect
                    label="Applied To"
                    name="applicableType"
                    options={appliedToOptions}
                    placeholder="Select where to apply"
                    required
                  />

                  <SelectItems />
                </div>

                <div>
                  {/* Selected Items */}
                  <div className="w-full bg-white p-5 rounded-lg">
                    <h3 className="font-medium mb-3 text-lg">Selected Items List</h3>
                    <div className="h-[400px] overflow-y-auto w-full">
                      {values.selectedItems.length > 0 ? (
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                          {values.selectedItems.map((item) => (
                            <div
                              key={item.id}
                              className="border rounded-lg bg-white overflow-hidden"
                            >
                              <div className="flex justify-between items-center px-3 py-2 bg-gray-50 border-b">
                                <span className="font-medium text-sm text-gray-700 uppercase">
                                  {item?.title || item?.name}
                                </span>
                                <button
                                  type="button"
                                  onClick={() => handleRemoveItem(item.id)}
                                  className="text-red-500 hover:text-red-700"
                                >
                                  <Icon
                                    icon="mdi:close"
                                    className="w-5 h-5"
                                  />
                                </button>
                              </div>
                              <div className="p-3">
                                {item.filePath ? (
                                  <img
                                    src={item.filePath}
                                    alt={item.name || item.title}
                                    className="w-full h-32 object-contain"
                                  />
                                ) : (
                                  <div className="h-32 flex items-center justify-center bg-gray-100 rounded">
                                    <div className="text-center">
                                      <h3 className="font-medium text-sm text-gray-800">
                                        {item.name || item.title}
                                      </h3>
                                      {values.applicableType === 'shop_item' && item.categoryName && (
                                        <p className="text-xs text-gray-500">
                                          Category: {item.categoryName}
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="flex flex-col items-center justify-center h-[200px] text-gray-500">
                          <Icon
                            icon="mdi:playlist-plus"
                            className="w-10 h-10 mb-2"
                          />
                          <p>No items selected</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex justify-end gap-3">
                  <button
                    type="button"
                    onClick={() => router.push('/dashboard/shops/promotions')}
                    className="px-6 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-6 py-2 bg-yellow-400 rounded-lg hover:bg-yellow-500 transition-colors disabled:opacity-50"
                  >
                    {isSubmitting ? 'Applying...' : 'Apply'}
                  </button>
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
    </div>
  );
};

export default ApplyPromotion;