import React, { useState } from 'react';
import { Formik, Form } from 'formik';
import { Icon } from '@iconify/react';
import FormSelect from '@/components/form/FormSelect';
import FormInput from '@/components/form/FormInput';

const Education = () => {
  const [editingId, setEditingId] = useState(null);
  const [isAddingNew, setIsAddingNew] = useState(false);

  const initialEducations = [
    {
      id: 1,
      title: "Bachelor's in Science",
      institute: "Bangladesh University of Business & Technology (BUBT)",
      completionStatus: "Enrolled",
      department: "CSE",
      passedYear: "2022"
    },
    {
      id: 2,
      title: "Higher Secondary School Certificate (HSC)",
      institute: "Ideal School & College, Motijheel",
      completionStatus: "Passed",
      department: "Science",
      passedYear: "2020"
    }
  ];

  const [educations, setEducations] = useState(initialEducations);

  const completionStatusOptions = [
    { value: 'enrolled', label: 'Enrolled' },
    { value: 'passed', label: 'Passed' },
    { value: 'ongoing', label: 'Ongoing' }
  ];

  const handleSubmit = async (values, { setSubmitting, resetForm }) => {
    console.log(values)
    try {
      if (editingId) {
        setEducations(educations.map(edu => 
          edu.id === editingId ? { ...values, id: edu.id } : edu
        ));
        setEditingId(null);
      } else {
        setEducations([...educations, { ...values, id: Date.now() }]);
        setIsAddingNew(false);
      }
      resetForm();
    } catch (error) {
      console.error('Error updating education:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const newEducationInitialValues = {
    title: "",
    institute: "",
    completionStatus: "",
    department: "",
    passedYear: ""
  };

  const renderEducationItem = (education) => {
    if (editingId === education.id) {
      return (
        <Formik
          key={education.id}
          initialValues={education}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting }) => (
            <Form className="bg-white border-b pb-4 mb-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <FormInput label="TITLE" name="title" type="text" />
                  <FormInput label="INSTITUTE" name="institute" type="text" />
                  <FormSelect
                    label="COMPLETION STATUS"
                    name="completionStatus"
                    options={completionStatusOptions}
                  />
                </div>
                <div className="space-y-4">
                  <FormInput label="DEPARTMENT" name="department" type="text" />
                  <FormInput label="PASSED YEAR" name="passedYear" type="number" />
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-4 lg:mt-0">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
                >
                  {isSubmitting ? 'Saving...' : 'Save'}
                </button>
                <button
                  type="button"
                  onClick={() => setEditingId(null)}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                >
                  Cancel
                </button>
              </div>
            </Form>
          )}
        </Formik>
      );
    }

    return (
      <div key={education.id} className="bg-white border-b pb-4 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Icon icon="material-symbols:school" className="w-6 h-6" />
            Education
          </h3>
          <button
            type="button"
            onClick={() => setEditingId(education.id)}
            className="p-2 hover:bg-gray-100 rounded-full"
          >
            <Icon icon="material-symbols:edit-outline" className="w-5 h-5" />
          </button>
        </div>
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <p className="text-base font-semibold text-gray-600">TITLE</p>
            <p className="mb-3">{education.title}</p>
            <p className="text-base font-semibold text-gray-600">INSTITUTE</p>
            <p className="mb-3">{education.institute}</p>
            <p className="text-base font-semibold text-gray-600">COMPLETION STATUS</p>
            <p>{education.completionStatus}</p>
          </div>
          <div>
            <p className="text-base font-semibold text-gray-600">DEPARTMENT</p>
            <p className="mb-3">{education.department}</p>
            <p className="text-base font-semibold text-gray-600">PASSED YEAR</p>
            <p>{education.passedYear}</p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6 border rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Education</h3>
      </div>

      {/* Display existing educations */}
      {educations.map(education => renderEducationItem(education))}

      {/* Add New Education Form */}
      {isAddingNew && (
        <Formik
          initialValues={newEducationInitialValues}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting }) => (
            <Form className="bg-white mb-6 lg:p-4">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <FormInput label="TITLE" name="title" type="text" />
                  <FormInput label="INSTITUTE" name="institute" type="text" />
                  <FormSelect
                    label="COMPLETION STATUS"
                    name="completionStatus"
                    options={completionStatusOptions}
                  />
                </div>
                <div className="space-y-4">
                  <FormInput label="DEPARTMENT" name="department" type="text" />
                  <FormInput label="PASSED YEAR" name="passedYear" type="number" />
                </div>
              </div>
              <div className="flex gap-2 mt-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600"
                >
                  {isSubmitting ? 'Saving...' : 'Save'}
                </button>
                <button
                  type="button"
                  onClick={() => setIsAddingNew(false)}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
                >
                  Cancel
                </button>
              </div>
            </Form>
          )}
        </Formik>
      )}

      {/* Add New Education Button */}
      {!isAddingNew && (
        <button 
          type="button" 
          onClick={() => setIsAddingNew(true)}
          className="flex items-center gap-2 text-blue-500 hover:text-blue-600 px-5 py-2 border border-blue-500 hover:border-blue-600 rounded-lg"
        >
          <Icon icon="material-symbols:add-circle-outline" className="w-5 h-5" />
          Add Education
        </button>
      )}
    </div>
  );
};

export default Education;
