import { useField } from 'formik';

const FormRadio = ({ label, options, isHorizontal, ...props }) => {
  const [field, meta, helpers] = useField(props);
  const { setValue } = helpers;

  return (
    <div className="">
      <label className="block text-sm sm:text-base font-[500] mb-2">
        {label}
      </label>
      <div className={`${isHorizontal ? 'flex space-x-4' : 'space-y-2'}`}>
        {options.map((option) => (
          <div key={String(option.value)} className="flex items-center">
            <input
              type="radio"
              {...field}
              value={String(option.value)}
              checked={field.value === option.value}
              onChange={() => setValue(option.value)}
              className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 cursor-pointer"
            />
            <label className="ml-2 text-sm text-gray-700">
              {option.label}
            </label>
          </div>
        ))}
      </div>
      {meta.touched && meta.error ? (
        <div className="text-red-500 text-xs mt-1">{meta.error}</div>
      ) : null}
    </div>
  );
};

export default FormRadio;
