'use client';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import FormInput from '@/components/form/FormInput';
import FormSelect from '@/components/form/FormSelect';
import FormRadio from '@/components/form/FormRadio';
import FormDatePicker from '@/components/form/FormDatePicker';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import RegularGoBack from '@/components/shared/RegularGoBack';
import NumberInput from '@/components/form/NumberInput';
import DateTimePicker from '@/components/form/DateTimePicker';
import MultiSelectComponent from '@/components/form/MultiSelectComponent';
import { useQueryClient } from '@tanstack/react-query';

const validationSchema = Yup.object().shape({
  name: Yup.string().required('Promotion name is required'),
  discountType: Yup.string().required('Discount type is required'),
  discountValue: Yup.number().required('Discount value is required'),
  applicableType: Yup.string().required('Please select applicable type'),
  applicableCategoryIds: Yup.array()
    .min(1, 'Please select at least one category')
    .required('Please select applicable category'),
  promotionCode: Yup.string().required('Promotion code is required'),
  startDate: Yup.date().required('Start date is required'),
  endDate: Yup.date().required('End date is required'),
  isActive: Yup.boolean().required('Active status is required'),
  usageLimit: Yup.number().min(1, 'Limit should not be less than 1'),
  minimumPurchaseAmount: Yup.number(),
  maximumDiscountAmount: Yup.number(),
  promotionId: Yup.string().matches(
    /^[0-9a-f]{8}-[0-9a-f]{4}-[4][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
    'Must be a valid UUID'
  ),
});

const discountTypeOptions = [
  { value: 'fixed_amount', label: 'Fixed amount' },
  { value: 'percentage', label: 'Percentage amount' },
];

const applicableTypeOptions = [
  { value: 'shop_item', label: 'Shop Items' },
  { value: 'plan', label: 'Plans' },
];

const activeStatusOptions = [
  { label: 'Yes', value: true },
  { label: 'No', value: false },
];

const AddPromotion = () => {
  const router = useRouter();
  const queryClient = useQueryClient();

  const { data: categories } = useDataFetch({
    queryKey: 'shop-categories',
    endPoint: '/admin/shop/categories',
  });

  const { data: plans } = useDataFetch({
    queryKey: 'plans',
    endPoint: '/plans',
  });


  const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
    try {
      const response = await api.post('/promotions', {...values, promotionType: values?.discountType});
      router.push('/dashboard/promotions');
      queryClient.invalidateQueries('/promotions/admin');
    } catch (error) {
      console.log(error);
      if (error?.response?.data?.validationErrors) {
        const validationErrors = error.response.data.validationErrors;
        Object.keys(validationErrors).forEach((field) => {
          setFieldError(`${field}`, validationErrors[field][0]);
        });
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="">
      <RegularGoBack className={'pb-5 max-w-32'} title={'Promotions'} />

      <Formik
        initialValues={{
          name: '',
          description: '',
          discountType: '',
          discountValue: '',
          applicableType: 'shop_item',
          applicableCategoryIds: [],
          applicablePlanIds: [],
          promotionCode: '',
          startDate: '',
          endDate: '',
          isActive: true,
          usageLimit: '',
          minimumPurchaseAmount: '',
          maximumDiscountAmount: '',
        }}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, values, setFieldValue, errors }) => (
          <Form className="space-y-6 bg-gray-50 border p-5 rounded-xl">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <FormInput
                label="Promotion Name"
                name="name"
                placeholder="Write here"
                required
              />

              <FormSelect
                label="Discount Type"
                name="discountType"
                options={discountTypeOptions}
                // multiple={false}
                placeholder="Select discount type"
                required
              />

              <NumberInput
                label="Discount Value"
                name="discountValue"
                placeholder="Write here"
                required
              />

              <MultiSelectComponent
                label="Discount Category"
                name="applicableCategoryIds"
                options={categories?.items || []}
                valueKey="id"
                labelKey="name"
                placeholder="Select discount category (Multi selection)"
                required
              />

              <MultiSelectComponent
                label="Applied to Plan"
                name="applicablePlanIds"
                options={plans?.items || []
                }
                valueKey="id"
                labelKey="name"
                placeholder="Select plan"
              />

              <FormInput
                label="Promotion Code"
                name="promotionCode"
                placeholder="Write here"
                required
              />

              <DateTimePicker label="Start Date" name="startDate" required />

              <DateTimePicker label="End Date" name="endDate" required />

              <div className="flex items-center">
                <FormRadio
                  label="Active Status"
                  name="isActive"
                  options={activeStatusOptions}
                  isHorizontal={true}
                  required
                />
              </div>

              <FormInput
                label="Usage Limit"
                name="usageLimit"
                type="number"
                placeholder="Write here"
                required
              />

              <NumberInput
                label="Minimum Purchase Amount"
                name="minimumPurchaseAmount"
                type="number"
                placeholder="Write here"
              />

              <NumberInput
                label="Maximum Purchase Amount"
                name="maximumDiscountAmount"
                type="number"
                placeholder="Write here"
              />
            </div>

            <FormInput
              label="Descriptions"
              name="description"
              isTextarea={true}
              placeholder="Write promotion descriptions"
              required
            />

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-6 py-2 bg-yellow-400 rounded-lg hover:bg-yellow-500 transition-colors"
              >
                {isSubmitting ? 'Adding...' : 'Add Promotion'}
              </button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default AddPromotion;
