import { ButtonIcon } from '@/components/Button';
import GoBack from '@/components/shared/GoBack';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

const Waterfall = () => {
  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0">
      <GoBack title={'HEC Play'} linkClass="my-5 mb-8 w-full max-w-40" />

      <div className="p-4 xl:px-6 mb-3 bg-[#FFF6EF] flex items-center justify-between rounded-lg shadow-lg text-center gap-5">
        <Image
          src={'/assets/images/all-img/catImg.png'}
          alt={'waterfall'}
          width={300}
          height={500}
          className="max-w-[250px] h-auto"
        />

        <div className="space-y-3">
          <p className="text-xl text-yellow-600">Take me to</p>

          <h1 className="text-5xl font-semibold bg-gradient-to-b from-yellow-500 to-yellow-700 text-transparent bg-clip-text">
            Waterfall
          </h1>
          <p className="text-md text-yellow-600">
            A little description about game. A little description about game
          </p>
        </div>

        <Link
          href={'/waterfall/play'}
          className="flex items-center gap-2 border border-yellow-800 text-3xl text-yellow-900 bg-gradient-to-b from-[#DCA600] to-[#FFDE5B] px-10 py-1 rounded-full mt-3"
        >
          Let's GO{' '}
          <ButtonIcon
            icon={'famicons:play'}
            innerBtnCls={'h-14 w-14'}
            btnIconCls={'h-6 w-6 text-white'}

          />
        </Link>
      </div>
    </div>
  );
};

export default Waterfall;
