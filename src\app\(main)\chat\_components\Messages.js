import ContactInfo from '@/components/ChatBox/ContactInfo';
import useDataFetch from '@/hooks/useDataFetch';
import { getSocket } from '@/lib/socket';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';

const Messages = ({ activeChat, setShowChatList, showChatList }) => {
    const [newMessage, setNewMessage] = useState('');
    const [messages, setMessages] = useState([]);
    const [showContactInfo, setShowContactInfo] = useState(false);
    const socket = getSocket(); // Get the socket instance

    const [isSocketConnected, setIsSocketConnected] = useState(false); // Track connection status

    // Get current user from Redux store
    const currentUser = useSelector((state) => state.auth.user);

    const { data, isLoading } = useDataFetch({
      queryKey: ['chat-history', activeChat?.conversationId],
      endPoint: `/chat/conversations/${activeChat?.conversationId}/messages`,
      enabled: !!activeChat?.conversationId,
    });

    useEffect(() => {
      if (data?.items && data.items.length > 0) {
        setMessages(data.items);
      }
    }, [data]);

    useEffect(() => {
      if (!activeChat?.conversationId) return;

      // Subscribe to the conversation
      socket.emit('subscribe_conversation', {
        conversationId: activeChat.conversationId,
      });

      socket.emit('send_message', {
        recipientId: activeChat?.id,
        conversationId: activeChat?.conversationId,
        type: 'text',
        content: 'Hello!',
      });

      // Listen for connection status
      socket.on('connect', () => {
        console.log('Socket connected');
        setIsSocketConnected(true); // Update connection status
      });

      socket.on('disconnect', () => {
        console.log('Socket disconnected');
        setIsSocketConnected(false); // Update connection status
      });

      socket.on('error', (error) => {
        console.error('Socket error:', error);
      });

      // Listen for incoming messages
      socket.on('new_message', (data) => {
        console.log('New message received:', data);

        if (data.conversationId === activeChat.conversationId) {
          setMessages((prevMessages) => [...prevMessages, data]);
        }
      });

      // Listen for subscription confirmation
      socket.on('subscribed_conversation', (data) => {
        console.log('Subscribed to conversation:', data);
      });

      // Cleanup socket listeners on unmount
      return () => {
        socket.off('new_message');
        socket.off('subscribed_conversation');
        socket.off('connect');
        socket.off('disconnect');
        socket.off('error');
      };
    }, [activeChat?.conversationId, socket]);

    const handleSendMessage = async (e) => {
      e.preventDefault();
      if (newMessage.trim() && isSocketConnected) {
        // Only send if socket is connected
        socket.emit('send_message', {
          recipientId: activeChat?.id,
          conversationId: activeChat?.conversationId,
          type: 'text',
          content: newMessage,
        });

        setMessages((prevMessages) => [
          ...prevMessages,
          {
            id: prevMessages.length + 1,
            content: newMessage,
            senderId: currentUser?.id,
            senderName: currentUser?.name,
            senderProfilePicture: currentUser?.profilePicture,
            type: 'text',
            createdAt: new Date().toISOString(),
          },
        ]);

        setNewMessage('');
      } else if (!isSocketConnected) {
        console.log('Socket is not connected');
      }
    };

    const toggleContactInfo = () => {
      setShowContactInfo(!showContactInfo);
    };

    const toggleChatList = () => {
      setShowChatList(!showChatList);
    };

  return (
    <>
      <div
        className={`${
          showChatList ? 'hidden' : 'block'
        } md:block md:flex-1 flex flex-col bg-white`}
      >
        {/* Chat Header */}
        <header className="bg-yellow-100 p-4 py-3.5 flex justify-between items-center border-b">
          <div className="flex items-center gap-3">
            <button
              className="md:hidden p-2 rounded-full hover:bg-yellow-200 mr-1"
              onClick={toggleChatList}
            >
              <Icon icon="material-symbols:arrow-back" width="20" height="20" />
            </button>
            <div className="relative">
              <Image
                src={activeChat?.avatar || '/assets/images/all-img/avatar.png'}
                alt={activeChat?.name || 'Contact'}
                width={50}
                height={50}
                className="rounded-full"
              />
              {activeChat?.online && (
                <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></span>
              )}
            </div>
            <div>
              <h3 className="font-semibold">{activeChat?.name || 'Contact'}</h3>
              <p className="text-sm flex items-center gap-1 text-gray-600">
                {activeChat?.online ? (
                  <>
                    <span className="w-2 h-2 bg-green-500 rounded-full"></span>{' '}
                    Online
                  </>
                ) : (
                  'Offline'
                )}
              </p>
            </div>
          </div>

          <div className="flex gap-2">
            <button
              onClick={toggleContactInfo}
              className={`p-2 rounded-full ${
                showContactInfo ? 'bg-yellow-200' : 'hover:bg-yellow-200'
              }`}
            >
              <Icon
                icon="material-symbols:info-outline"
                width="20"
                height="20"
              />
            </button>
          </div>
        </header>

        {/* Chat Messages */}
        <div
          style={{ height: 'calc(100vh - 30%)' }}
          className="flex-1 overflow-y-auto p-4 py-2 bg-gray-50"
        >
          <div className="text-center text-xs text-gray-500 mb-4">Today</div>

          {isLoading ? (
            <div className="flex justify-center items-center h-full">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-500"></div>
            </div>
          ) : messages?.length === 0 ? (
            <div className="text-center text-gray-500">No messages yet</div>
          ) : (
            messages.map((message) => (
              <div
                key={message.id}
                className={`mb-4 flex ${
                  message.senderId === currentUser?.id
                    ? 'justify-end'
                    : 'justify-start'
                }`}
              >
                {message.senderId !== currentUser?.id && (
                  <div className="w-8 h-8 rounded-full overflow-hidden mr-2 flex-shrink-0">
                    <Image
                      src={
                        message.senderProfilePicture ||
                        '/assets/images/all-img/avatar.png'
                      }
                      alt={message.senderName || 'Contact'}
                      width={32}
                      height={32}
                      className="object-cover"
                    />
                  </div>
                )}

                <div
                  className={`max-w-[70%] ${
                    message.senderId === currentUser?.id ? 'order-1' : 'order-2'
                  }`}
                >
                  {message.content && message.type === 'text' && (
                    <div
                      className={`p-3 rounded-lg ${
                        message.senderId === currentUser?.id
                          ? 'bg-gray-700 text-white rounded-br-none'
                          : 'bg-yellow-100 text-gray-800 rounded-bl-none'
                      }`}
                    >
                      {message.content}
                    </div>
                  )}

                  {message.type === 'image' && (
                    <div className="rounded-lg overflow-hidden">
                      <Image
                        src={message.content}
                        alt="Shared image"
                        width={200}
                        height={200}
                        className="object-cover"
                      />
                    </div>
                  )}

                  <div
                    className={`text-xs text-gray-500 mt-1 flex items-center gap-1 ${
                      message.senderId === currentUser?.id
                        ? 'justify-end'
                        : 'justify-start'
                    }`}
                  >
                    {message.senderId !== currentUser?.id &&
                      message.senderName && (
                        <span className="font-medium text-xs text-gray-600">
                          {message.senderName}
                        </span>
                      )}
                    <span>
                      {new Date(message.createdAt).toLocaleTimeString('en-US', {
                        hour: 'numeric',
                        minute: 'numeric',
                        hour12: true,
                      })}
                    </span>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Message Input */}
        <form
          onSubmit={handleSendMessage}
          className="p-3 border-t flex items-center gap-2 bg-white relative"
        >
          <div className="flex-1 relative w-[90%]">
            <input
              placeholder="Type a message..."
              className="w-full p-1.5 px-2 pr-24 border border-gray-300 overflow-y-auto scrollbar-hide rounded-md bg-gray-50 focus:outline-none focus:border-yellow-400 no-focus-outline resize-none"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              rows="1"
            />
            <div className="flex items-center gap-1 absolute right-2 top-1/2 -translate-y-1/2">
              <button
                type="button"
                className="p-1.5 bg-white rounded text-gray-500 hover:text-blue-500"
              >
                <Icon
                  icon="fluent:folder-add-28-filled"
                  width="20"
                  height="20"
                />
              </button>
              <button
                type="button"
                className="p-1.5 bg-white rounded text-gray-500 hover:text-blue-500"
              >
                <Icon icon="bxs:file" width="20" height="20" />
              </button>
              <button
                type="button"
                className="p-1.5 bg-white rounded text-gray-500 hover:text-blue-500"
              >
                <Icon icon="icon-park-solid:voice" width="20" height="20" />
              </button>
            </div>
          </div>
          <button
            type="submit"
            className=" p-2 bg-gray-100 rounded-md text-gray-600 hover:bg-yellow-400 hover:text-black transition-colors"
          >
            <Icon icon="mynaui:send-solid" width="24" height="24" />
          </button>
        </form>
      </div>

      {/* Contact Info Sidebar */}
      {showContactInfo && (
        <div className="w-full md:w-1/3 lg:w-1/4 border-l bg-white">
          <div className="flex justify-between items-center p-4 py-5 border-b bg-yellow-50">
            <h3 className="font-semibold">Contact Info</h3>
            <button
              onClick={toggleContactInfo}
              className="p-2 rounded-full hover:bg-yellow-200"
            >
              <Icon icon="material-symbols:close" width="20" height="20" />
            </button>
          </div>
          <ContactInfo
            contact={{
              name: activeChat?.name || 'Contact',
              email: '<EMAIL>',
              profileImage:
                activeChat?.avatar || '/assets/images/all-img/avatar.png',
              files: [
                { name: 'file-name.jpeg', date: '2/13/2023', size: '3.5 MB' },
                { name: 'file-name.jpeg', date: '2/13/2023', size: '3.5 MB' },
                { name: 'file-name.jpeg', date: '2/13/2023', size: '3.5 MB' },
                { name: 'file-name.jpeg', date: '2/13/2023', size: '3.5 MB' },
                { name: 'file-name.jpeg', date: '2/13/2023', size: '3.5 MB' },
              ],
            }}
          />
        </div>
      )}
    </>
  );
};

export default Messages;
