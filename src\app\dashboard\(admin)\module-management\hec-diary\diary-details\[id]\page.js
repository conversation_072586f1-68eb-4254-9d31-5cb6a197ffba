'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import useDataFetch from '@/hooks/useDataFetch';
import DiaryCanvas from '@/app/dashboard/(tutor)/submission-management/hec-diary/review/_components/DiaryCanvas';
import DiaryFeedbackModal from '../../teacher-feedback/page'; // Adjust the import path as needed
import Image from 'next/image';

const DiaryDetails = ({ params }) => {
  const router = useRouter();
  const [diary, setDiary] = useState(null);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  
  // Get diary ID from route params
  const diaryId = params?.id;
  
  useEffect(() => {
    console.log("Diary ID from params:", diaryId);
  }, [diaryId]);

  // Fetch diary details using the useDataFetch hook
  const { 
    data: diaryData, 
    isLoading: isDiaryLoading, 
    error: diaryError 
  } = useDataFetch({
    queryKey: [`/admin/diary/entries/${diaryId}`],
    endPoint: `/admin/diary/entries/${diaryId}`,
    enabled: !!diaryId
  });

  // Process diary data when it's fetched
  useEffect(() => {
    if (diaryData && diaryData.id) {
      console.log("Fetched diaryData:", diaryData);
      setDiary(diaryData);
    }
  }, [diaryData]);

  // Handle back button click
  const handleBackClick = () => {
    router.back();
  };

  // Loading state
  if (isDiaryLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-16 h-16 border-4 border-amber-500 border-t-transparent rounded-full animate-spin"></div>
          <p className="text-gray-600">Loading diary details...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (diaryError || !diary) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
          <h2 className="text-red-600 text-lg font-semibold mb-2">Error Loading Diary</h2>
          <p className="text-gray-700 mb-4">
            We couldn't load the diary details. Please try again or contact support.
          </p>
          <button
            onClick={handleBackClick}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Back to List
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white min-h-screen w-full">
      <div className="max-w-7xl mx-auto pt-16 right-0 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <h1 className="font-normal text-xl">View Diary</h1>
         
        </div>

         {/* User info and date section */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div className="flex flex-col items-start space-y-1 mb-4 mt-3 sm:mb-0">
            <span className="text-sm font-normal">Submitted by:</span>
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 rounded-full bg-amber-100 flex items-center justify-center text-amber-800 overflow-hidden mt-2">
                {/* User Avatar Circle */}
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-xs text-gray-700 mt-2">{diary?.diary?.user?.name || 'Unknown User'}</span>
            </div>
          </div>
          
          <div className="flex flex-col items-start sm:items-center text-xs text-gray-700 space-y-1">
            <span className="text-sm font-normal">Date:</span>
            <div className="flex items-center space-x-2">
              <div className="text-yellow-500 bg-yellow-50 rounded-full p-1 flex items-center justify-center">
                {/* Calendar icon */}
                <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                </svg>
              </div>
              <span>{diary?.createdAt ? new Date(diary?.diary?.user?.createdAt).toLocaleDateString('en-US', {
                      day: 'numeric',
                      month: 'short',
                      year: '2-digit'
                    }) : 'Unknown'}</span>
            </div>
          </div>
        </div>
        
        {/* Diary content display */}
        <div className="flex flex-col lg:flex-row bg-[#FDE7E9] rounded-md mt-9">
          {/* Canvas Panel */}
          <div className="w-full lg:w-1/2 p-1 relative">
            <div className="w-full h-[500px] flex items-center justify-center overflow-hidden">
              <div className="bg-white h-full flex items-center justify-center p-2 overflow-hidden shadow-xl">
                <div
                  className="canvas-container-wrapper"
                  style={{ width: '100%', height: '100%', padding: '20px' }}
                >
                  {diary && (
                    <DiaryCanvas data={diary} />
                  )}            
                </div>
              </div>
            </div>
          </div>
          
          {/* Diary Information Panel */}
          <div className="w-full lg:w-1/2 p-1 relative">
            <div className='bg-white h-full flex items-center justify-center p-2 overflow-hidden shadow-xl'>
              <div className="bg-white border border-white shadow-lg rounded-lg p-4 flex-1">
                <div className="flex justify-between mb-2">
                  <span className="font-semibold text-sm">{diary?.title || 'Diary Entry'}</span>
                  <span className="text-xs">
                    Date: {diary?.createdAt ? new Date(diary.createdAt).toLocaleDateString('en-US', {
                      day: 'numeric',
                      month: 'short',
                      year: '2-digit'
                    }) : 'Unknown'}
                  </span>
                </div>
                <div className="h-40 overflow-auto bg-white text-xs text-gray-700 p-2 border border-gray-200 rounded">
                  <p>{diary?.correction?.correctionText || 'No content available'}</p>
                </div>
              </div>
            </div>

            <button
                            onClick={() => setShowFeedbackModal(true)}
                            className=" absolute bottom-4 right-1 flex items-center hover:opacity-80 transition-opacity "
                          >
                            <Image
                              src="/assets/images/all-img/feedback-bg.png"
                              alt="Teacher Feedback"
                              width={150}
                              height={50}
                              className="h-10 w-auto"
                              priority
                            />
                          </button>
          </div>
        </div>
      </div>
      
      {/* Feedback Modal */}
      {showFeedbackModal && (
        <DiaryFeedbackModal 
          diary={diary} 
          onClose={() => setShowFeedbackModal(false)} 
        />
      )}
    </div>
  );
};

export default DiaryDetails;