'use client';
import React from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import FormInput from '@/components/form/FormInput';
import api from '@/lib/api';

const CreateQuestions = () => {
  const queryClient = useQueryClient();

  // Define validation schema
  const validationSchema = Yup.object().shape({
    question: Yup.string().required('Question is required'),
    points: Yup.number().required('Points is required').positive('Points must be positive'),
    minimumWords: Yup.number().required('Minimum Words is required').positive('Minimum words must be positive'),
    isActive: Yup.boolean().default(true)
  });

  // Define initial values
  const initialValues = {
    question: '',
    points: '',
    minimumWords: '',
    isActive: true
  };

  // Create mutation for API call
  const createQuestionMutation = useMutation({
    mutationFn: (questionData) => {
      return api.post('/admin/qa/create', questionData);
    },
    onSuccess: () => {
      // Invalidate and refetch questions list if needed
      queryClient.invalidateQueries({ queryKey: ['questions'] });
      toast.success('Question created successfully');
    },
    onError: (error) => {
      console.error('Error creating question:', error);
      toast.error('Failed to create question');
    }
  });

  // Handle form submission
  const handleSubmit = (values, { setSubmitting, resetForm }) => {
    // Convert string values to appropriate types
    const payload = {
      question: values.question,
      points: parseInt(values.points, 10),
      minimumWords: parseInt(values.minimumWords, 10),
      isActive: values.isActive
    };

    createQuestionMutation.mutate(payload, {
      onSettled: () => {
        setSubmitting(false);
        resetForm();
      }
    });
  };

  return (
    <div className='min-h-screen bg-white p-5'>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, errors, touched, resetForm }) => (
          <Form className="mx-auto bg-gray-100 p-6 rounded-lg shadow-md">
            <div className="flex flex-wrap gap-4 mb-4">
              <div className="flex-1 min-w-[200px]">
                <label htmlFor="question" className="block font-bold">
                  Question <span className="text-red-500">*</span>
                </label>
                <FormInput
                  type="text"
                  name="question"
                  id="question"
                  placeholder="Write question here"
                  className={`w-full mt-1 p-2 border ${
                    errors.question && touched.question ? 'border-red-500' : 'border-gray-300'
                  } rounded`}
                />
               
              </div>

              <div className="flex-1 min-w-[200px]">
                <label htmlFor="points" className="block font-bold">
                  Points <span className="text-red-500">*</span>
                </label>
                <FormInput
                  type="number"
                  name="points"
                  id="points"
                  placeholder="Enter points"
                  className={`w-full mt-1 p-2 border ${
                    errors.points && touched.points ? 'border-red-500' : 'border-gray-300'
                  } rounded`}
                />
                
              </div>

              <div className="flex-1 min-w-[200px]">
                <label htmlFor="minimumWords" className="block font-bold">
                  Minimum Words <span className="text-red-500">*</span>
                </label>
                <FormInput
                  type="number"
                  name="minimumWords"
                  id="minimumWords"
                  placeholder="Enter minimum words required"
                  className={`w-full mt-1 p-2 border ${
                    errors.minimumWords && touched.minimumWords ? 'border-red-500' : 'border-gray-300'
                  } rounded`}
                />
                
              </div>
            </div>

            <div className="flex justify-end gap-4 mt-7">
              <button
                type="button"
                className="bg-gray-300 hover:bg-gray-400 text-black font-medium py-2 px-4 rounded"
                onClick={() => resetForm()}
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="bg-[#FFDE34] hover:bg-yellow-400 text-black font-medium py-2 px-4 rounded"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Creating...' : 'Create'}
              </button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default CreateQuestions;