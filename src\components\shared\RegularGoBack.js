'use client';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import React from 'react';

const RegularGoBack = ({className, title}) => {
  const router = useRouter();
  return (
    <div
      onClick={() => router.back()}
      className={`flex justify-start items-center gap-2 group cursor-pointer ${className}`}
    >
      <Icon
        icon="eva:arrow-back-fill"
        className="w-5 h-5 group-hover:text-yellow-500"
      />

      <h3 className="text-lg font-[500] text-gray-700 group-hover:text-yellow-500">
        {title ? title : "Go Back"}
      </h3>
    </div>
  );
};

export default RegularGoBack;
