'use client';

import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { usePathname, useRouter } from 'next/navigation';
import Image from 'next/image';
import { Icon } from '@iconify/react';
import { setGoBackStep } from '@/store/features/authSlice';

export default function AuthLayout({ children }) {
  const dispatch = useDispatch();
  const router = useRouter();
  const pathName = usePathname();
  const goBackStep = useSelector((state) => state.auth.goBackStep);

  const isResetPassPage = pathName.startsWith('/password-reset/');

  const handleGoBack = () => {
    const paths = goBackStep.split('/');
    paths.pop();
    if (paths.length > 0) {
      dispatch(setGoBackStep(paths.join('/')));
    } else {
      router.back();
    }
  };

  return (
    <div className="relative bg-gradient-to-r from-[#D8D2FF] to-[#FFFAC2] p-5 md:p-10 xl:p-16 relative min-h-screen flex items-center">
      <div className="xl:max-h-[87vh] xl:max-w-[95vw] xl:min-w-[92vw] relative mx-auto">
        <div className="absolute -top-5 rounded-3xl w-full h-full bg-[#FFF189] z-0"></div>
        <div className="flex flex-col xl:flex-row rounded-3xl w-[98%] xl:max-w-[99%] mx-auto z-10">
          <div className="relative w-full max-lg:flex-1 xl:w-3/5 h-[87vh] hidden xl:block overflow-hidden bg-white p-10 xl:pr-0 rounded-t-3xl xl:rounded-l-3xl xl:rounded-r-none">
            <Image
              src="/assets/images/auth/education.png"
              alt="Auth background"
              height={900}
              width={900}
              className="object-cover object-center w-full h-full rounded-3xl"
              priority
            />

            {!isResetPassPage && (
              <span
                onClick={handleGoBack}
                className="absolute top-16 left-16 cursor-pointer hover:text-yellow-600"
              >
                <Icon
                  icon="eva:arrow-back-fill"
                  width="18"
                  height="18"
                  className="inline mb-1"
                />{' '}
                Go Back
              </span>
            )}
          </div>

          <div className="xl:w-2/5 relative max-lg:flex-1 flex items-center justify-center p-3 bg-white max-sm:rounded-2xl sm:rounded-b-2xl xl:rounded-r-3xl xl:rounded-none">
            <div className="w-full xl:m-2">
              {children}

              {/* {(isRegisterPage || isLoginPage) &&
                (isLoginPage ? (
                  <div className="text-center text-sm text-gray-600">
                    Don't have an account?
                    <Link
                      href="/register"
                      className="text-yellow-500 hover:text-yellow-600 ml-1"
                    >
                      Create Account
                    </Link>
                  </div>
                ) : (
                  <div className="text-sm text-center">
                    Already have an account?{' '}
                    <Link href="/login" className="text-yellow-500">
                      Sign in
                    </Link>
                  </div>
                ))} */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
