'use client';
import { Editor } from '@tinymce/tinymce-react';
import { useEffect, useState } from 'react';

const TinyMceEditor = ({
  editorRef,
  setValue,
  initialValue,
  onAutoSave,
  height,
  maxWords, // Default max words limit
  ...props
}) => {
  const [lastContent, setLastContent] = useState('');
  const [typingTimer, setTypingTimer] = useState(null);
  const [wordCount, setWordCount] = useState(0);
  const [isOverLimit, setIsOverLimit] = useState(false);

  // Count words in HTML content
  const countWords = (html) => {
    // Remove HTML tags
    const text = html.replace(/<[^>]*>/g, ' ');
    // Remove entities
    const cleanText = text.replace(/&nbsp;|&amp;|&lt;|&gt;|&quot;|&#39;/g, ' ');
    // Remove extra spaces and split by whitespace
    const words = cleanText.trim().split(/\s+/).filter(word => word.length > 0);
    return words.length;
  };

  // Handle content change with debounce for auto-save
  const handleEditorChange = (content) => {
    // Count words
    const count = countWords(content);

    // Check if over limit
    const overLimit = count > maxWords;

    // If over limit and we have previous content, restore it
    if (overLimit && editorRef.current && lastContent) {
      // Prevent typing by restoring the previous content
      editorRef.current.setContent(lastContent);
      return; // Exit early to prevent further processing
    }

    // Update word count and limit status
    setWordCount(count);
    setIsOverLimit(overLimit);

    // Update content since it's within limits or we don't have previous content yet
    setValue(content);

    // Store the content as last content if it's within limits
    if (!overLimit) {
      setLastContent(content);
    }

    // Auto-save functionality
    if (onAutoSave) {
      // Clear previous timer
      if (typingTimer) clearTimeout(typingTimer);

      // Set a new timer to trigger auto-save after 500ms of inactivity
      const timer = setTimeout(() => {
        // Only save if content has changed and contains a space (word completion)
        if (content !== lastContent && (content.includes(' ') || content.includes('.'))) {
          onAutoSave({ answer: content });
        }
      }, 500);

      setTypingTimer(timer);
    }
  };

  // Clean up timer on unmount
  useEffect(() => {
    return () => {
      if (typingTimer) clearTimeout(typingTimer);
    };
  }, [typingTimer]);

  // Calculate initial word count when component mounts
  useEffect(() => {
    if (initialValue) {
      const count = countWords(initialValue);
      setWordCount(count);
      setIsOverLimit(count > maxWords);

      // Always set the initial content as lastContent to allow editing
      // even if it's over the limit (for existing submissions)
      setLastContent(initialValue);
    }
  }, [initialValue, maxWords]);

  return (
    <div className="relative">
      <Editor
        {...props}
        apiKey={process.env.NEXT_TINYMCE_TOKEN || "w64sqy7kf7wf2qoy3qqfmyatf85cys00il0jcezjers4pl9o"}
        onInit={(_evt, editor) => (editorRef.current = editor)}
        initialValue={initialValue || '<p></p>'}
        onEditorChange={handleEditorChange}
        init={{
          height: height || 500,
          menubar: false,
          plugins: [
          ],
          toolbar:
            'undo redo | blocks | bold italic forecolor | ' +
            'alignleft aligncenter alignright alignjustify | ' +
            'bullist numlist outdent indent | removeformat',
          content_style:
            'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
          // Add setup callback to initialize word count
          setup: (editor) => {
            editor.on('init', () => {
              const count = countWords(editor.getContent());
              setWordCount(count);
              setIsOverLimit(count > maxWords);
            });
          }
        }}
      />

      {/* Word count display */}
      <div className="absolute left-2 w-[98.5%] z-20 flex justify-between items-center mt-2 text-sm">
        <div className={`font-medium ${isOverLimit ? 'text-red-600 bg-red-50 px-2 py-1 rounded-md border border-red-200' : 'text-gray-600'}`}>
          {maxWords && <span className={isOverLimit ? 'font-bold' : ''}>
            {wordCount} / {maxWords} words
          </span>}
          {isOverLimit && (
            <span className="ml-2 text-red-600 font-bold">
              (Maximum limit reached! Cannot add more words. Please edit or remove content.)
            </span>
          )}
        </div>
      </div>

      <div className="h-6 rounded-b-lg w-[98.5%] bg-white absolute left-1 bottom-1 z-10"></div>
    </div>
  );
};

export default TinyMceEditor;
