'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import api from '@/lib/api';

const EditQuestionModal = ({ isOpen, onClose, questionId, onQuestionUpdated }) => {
  const [formData, setFormData] = useState({
    question: '',
    points: 0,
    minimumWords: 0
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [touched, setTouched] = useState({});

  // Fetch question data when modal opens
  useEffect(() => {
    if (isOpen && questionId) {
      fetchQuestionData();
    }
  }, [isOpen, questionId]);

  const fetchQuestionData = async () => {
    try {
      setIsLoading(true);
      const response = await api.get(`/admin/qa/${questionId}`);
      
      // Extract question data depending on the API response structure
      const questionData = response.data.data || response.data;
      
      setFormData({
        question: questionData.question || '',
        points: questionData.points || 0,
        minimumWords: questionData.minimumWords || 0
      });
    } catch (error) {
      console.error('Error fetching question data:', error);
      toast.error('Failed to load question data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'points' || name === 'minimumWords' ? parseInt(value, 10) || 0 : value
    }));
    setTouched(prev => ({ ...prev, [name]: true }));
  };

  const validate = () => {
    const newErrors = {};
    if (!formData.question.trim()) {
      newErrors.question = 'Question is required';
    }
    if (formData.points <= 0) {
      newErrors.points = 'Points must be greater than 0';
    }
    if (formData.minimumWords < 0) {
      newErrors.minimumWords = 'Minimum words cannot be negative';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Mark all fields as touched for validation
    setTouched({
      question: true,
      points: true,
      minimumWords: true
    });
    
    if (!validate()) {
      return;
    }
    
    try {
      setIsLoading(true);
      await api.put(`/admin/qa/questions/${questionId}`, formData);
      onQuestionUpdated();
      onClose();
    } catch (error) {
      console.error('Error updating question:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      question: '',
      points: 0,
      minimumWords: 0
    });
    setErrors({});
    setTouched({});
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl p-6 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">Edit Question</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="mx-auto bg-gray-100 p-6 rounded-lg shadow-md">
            <div className="flex flex-wrap gap-4 mb-4">
              <div className="flex-1 min-w-[200px]">
                <label htmlFor="question" className="block font-bold">
                  Question <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="question"
                  id="question"
                  value={formData.question}
                  onChange={handleChange}
                  placeholder="Write question here"
                  className={`w-full mt-1 p-2 border ${
                    errors.question && touched.question ? 'border-red-500' : 'border-gray-300'
                  } rounded`}
                />
                {errors.question && touched.question && (
                  <div className="text-red-500 text-sm">{errors.question}</div>
                )}
              </div>
              
              <div className="flex-1 min-w-[200px]">
                <label htmlFor="points" className="block font-bold">
                  Points <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  name="points"
                  id="points"
                  value={formData.points}
                  onChange={handleChange}
                  placeholder="Enter points"
                  className={`w-full mt-1 p-2 border ${
                    errors.points && touched.points ? 'border-red-500' : 'border-gray-300'
                  } rounded`}
                />
                {errors.points && touched.points && (
                  <div className="text-red-500 text-sm">{errors.points}</div>
                )}
              </div>
              
              <div className="flex-1 min-w-[200px]">
                <label htmlFor="minimumWords" className="block font-bold">
                  Minimum Words <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  name="minimumWords"
                  id="minimumWords"
                  value={formData.minimumWords}
                  onChange={handleChange}
                  placeholder="Enter minimum words required"
                  className={`w-full mt-1 p-2 border ${
                    errors.minimumWords && touched.minimumWords ? 'border-red-500' : 'border-gray-300'
                  } rounded`}
                />
                {errors.minimumWords && touched.minimumWords && (
                  <div className="text-red-500 text-sm">{errors.minimumWords}</div>
                )}
              </div>
            </div>
            
            <div className="flex justify-end gap-4 mt-7">
              <button
                type="button"
                className="bg-gray-300 hover:bg-gray-400 text-black font-medium py-2 px-4 rounded"
                onClick={resetForm}
                disabled={isLoading}
              >
                Cancel
              </button>
              <button
                type="submit"
                className="bg-[#FFDE34] hover:bg-yellow-400 text-white font-medium py-2 px-4 rounded"
                disabled={isLoading}
              >
                {isLoading ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default EditQuestionModal;