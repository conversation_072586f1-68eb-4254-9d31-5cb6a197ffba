'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import useDataFetch from '@/hooks/useDataFetch';
import DiaryCanvas from '../_components/DiaryCanvas';
import HecDiaryLayout from '../_components/HecDiaryLayout';
import DiaryReviewSection from '../_components/DiaryReviewSection';
import { Icon } from '@iconify/react';
import { formatDate } from '@/utils/dateFormatter';

const DiaryReviewPage = () => {
  const { id } = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState(tabParam || 'todaysDiary');

  useEffect(() => {
    if (tabParam) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  // Get the status from the URL search params if available
  const status = searchParams.get('status');

  // Fetch diary entry data based on status from URL
  const { data, isLoading, error } = useDataFetch({
    queryKey: ['diary-entry-review', id, status],
    endPoint: status === 'reviewed'
      ? `/tutor/diary/entries/${id}`
      : `/tutor/diary/entries/${id}/start-review`,
    method: status === 'reviewed' ? 'GET' : 'POST',
    params: {},
    enabled: !!id,
  });

  const handleBack = () => {
    router.push('/dashboard/submission-management/hec-diary');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-100 text-red-700 rounded-md">
        Error: {error.message}
        <button
          onClick={handleBack}
          className="mt-4 px-4 py-2 bg-gray-200 rounded-md"
        >
          Back to List
        </button>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="p-4 bg-yellow-100 text-yellow-700 rounded-md">
        No diary entry found
        <button
          onClick={handleBack}
          className="mt-4 px-4 py-2 bg-gray-200 rounded-md"
        >
          Back to List
        </button>
      </div>
    );
  }

  return (
    <HecDiaryLayout activeTab={activeTab}>
        <div className="flex justify-between items-center mb-2">
          <div className="flex gap-4 items-center">
            <h6 className="text-sm text-black font-medium ">
              Submitted by:
            </h6>
            <h2 className="text-[#464646] font-normal">
              {data?.diary?.userName}
            </h2>
          </div>
          <div className="flex items-center gap-3 text-sm text-gray-500">
            <div className="font-medium text-sm text-black ">Date:</div>
            <div className="flex items-center gap-3">
              <Icon
                icon="uil:calender"
                width="24"
                height="24"
                className="mx-auto text-gray-900 p-1 rounded-full bg-[#FFF189]"
              />
              {formatDate(data.entryDate, 'ordinal')}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 items-center bg-[#FDE7E9] gap-2 p-1 shadow-xl">
          {/* Diary Canvas */}
          <div className="bg-white h-full flex items-center justify-center p-2 overflow-hidden shadow-xl">
            <div className="w-full h-[500px] flex items-center justify-center overflow-hidden">
              <div
                className="canvas-container-wrapper"
                style={{ width: '100%', height: '100%', padding: '20px' }}
              >
                <DiaryCanvas data={data} />
              </div>
            </div>
          </div>

          {/* Review Section */}
          <DiaryReviewSection data={data} entryId={id} />
        </div>


    </HecDiaryLayout>
  );
};

export default DiaryReviewPage;