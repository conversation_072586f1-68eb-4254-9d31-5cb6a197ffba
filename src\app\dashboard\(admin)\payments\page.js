'use client';
import BasicTablePage from '@/components/form/BasicTablePage';
import Link from 'next/link';
import React, { useState } from 'react';

const Payments = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);

  const paymentsData = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+1234567890',
      status: 'Active',
    },
    {
      id: 2,
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+9876543210',
      status: 'Inactive',
    },
  ];

  const tableData = paymentsData.map((item, index) => {
    return {
      name: item.name,
      email: item.email,
      phone: item.phone,
      status:
        item?.status === 'Active' ? (
          <span className="text-green-600 px-5 rounded bg-green-100 shadow">
            Active
          </span>
        ) : (
          <span className="text-red-600 px-5 rounded bg-red-100 shadow">
            Inactive
          </span>
        ),
    };
  });

  const columns = [
    {
      label: 'User Name',
      field: 'name',
    },
    {
      label: 'Email',
      field: 'email',
    },
    {
      label: 'Phone Number',
      field: 'phone',
    },
    {
      label: 'Status',
      field: 'status',
    },
    {
      label: 'Action',
      field: '',
    },
  ];
  // if (isAdmin) {
  //   columns.push({
  //     label: t("dashboard.action"),
  //     field: "",
  //   });
  // }

  const actions = [
    {
      name: 'view',
      icon: 'material-symbols:visibility',
      className: 'text-blue-600',
      onClick: (val) => {
        router.push(`/dashboard/users/${data?.data[val].id}`);
      },
    },
    {
      name: 'edit',
      icon: 'material-symbols:edit-outline',
      className: 'text-gray-600',
      onClick: (val) => {
        dispatch(setEditData(data?.data[val]));
        dispatch(setShowEditModal(true));
      },
    },
    {
      name: 'delete',
      icon: 'heroicons-outline:trash',
      className: 'text-red-600',
      onClick: (val) => {
        // console.log(data?.data[val]);
        setDeleteData(data?.data[val]);
        dispatch(setShowDeleteModal(true));
      },
    },
  ];

  const changePage = (value) => {
    setCurrentPage(value);
  };

  return (
    <div>
      <BasicTablePage
        title={'All Payments'}
        createButton={'Add User'}
        // showCreateButton={isAdmin ? true : false}
        actions={actions}
        columns={columns}
        loading={isLoading}
        data={tableData}
        // filter={filter}
        changePage={changePage}
        totalItems={paymentsData?.length}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        currentPage={paymentsData?.current_page || 1}
        totalPages={Math.ceil(paymentsData?.total / paymentsData?.per_page) || 1}
      />
    </div>
  );
};

export default Payments;
