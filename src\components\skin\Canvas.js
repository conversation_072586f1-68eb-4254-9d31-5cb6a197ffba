'use client';
import Image from 'next/image';
import { useDispatch, useSelector } from 'react-redux';
import { useRef, forwardRef, useImperativeHandle } from 'react';
import ResizableWrapper from './ResizableWrapper.js';
import {
  selectCanvasItems,
  selectCanvasBackground,
  selectSelectedId,
  selectCanvasWidth,
  selectCanvasHeight,
  setSelectedId,
  clearSelection,

} from '../../store/features/canvasSlice';

const Canvas = forwardRef((props, ref) => {
  const canvasRef = useRef(null);

  // Expose the canvas ref to parent components
  useImperativeHandle(ref, () => ({
    canvasRef,
  }));
  const dispatch = useDispatch();
  const canvasItemGlobal = useSelector(selectCanvasItems);
  const canvasItems = props?.canvasItems || canvasItemGlobal;
  const canvasBackground = useSelector(selectCanvasBackground);
  const selectedId = useSelector(selectSelectedId);
  const canvasWidth = useSelector(selectCanvasWidth);
  const canvasHeight = useSelector(selectCanvasHeight);
  // console.log('Canvas items:', canvasItems);
  const handleSelect = (id) => {
    dispatch(setSelectedId(id === selectedId ? null : id));
  };

  const handleCanvasClick = () => {
    dispatch(clearSelection());
  };
  // console.log(canvasItems, 'tst data');
  return (
    <div
      ref={canvasRef}
      onClick={handleCanvasClick}
      style={{
        position: 'relative',
        width: canvasWidth,
        height: canvasHeight,
        background: canvasBackground,
        overflow: selectedId ? 'visible' : 'hidden', // Only allow overflow when an element is selected
        transition: 'all 0.3s ease',
        boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
        margin: '0 auto',
        border: '1px solid #e5e7eb',
      }}
    >
      {canvasItems.map((item) => (
        <ResizableWrapper
          key={item.id}
          id={item.id}
          styles={item.styles}
          selectedId={selectedId}
          onSelect={handleSelect}
          zIndex={item.zIndex}
          type={item.type}
        >
          {item.type === 'image' ? (
            <div
              style={{
                position: 'relative',
                width: '100%',
                height: '100%',
              }}
            >
              {item.image ? (
                <Image
                  src={item.image}
                  alt="Skin element"
                  fill
                  sizes="(max-width: 200px) 100vw"
                  style={{ objectFit: 'contain' }}
                  onError={(e) => {
                    console.error('Image failed to load:', item.image);
                    e.target.style.display = 'none';
                  }}
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-100 text-gray-400 text-xs">
                  Image not available
                </div>
              )}
            </div>
          ) : (
            <div
              className={`w-full h-full ${
                item.textType === 'body'
                  ? 'flex-col'
                  : 'flex items-center justify-center'
              } ${item.textType === 'body' ? 'custom-scrollbar' : ''}`}
              style={{
                padding: '8px',
                color: item.styles?.color || '#000000',
                fontFamily: item.styles?.fontFamily || 'Roboto',
                fontSize: item.styles?.fontSize || '1rem',
                textAlign: item.styles?.textAlign || 'left',
                display: 'block', // Override flex to ensure text-align works properly
                ...(item.textType === 'body' && {
                  maxHeight: '100%', // Use full height of the container
                  overflowY: 'auto', // Enable vertical scrolling when content exceeds container height
                  minHeight: '200px', // Set minimum height before scrolling starts
                }),
              }}
            >
              {item.content}
            </div>
          )}
        </ResizableWrapper>
      ))}
    </div>
  );
});

// Add display name for forwardRef component
Canvas.displayName = 'Canvas';

export default Canvas;
