import React, { useState } from 'react';
import { toast } from 'sonner';
import { Icon } from '@iconify/react';
import api from '@/lib/api';
import { useSelector } from 'react-redux';
import Image from 'next/image';

const ShareModalContent = ({ entryId }) => {
  // Mock data for recently invited people
  const [invitedPeople, setInvitedPeople] = useState([
    {
      id: 1,
      name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      profilePicture: '/assets/images/all-img/introduction/Frame 1.png',
      selected: false,
    },
    {
      id: 2,
      name: '<PERSON><PERSON>',
      email: '<PERSON><PERSON>t.<PERSON><EMAIL>',
      profilePicture: '/assets/images/all-img/introduction/Frame 1.png',
      selected: false,
    },
    {
      id: 3,
      name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      profilePicture: '/assets/images/all-img/introduction/Frame 1.png',
      selected: false,
    },
    {
      id: 4,
      name: '<PERSON><PERSON>',
      email: '<PERSON><PERSON><PERSON>.<PERSON>@email.com',
      profilePicture: '/assets/images/all-img/introduction/Frame 1.png',
      selected: false,
    },
  ]);

  const [phoneNumber, setPhoneNumber] = useState('');
  const [shareLink, setShareLink] = useState('');
  const [qrCodeUrl, setqrCodeUrl] = useState('');
  const [isGeneratingLink, setIsGeneratingLink] = useState(false);

  // Get the diary entry ID from props or from Redux store if not provided
  const todayEntry = useSelector((state) => state.diary?.todayEntry);
  console.log(todayEntry?.id);
  const diaryEntryId = entryId || todayEntry?.id;
  console.log(entryId);

  // State to track if all users are selected
  const [allSelected, setAllSelected] = useState(false);

  // Handle individual checkbox change - supports multiple selection
  const handleCheckboxChange = (id) => {
    const updatedPeople = invitedPeople.map((person) =>
      person.id === id ? { ...person, selected: !person.selected } : person
    );

    setInvitedPeople(updatedPeople);

    // Update allSelected state based on whether all checkboxes are now selected
    setAllSelected(updatedPeople.every((person) => person.selected));
  };

  // Handle select all checkbox change
  const handleSelectAll = () => {
    const newAllSelected = !allSelected;
    setAllSelected(newAllSelected);

    // Update all people to be selected or deselected
    setInvitedPeople(
      invitedPeople.map((person) => ({
        ...person,
        selected: newAllSelected,
      }))
    );
  };

  // Handle individual share button click
  const handleShare = (id) => {
    // Implement sharing with a single user
    console.log(`Sharing with user ${id}`);
    toast.success(`Shared with user ${id}`);
  };

  // Handle generating a shareable link
  const handleShareSelected = async () => {
    if (!diaryEntryId) {
      toast.error('No diary entry found to share');
      return;
    }

    setIsGeneratingLink(true);

    try {
      // Make API call to generate a shareable link
      const response = await api.post(`/diary/entries/${diaryEntryId}/share`, {
        entryId: diaryEntryId
      });

      if (response.success) {
        // Update the share link with the generated link from the API
        setShareLink(response.data?.shareUrl || response.data?.shareLink || response.data?.url || '');
        // Set the QR code link if available
        setqrCodeUrl(response.data?.qrCodeUrl || '');
        toast.success('Share link generated successfully');
      } else {
        throw new Error(response.message || 'Failed to generate share link');
      }
    } catch (error) {
      console.error('Error generating share link:', error);
      toast.error(error.message || 'Failed to generate share link. Please try again.');
    } finally {
      setIsGeneratingLink(false);
    }
  };

  // State to track if link is copied
  const [isCopied, setIsCopied] = useState(false);

  // Handle copy link button click
  const handleCopyLink = () => {
    navigator.clipboard
      .writeText(shareLink)
      .then(() => {
        setIsCopied(true);
        // toast.success('Link copied to clipboard!');

        // Reset the copied state after 2 seconds
        setTimeout(() => {
          setIsCopied(false);
        }, 2000);
      })
      .catch((error) => {
        console.error('Failed to copy link:', error);
        toast.error('Failed to copy link. Please try again.');
      });
  };

  // Handle QR code download
  const handleDownloadQR = async () => {
    if (!qrCodeUrl) {
      toast.error('No QR code available to download');
      return;
    }

    try {
      // Use the actual QR code URL directly for download
      const response = await fetch(qrCodeUrl);

      if (!response.ok) {
        throw new Error(`Failed to download QR code: ${response.statusText}`);
      }

      // Get the blob from the response
      const blob = await response.blob();

      // Create a URL for the blob
      const blobUrl = URL.createObjectURL(blob);

      // Create a temporary anchor element to trigger the download
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = `diary-qr-${diaryEntryId}.png`;
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl);

      toast.success('QR code download started');
    } catch (error) {
      console.error('Error downloading QR code:', error);
      toast.error('Failed to download QR code. Please try again.');
    }
  };

  return (
    <div className="px-2">
      {/* Input for adding people */}
      <div className="mb-6">
        <input
          type="text"
          className="w-full px-3 py-3 border border-gray-500 rounded-md focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
          placeholder="Add people via phone number"
          value={phoneNumber}
          onChange={(e) => setPhoneNumber(e.target.value)}
        />
      </div>

      {/* Recently invited people section */}
      <div className="pb-6 border-b">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-2xl font-semibold text-gray-700">
            Recently invited people
          </h3>

          {/* Select All checkbox */}
          {/* <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="select-all"
              className="h-4 w-4 text-yellow-500 focus:ring-yellow-500 border-gray-300 rounded"
              checked={allSelected}
              onChange={handleSelectAll}
            />
            <label htmlFor="select-all" className="text-sm text-gray-700">
              Select All
            </label>
          </div> */}
        </div>

        <div className="space-y-3">
          {invitedPeople.map((person) => (
            <div key={person.id} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  className="h-4 w-4 text-yellow-500 focus:ring-yellow-500 border-gray-300 rounded mr-3"
                  checked={person.selected}
                  onChange={() => handleCheckboxChange(person.id)}
                />
                <div className="w-8 h-8 rounded-full overflow-hidden mr-3 relative">
                  <Image
                    src={person.profilePicture}
                    alt={person.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div>
                  <p className="text-base font-medium">{person.name}</p>
                  <p className="text-xs text-gray-500">{person.email}</p>
                </div>
              </div>
              <button
                className="bg-yellow-100 text-gray-700 border border-gray-500 px-4 py-1 rounded-lg text-sm hover:bg-yellow-200"
                onClick={() => handleShare(person.id)}
              >
                Share
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* General access section */}
      <div className="mb-6 pt-6">
        <h3 className="text-2xl font-semibold text-gray-700 mb-4">
          General access
        </h3>
        <div className="flex items-center">
          <div className="p-1.5 bg-yellow-300 rounded-full flex items-center justify-center text-gray-800 mr-3">
            <Icon icon="tabler:world-check" width="24" height="24" />
          </div>
          <input
            type="text"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"
            value={shareLink}
            readOnly
          />
        </div>

        {/* QR Code section */}
        {qrCodeUrl && (
          <div className="mt-4">
            <div className="flex justify-center">
              <div className="relative w-40 h-40 border border-gray-300 rounded-md overflow-hidden">
                <Image
                  src={qrCodeUrl}
                  alt="QR Code for sharing"
                  fill
                  className="object-contain"
                />
              </div>
            </div>
            <div className="mt-3 flex justify-center">
              <button
                className="flex items-center gap-1 px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition-colors"
                onClick={handleDownloadQR}
              >
                <Icon icon="material-symbols:download" width="20" height="20" />
                Download QR
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Copy link button */}
      <div className="flex justify-between items-center mb-4">
        <button
          className={`flex items-center gap-1 px-4 py-1 rounded-md transition-all duration-200 ${
            isCopied
              ? 'bg-green-100 text-green-700 border border-green-300'
              : 'border border-yellow-500 hover:bg-gray-50'
          }`}
          onClick={handleCopyLink}
        >
          <Icon
            icon={isCopied ? 'mdi:check' : 'material-symbols:add-link-rounded'}
            width="24"
            height="24"
            className={isCopied ? 'text-green-600' : ''}
          />
          {isCopied ? 'Copied!' : 'Copy Link'}
        </button>

        <button
          className="bg-gray-800 text-white px-6 py-2 rounded-md hover:bg-gray-700 flex items-center gap-2"
          onClick={handleShareSelected}
          disabled={isGeneratingLink}
        >
          {isGeneratingLink ? (
            <>
              <span className="animate-spin h-4 w-4 border-2 border-white rounded-full border-t-transparent mr-2"></span>
              Generating...
            </>
          ) : (
            'Generate Link'
          )}
        </button>

      </div>
    </div>
  );
};

export default ShareModalContent;
