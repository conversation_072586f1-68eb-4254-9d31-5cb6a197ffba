'use client';

import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { usePathname } from 'next/navigation';

export default function MainLayout({ children }) {
  const pathname = usePathname();
  const isDiaryPage = pathname.startsWith('/diary');
  const isDashboardPage = pathname.startsWith('/dashboard');
  const isCreateSkinPage = pathname.startsWith('/create-skin');
  const isChat = pathname.startsWith('/chat');

  return (
    <div className="min-h-screen flex flex-col bg-[#FCFDFF]">
      <Header />
      <main className="flex-grow">{children}</main>
      { !isDiaryPage && !isDashboardPage && !isCreateSkinPage && !isChat && <Footer className="mt-auto" />}
    </div>
  );
}
