'use client';

import React, { useState } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import api from '@/lib/api';
import FormInput from '@/components/form/FormInput';
import { FaEye, FaEyeSlash } from 'react-icons/fa';

const AddTutor = () => {
  const queryClient = useQueryClient();

  // Create mutation for adding a tutor
  const createTutorMutation = useMutation({
    mutationFn: async (userData) => {
      // The API will automatically handle toast notifications
      return await api.post('/users/admin/create-tutor', userData);
    },
    onSuccess: () => {
      // Invalidate and refetch relevant queries after successful mutation
      queryClient.invalidateQueries(['tutors']);
      // No need for alert here as toast is handled by API interceptor
    },
    onError: (error) => {
      // Error handling is done in API interceptor
      console.error("Mutation error:", error);
      // You can add additional error handling here if needed
    },
    onSettled: (data, error) => {
      // Actions to perform regardless of success or failure
      console.log("Mutation settled. Data:", data, "Error:", error);
    }
  });

  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

  const validationSchema = Yup.object().shape({
    userId: Yup.string().required('ID is required'),
    email: Yup.string().email('Invalid email').required('Email is required'),
    phoneNumber: Yup.string().required('Phone number is required'),
    bio: Yup.string().required('Biography is required'),
    gender: Yup.string().required('Gender is required'),
     password: Yup.string()
          .matches(
            passwordRegex,
            'Min. 8 chars with uppercase, lowercase, number & special char (@$!%*?&)'
          )
          .required('Password is required'),
  });

  const initialValues = {
    userId: '',
    email: '',
    phoneNumber: '',
    bio: '',
    gender: 'male',
    password: '',
  };

  const handleSubmit = (values, { setSubmitting, resetForm }) => {
    // Add additional fields required by the backend
    const payload = {
      ...values,
      type: "tutor",
      agreedToTerms: true,
      skipVerification: true
    };
    
    console.log("Submitting values:", payload);
    
    // Submit the form data to the API
    createTutorMutation.mutate(payload, {
      onSettled: (data, error) => {
        console.log("Response:", data);
        console.log("Error:", error);
        setSubmitting(false);
      },
      onSuccess: () => {
        console.log("Form submission successful");
        resetForm();
      }
    });
  };

  return (
    <div className="min-h-screen bg-white p-5">

<div className="p-4 border-b border-gray-200">
  <h2 className="card-title text-black text-xl"> Add Tutor</h2>
</div>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
         {({ isSubmitting, errors, touched }) => {
                  const [showPassword, setShowPassword] = useState(false);
                  return(
          <Form className="mx-auto bg-gray-100 p-6 rounded-lg shadow-md">
            <div className="flex flex-wrap gap-4 mb-4">
              <div className="flex-1 min-w-[200px]">
                <label htmlFor="userId" className="block font-bold">
                  ID <span className="text-red-500">*</span>
                </label>
                <FormInput
                  type="text"
                  name="userId"
                  id="userId"
                  placeholder="Write ID here"
                  className={`w-full mt-1 p-2 border ${
                    errors.userId && touched.userId ? 'border-red-500' : 'border-gray-300'
                  } rounded`}
                />
               
              </div>

              <div className="flex-1 min-w-[200px]">
                <label htmlFor="email" className="block font-bold">
                  Email Address <span className="text-red-500">*</span>
                </label>
                <FormInput
                  type="email"
                  name="email"
                  id="email"
                  placeholder="<EMAIL>"
                  className={`w-full mt-1 p-2 border ${
                    errors.email && touched.email ? 'border-red-500' : 'border-gray-300'
                  } rounded`}
                />
               
              </div>

                <div className="flex-1 min-w-[200px] relative">
                                <label htmlFor="password" className="block font-bold">
                                  Password <span className="text-red-500">*</span>
                                </label>
                                <FormInput
                                  type={showPassword ? 'text' : 'password'}
                                  name="password"
                                  id="password"
                                  placeholder="Enter password"
                                  className={`w-full mt-1 p-2 pr-10 border ${
                                    errors.password && touched.password ? 'border-red-500' : 'border-gray-300'
                                  } rounded`}
                                />
                               
                                <span
                                  onClick={() => setShowPassword(prev => !prev)}
                                  className="absolute right-3 top-[42px] cursor-pointer text-gray-600"
                                >
                                  {showPassword ? <FaEyeSlash /> : <FaEye />}
                                </span>
                                
                              </div>
            </div>

            <div className="flex flex-wrap gap-4 mb-4">
              <div className="flex-1 min-w-[200px]">
                <label htmlFor="phoneNumber" className="block font-bold">
                  Phone Number <span className="text-red-500">*</span>
                </label>
                <FormInput
                  type="text"
                  name="phoneNumber"
                  id="phoneNumber"
                  placeholder="123456789"
                  className={`w-full mt-1 p-2 border ${
                    errors.phoneNumber && touched.phoneNumber ? 'border-red-500' : 'border-gray-300'
                  } rounded`}
                />
                
              </div>

              <div className="flex-1 min-w-[200px]">
                <label htmlFor="bio" className="block font-bold">
                  Biography <span className="text-red-500">*</span>
                </label>
                <FormInput
                  type="text"
                  name="bio"
                  id="bio"
                  placeholder="Write Bio here"
                  className={`w-full mt-1 p-2 border ${
                    errors.bio && touched.bio ? 'border-red-500' : 'border-gray-300'
                  } rounded`}
                />
                
              </div>

             <div className="flex-1 min-w-[200px]">
  <label className="block font-bold">
    Select Gender <span className="text-red-500">*</span>
  </label>
  <div className="flex items-center gap-10 mt-4">
    <label className="flex items-center text-gray-600 space-x-2">
      <FormInput
        type="radio"
        name="gender"
        value="male"
      />
      <span>Male</span>
    </label>
    <label className="flex items-center text-gray-600 space-x-2">
      <FormInput
        type="radio"
        name="gender"
        value="female"
      />
      <span>Female</span>
    </label>
  </div>
</div>

            </div>

            <button
              type="submit"
              disabled={isSubmitting || createTutorMutation.isLoading}
              className={`bg-[#FFDE34] hover:bg-yellow-300 text-black font-medium py-2 px-4 rounded mt-10 absolute right-10 ${
                (isSubmitting || createTutorMutation.isLoading) ? 'opacity-50 cursor-not-allowed' : ''
              }`}
            >
              {(isSubmitting || createTutorMutation.isLoading) ? 'Adding...' : 'Add Tutor'}
            </button>
          </Form>
                  );
         }}
      </Formik>
    </div>
  );
};

export default AddTutor;