'use client';
import React from 'react';
import { Icon } from '@iconify/react';
import Link from 'next/link';

const AdminProfile = ({ profileData }) => {
  if (!profileData) {
    return (
      <div className="max-w-7xl mx-auto px-5 xl:px-0 py-8">
        <div className="bg-white p-6 rounded-lg shadow-md text-center">
          <Icon
            icon="mdi:account-alert"
            className="text-4xl mx-auto text-yellow-600 mb-4"
          />
          <h2 className="text-2xl font-semibold text-gray-700">
            Profile Not Found
          </h2>
          <p className="text-gray-500">
            The requested profile data is not available
          </p>
        </div>
      </div>
    );
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getPlatformIcon = (platform) => {
    const iconMap = {
      linkedin: 'linkedin',
      twitter: 'twitter',
      facebook: 'facebook',
      instagram: 'instagram',
      github: 'github',
      youtube: 'youtube',
      // Add more platform mappings as needed
    };

    return iconMap[platform.toLowerCase()] || 'share-variant';
  };

  return (
    <div className="max-w-7xl mx-auto px-5 xl:px-0 py-8 space-y-8">
      {/* Profile Header */}
      <div className="bg-gradient-to-r from-yellow-300 to-yellow-200 rounded-xl p-6 shadow-lg text-yellow-800">
        <div className="flex flex-col md:flex-row items-start gap-6">
          <div className="relative h-32 w-32 rounded-full border-4 border-white bg-gray-200 overflow-hidden shadow-md flex items-center justify-center">
            {profileData.profilePictureUrl ? (
              <img
                src={profileData.profilePictureUrl}
                alt={profileData.name}
                className="h-full w-full object-cover"
              />
            ) : (
              <Icon
                icon="mdi:account-circle"
                className="h-full w-full text-yellow-600"
              />
            )}
          </div>
          <div className="flex-1 space-y-3">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold">{profileData.name}</h1>
                <div className="flex items-center gap-2 text-yellow-800">
                  <Icon icon="mdi:email" className="text-lg" />
                  <span>{profileData.email}</span>
                </div>
              </div>
              <div className="flex gap-2">
                {/* <button className="bg-white/20 hover:bg-white/30 border border-yellow-300 shadow backdrop-blur-sm px-4 py-2 rounded-lg flex items-center gap-2 transition-all">
                  <Icon icon="mdi:pencil" className="text-lg" />
                  <span>Edit Profile</span>
                </button> */}
              </div>
            </div>
            <p className="text-yellow-800">{profileData.bio}</p>
            <div className="flex flex-wrap gap-2">
              <span className="bg-white/20 px-3 py-1 rounded-full text-sm flex items-center gap-1">
                <Icon
                  icon={
                    profileData.isActive
                      ? 'mdi:check-circle'
                      : 'mdi:close-circle'
                  }
                />
                {profileData.isActive ? 'Active' : 'Inactive'}
              </span>
              <span className="bg-white/20 px-3 py-1 rounded-full text-sm flex items-center gap-1">
                <Icon
                  icon={
                    profileData.isConfirmed
                      ? 'mdi:shield-check'
                      : 'mdi:shield-alert'
                  }
                />
                {profileData.isConfirmed ? 'Verified' : 'Unverified'}
              </span>
              {profileData.roles.map((role, index) => (
                <span
                  key={index}
                  className="bg-white/20 px-3 py-1 rounded-full text-sm flex items-center gap-1"
                >
                  <Icon icon="mdi:badge-account" />
                  {role}
                </span>
              ))}

              {Object.entries(profileData.socialLinks).map(
                ([platform, url]) => (
                  <Link
                    key={platform}
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-yellow-700 hover:text-blue-500 transition-colors"
                    title={`Visit ${platform} profile`}
                  >
                    <Icon
                      icon={`mdi:${getPlatformIcon(platform)}`}
                      className="text-2xl"
                    />
                  </Link>
                )
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Profile Sections */}
      <div className="grid grid-cols-1 md:grid-cols-2 bg-white border rounded-lg shadow-md p-6 gap-6">
        {/* Personal Information */}
        <div className="">
          <div className="flex items-center gap-2 border-b pb-3 mb-4">
            <Icon
              icon="mdi:account-details"
              className="text-xl text-blue-600"
            />
            <h3 className="text-lg font-semibold">Personal Information</h3>
          </div>
          <div className="space-y-4">
            <div>
              <p className="text-sm text-gray-500 flex items-center gap-1">
                <Icon icon="mdi:gender-male-female" />
                Gender
              </p>
              <p className="capitalize mt-1">{profileData.gender}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500 flex items-center gap-1">
                <Icon icon="mdi:cake" />
                Date of Birth
              </p>
              <p className="mt-1">
                {formatDate(profileData.dateOfBirth)} ({profileData.age} years)
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500 flex items-center gap-1">
                <Icon icon="mdi:phone" />
                Phone
              </p>
              <p className="mt-1">{profileData.phoneNumber}</p>
            </div>
          </div>
        </div>

        {/* Address Information */}
        <div className="">
          <div className="flex items-center gap-2 border-b pb-3 mb-4">
            <Icon icon="mdi:map-marker" className="text-xl text-blue-600" />
            <h3 className="text-lg font-semibold">Address</h3>
          </div>
          <div className="space-y-4">
            <div>
              <p className="text-sm text-gray-500 flex items-center gap-1">
                <Icon icon="mdi:home" />
                Street
              </p>
              <p className="mt-1">{profileData.address}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500 flex items-center gap-1">
                <Icon icon="mdi:city" />
                City
              </p>
              <p className="mt-1">{profileData.city}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500 flex items-center gap-1">
                <Icon icon="mdi:earth" />
                State/Country
              </p>
              <p className="mt-1">
                {profileData.state}, {profileData.country}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500 flex items-center gap-1">
                <Icon icon="mdi:post" />
                Postal Code
              </p>
              <p className="mt-1">{profileData.postalCode}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminProfile;
