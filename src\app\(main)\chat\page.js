'use client';
import React, { useEffect, useState } from 'react';
import '../../../components/ChatBox/ChatBox.css';
import ChatList from './_components/ChatList';
import Messages from './_components/Messages';
import useDataFetch from '@/hooks/useDataFetch';

const Chatlist = () => {
  // Sample chat history data

  const { data: chats = [], isLoading: isSearching } = useDataFetch({
    queryKey: ['friendship-search'],
    endPoint: `/chat/contacts`,
  });


  // Active chat state
  const [activeChat, setActiveChat] = useState(null);
  const [isMobileView, setIsMobileView] = useState(false);
  const [showChatList, setShowChatList] = useState(true);

  // Only set activeChat when chats change and there's no active chat yet
  useEffect(() => {
    if (chats && chats.length > 0 && !activeChat) {
      setActiveChat(chats[0]);
    }
  }, [chats, activeChat])

  const handleSelectChat = (chat) => {
    setActiveChat(chat);
    if (isMobileView) {
      setShowChatList(false);
    }
  };

  return (
    <div className={`h-[89vh] flex`}>
      {/* Chat History Sidebar */}
      <div
        className={`${
          showChatList ? 'block' : 'hidden'
        } md:block w-full md:w-1/3 lg:w-1/4 border-r bg-white`}
      >
        <ChatList
          chats={chats}
          activeChat={activeChat}
          onSelectChat={handleSelectChat}
        />
      </div>

      {/* Chat Area */}
      <Messages
        activeChat={activeChat}
        showChatList={showChatList}
        setShowChatList={setShowChatList}
      />
    </div>
  );
};

export default Chatlist;
