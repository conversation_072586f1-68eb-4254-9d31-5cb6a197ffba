'use client';
import React from 'react';
import Image from 'next/image';
import HeaderCard from '@/components/HeaderCard';
import FooterSection from '@/components/FooterSection';
import { ButtonIcon } from '@/components/Button';
import { useRouter } from 'next/navigation';

const About = () => {
  const router = useRouter();
  return (
    <div className="bg-white">
      <HeaderCard
        text="About Us"
        bgColor="#FEFCE8"
        textColor="#333"
        textClass=""
      />
      {/* <div>
        <div className="absolute top-[440px] left-64">
          <Image
            src="/assets/images/all-img/aboutus/aboutus14.svg"
            alt="icon"
            width={50}
            height={50}
            priority
          />


        </div>


        <div className="w-[339px] h-[32px] top-[450px]  text-[#1E293B] flex items-center justify-center rounded-lg
     font-inter font-medium text-[24px] leading-[32px] tracking-normal absolute left-52">
          About Us
        </div>



      </div> */}

      <div className="relative flex-col items-center justify-center max-w-7xl mx-auto px-5 xl:px-0">
        <div
          onClick={() => router.back()}
          className="flex justify-start items-center gap-2 my-5 mb-8 max-w-52 group cursor-pointer"
        >
          <ButtonIcon
            icon="famicons:arrow-back-outline"
            innerBtnCls="h-12 w-12 text-white"
            btnIconCls="h-5 w-5 text-white"
          />

          <h3 className="text-xl font-[500] text-gray-800 group-hover:text-yellow-600">
            About Us
          </h3>
        </div>

        <div className="flex flex-col md:flex-row items-center justify-between gap-5 lg:gap-10 p-5 lg:p-10 bg-[#FCF8EF] text-white rounded-lg mx-5 lg:mx-10">
          <div className="bg-white rounded-lg w-full lg:w-3/5 p-5 space-y-2">
            <div className="">
              <Image
                src="/assets/images/all-img/Logo.png"
                alt="icon"
                width={180}
                height={180}
                priority
              />
            </div>

            <div className=" text-[#0C1A24] flex items-center rounded-lg font-medium text-[24px]">
              About Hello English Coaching
            </div>

            <div className="bg-transparent text-[#262626] flex items-center justify-center rounded-lg">
              Writing is a powerful tool for enhancing creativity, as it
              encourages self-expression, critical thinking, and idea
              generation. Whether through storytelling, journaling, or
              brainstorming, writing helps individuals explore new perspectives
              and refine their thoughts.
            </div>
          </div>

          <div className="w-full lg:w-2/5">
            <Image
              src="/assets/images/all-img/aboutus/aboutus13.png"
              alt="icon"
              width={480}
              height={480}
              priority
            />
          </div>
        </div>
      </div>

      <FooterSection />
    </div>
  );
};

export default About;
