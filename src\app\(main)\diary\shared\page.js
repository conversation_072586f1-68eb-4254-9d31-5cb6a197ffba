'use client';
import React, { useState } from 'react';
import Image from 'next/image';
import { <PERSON>H<PERSON>t, FiMessageSquare, FiShare2, FiUser } from 'react-icons/fi';
import SharedDiarySection from '@/components/home/<USER>';

const SharedDiaryPage = () => {
  const [activeTab, setActiveTab] = useState('popular');

  // Sample shared diary entries
  const sharedEntries = {
    popular: [
      {
        id: 1,
        title: 'My First Day at School',
        author: '<PERSON>',
        authorAvatar: '/assets/images/all-img/introduction/Frame 1.png',
        date: 'June 15, 2023',
        content:
          'Today was my first day at the new school. I was nervous but excited to meet new friends and teachers. The campus is much bigger than my old school, and I got lost a few times trying to find my classrooms. But everyone was helpful and friendly.',
        image: '/assets/images/all-img/introduction/Frame 1.png',
        likes: 42,
        comments: 8,
        shares: 5,
      },
      {
        id: 2,
        title: 'Weekend Trip to the Mountains',
        author: '<PERSON>',
        authorAvatar: '/assets/images/all-img/introduction/Frame 2.png',
        date: 'June 10, 2023',
        content:
          'We went on a family trip to the mountains this weekend. The views were breathtaking, and the air was so fresh. We hiked for hours and had a picnic by a beautiful lake. I took so many photos that my phone ran out of storage!',
        image: '/assets/images/all-img/introduction/Frame 2.png',
        likes: 38,
        comments: 12,
        shares: 7,
      },
      {
        id: 3,
        title: 'Learning to Cook',
        author: 'Sophia Williams',
        authorAvatar: '/assets/images/all-img/introduction/Frame 3.png',
        date: 'June 5, 2023',
        content:
          "I tried cooking a new recipe today. It was challenging but fun. I made pasta from scratch for the first time, and though it wasn't perfect, everyone said it tasted good. I'm proud of myself for trying something new.",
        image: '/assets/images/all-img/introduction/Frame 3.png',
        likes: 35,
        comments: 6,
        shares: 3,
      },
    ],
    recent: [
      {
        id: 4,
        title: 'My Art Exhibition',
        author: 'David Lee',
        authorAvatar: '/assets/images/all-img/introduction/Frame 1.png',
        date: 'June 18, 2023',
        content:
          'I had my first art exhibition today at the community center. I was so nervous about showing my paintings to the public, but the feedback was overwhelmingly positive. Several people even asked if my work was for sale!',
        image: '/assets/images/all-img/introduction/Frame 1.png',
        likes: 12,
        comments: 4,
        shares: 2,
      },
      {
        id: 5,
        title: 'A Day at the Beach',
        author: 'Olivia Garcia',
        authorAvatar: '/assets/images/all-img/introduction/Frame 2.png',
        date: 'June 17, 2023',
        content:
          'Spent the day at the beach with friends. The weather was perfect - sunny but not too hot. We swam, played volleyball, and had a barbecue. It was exactly what I needed after a stressful week of exams.',
        image: '/assets/images/all-img/introduction/Frame 2.png',
        likes: 8,
        comments: 2,
        shares: 1,
      },
    ],
    friends: [
      {
        id: 6,
        title: 'My New Puppy',
        author: 'Emma Wilson',
        authorAvatar: '/assets/images/all-img/introduction/Frame 3.png',
        date: 'June 16, 2023',
        content:
          "We adopted a puppy today! He's a 3-month-old golden retriever named Max. He's so playful and adorable. I've already taught him to sit and give paw. Can't wait to share more adventures with my new furry friend!",
        image: '/assets/images/all-img/introduction/Frame 3.png',
        likes: 15,
        comments: 7,
        shares: 3,
      },
    ],
  };

  const displayEntries = sharedEntries[activeTab] || [];

  return (
    <div className="container mx-auto">
      {/* <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Shared Diaries</h1>
        
        <div className="flex space-x-2 bg-white rounded-lg shadow-sm">
          <button
            className={`px-4 py-2 rounded-lg ${activeTab === 'popular' ? 'bg-yellow-100 text-yellow-800' : 'text-gray-700'}`}
            onClick={() => setActiveTab('popular')}
          >
            Popular
          </button>
          <button
            className={`px-4 py-2 rounded-lg ${activeTab === 'recent' ? 'bg-yellow-100 text-yellow-800' : 'text-gray-700'}`}
            onClick={() => setActiveTab('recent')}
          >
            Recent
          </button>
          <button
            className={`px-4 py-2 rounded-lg ${activeTab === 'friends' ? 'bg-yellow-100 text-yellow-800' : 'text-gray-700'}`}
            onClick={() => setActiveTab('friends')}
          >
            Friends
          </button>
        </div>
      </div>
      
      <div className="space-y-6">
        {displayEntries.map((entry) => (
          <div 
            key={entry.id} 
            className="bg-white rounded-lg shadow-md overflow-hidden"
          >
            <div className="p-4 border-b">
              <div className="flex items-center">
                <div className="relative w-10 h-10 rounded-full overflow-hidden mr-3">
                  <Image
                    src={entry.authorAvatar}
                    alt={entry.author}
                    fill
                    className="object-cover"
                  />
                </div>
                <div>
                  <h3 className="font-medium">{entry.author}</h3>
                  <p className="text-sm text-gray-500">{entry.date}</p>
                </div>
              </div>
            </div>
            
            <div className="p-4">
              <h2 className="text-xl font-semibold mb-2">{entry.title}</h2>
              <p className="text-gray-700 mb-4">{entry.content}</p>
              
              {entry.image && (
                <div className="relative h-64 mb-4 rounded-lg overflow-hidden">
                  <Image
                    src={entry.image}
                    alt={entry.title}
                    fill
                    className="object-cover"
                  />
                </div>
              )}
              
              <div className="flex justify-between items-center pt-3 border-t">
                <div className="flex space-x-4">
                  <button className="flex items-center text-gray-600 hover:text-red-500">
                    <FiHeart className="mr-1" />
                    <span>{entry.likes}</span>
                  </button>
                  <button className="flex items-center text-gray-600 hover:text-blue-500">
                    <FiMessageSquare className="mr-1" />
                    <span>{entry.comments}</span>
                  </button>
                  <button className="flex items-center text-gray-600 hover:text-green-500">
                    <FiShare2 className="mr-1" />
                    <span>{entry.shares}</span>
                  </button>
                </div>
                
                <button className="bg-gradient-to-b from-yellow-300 to-yellow-500 text-black font-medium py-1 px-3 rounded-full border-2 border-yellow-100 shadow-md hover:from-yellow-400 hover:to-yellow-600 transition-all duration-300 text-sm">
                  Read More
                </button>
              </div>
            </div>
          </div>
        ))}
        
        {displayEntries.length === 0 && (
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <div className="flex justify-center mb-4">
              <FiUser className="text-gray-400 text-5xl" />
            </div>
            <h3 className="text-xl font-medium text-gray-700 mb-2">No entries found</h3>
            <p className="text-gray-500">
              {activeTab === 'friends' 
                ? 'Your friends haven\'t shared any diary entries yet.' 
                : 'No diary entries found in this category.'}
            </p>
          </div>
        )}
      </div> */}

      <SharedDiarySection />
    </div>
  );
};

export default SharedDiaryPage;
