'use client';
import Button, { ButtonIcon } from '@/components/Button';
import <PERSON><PERSON>iewer from '@/components/EditorViewer';
import DetailsModal from '@/components/form/modal/MissionConfirmationModal';
import TinyMceEditor from '@/components/form/TinyMceEditor';
import GoBack from '@/components/shared/GoBack';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { Editor } from '@tinymce/tinymce-react';
import { Form, Formik } from 'formik';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useSelector } from 'react-redux';

const CommonQnA = () => {
  const { id } = useParams();
  const getEndPoint = `/play/story-maker/play/${id}`;
  const submitEndPoint = `/play/story-maker/play/${id}/submit`;

  const editorRef = useRef(null);
  const [value, setValue] = useState('');
  const { user } = useSelector((state) => state.auth);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  const { data: questionDetails, refetch } = useDataFetch({
    queryKey: [`question-info`, getEndPoint],
    endPoint: getEndPoint,
  });

  const showSubmission =
    !questionDetails?.latest_submission?.is_evaluated &&
    !isSubmitted &&
    questionDetails?.is_played;
  const showFeedback =
    questionDetails?.latest_submission?.is_evaluated &&
    questionDetails?.submission?.feedback;

  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      setSubmitting(true);
      const response = await api.post(submitEndPoint, {content: values.content, story_maker_id: values?.story_maker_id});
      console.log(response);
      setIsSubmitted(true);
    } catch (error) {
      console.log(error);
    } finally{
      setSubmitting(false);
    }
  };

  return (
    <div className="relative">
      <div className="max-w-7xl mx-auto px-5 xl:px-0 relative z-10">
        <GoBack title={'HEC Play'} linkClass="my-5 mb-8 w-full max-w-40" />

        <div className="p-5 rounded-lg bg-[#FFF9FB] shadow-lg space-y-5 mb-10">
          <div className="p-5 bg-[#FCF8EF] rounded-lg [box-shadow:2px_2px_12px_0px_#F5D1B066_inset,_-2px_-2px_12px_0px_#F5D1B066_inset]">
            <h1 className="text-2xl text-yellow-800 font-semibold mb-4">
              {questionDetails?.title}
            </h1>
            <div className="flex items-center gap-6">
              {questionDetails?.picture && (
                <Image
                  src={questionDetails?.picture}
                  alt={questionDetails?.title}
                  width={200}
                  height={200}
                />
              )}
              <div>
                {(questionDetails?.instructions ||
                  questionDetails?.description) && (
                  <p className="font-semibold text-lg text-gray-700">
                    Instruction:
                  </p>
                )}
                <EditorViewer
                  data={
                    questionDetails?.instruction || questionDetails?.description
                  }
                />
              </div>
            </div>
          </div>

          {showSubmission ? (
            <div className="space-y-3">
              <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                <h1 className="text-xl text-yellow-800 font-semibold">
                  My Submission
                </h1>
                <EditorViewer
                  data={
                    questionDetails?.latest_submission?.content?.length > 200
                      ? questionDetails?.latest_submission?.content?.slice(
                          0,
                          400
                        ) + '...'
                      : questionDetails?.latest_submission?.content
                  }
                />
{/* 
                <div className="absolute right-2 top-2">
                  <ButtonIcon
                    icon={'ri:edit-2-fill'}
                    innerBtnCls={'h-10 w-10'}
                    btnIconCls={'h-5 w-5'}
                    onClick={() => setIsSubmitted(true)}
                  />
                </div> */}
              </div>

              <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                <h1 className="text-xl text-yellow-800 font-semibold">
                  Tutor Correction Zone
                </h1>
                {showFeedback && (
                  <>
                    {questionDetails?.submission?.corrections?.grammar?.length >
                      0 && (
                      <div className="rounded-md">
                        <ul className=" text-sm text-gray-800">
                          {questionDetails.submission.corrections.grammar.map(
                            (item, index) => (
                              <EditorViewer key={index} data={item} />
                            )
                          )}
                        </ul>
                      </div>
                    )}

                    <div className="absolute right-2 top-2">
                      <ButtonIcon
                        icon={'arcticons:feedback-2'}
                        innerBtnCls={'h-10 w-10'}
                        btnIconCls={'h-4 w-4'}
                        onClick={() => setShowDetailsModal(true)}
                      />
                    </div>
                  </>
                )}

                <p
                  className={`${
                    !(questionDetails?.latest_submission?.is_evaluated) &&
                    'text-red-600'
                  } text-center mt-2`}
                >
                  {!(questionDetails?.latest_submission?.is_evaluated) &&
                    'Not Confirmed yet'}
                </p>
              </div>
            </div>
          ) : (
            <Formik
              initialValues={{
                content: value || questionDetails?.latest_submission?.content || '',
                story_maker_id: questionDetails?.id,
              }}
              onSubmit={handleSubmit}
              enableReinitialize
            >
              {({isSubmitting}) => (
                <Form>
                  <TinyMceEditor
                    name="content"
                    editorRef={editorRef}
                    initialValue={questionDetails?.latest_submission?.content || ''}
                    onAutoSave={(content) => {
                      setValue(content);
                      handleUpdate({ answer: content, _autoSave: true });
                    }}
                    setValue={setValue}
                  />

                  <div className="flex justify-center mt-3 gap-3">
                    <Button
                      buttonText="Cancel"
                      type="button"
                      onClick={() => setIsSubmitted(false)}
                    />
                    <Button
                      disabled={isSubmitting}
                      buttonText={isSubmitting ? 'Submitting...' : 'Submit'}
                      type="submit"
                      className="bg-yellow-400 hover:bg-yellow-500 text-black"
                    />
                    {/* questionDetails?.latest_submission?.content
                          ? isSubmitting ? 'Updating...' : 'Update'
                          : isSubmitting ? 'Submitting...' : 'Submit' */}
                  </div>
                </Form>
              )}
            </Formik>
          )}
        </div>
      </div>

      <DetailsModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        data={questionDetails?.submission?.corrections?.grammar}
        title="Teachers Feedback"
        link={`/answer`}
        showBtn={false}
      />
    </div>
  );
};

export default CommonQnA;
