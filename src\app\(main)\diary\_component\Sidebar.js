'use client';
import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ButtonIcon } from '@/components/Button';

const Sidebar = () => {
  const [activeMenu, setActiveMenu] = useState(null);
  const pathname = usePathname();
  const buttonSize = { width: 32, height: 32 }; // For consistent button sizing

  // Helper function to check if a menu item is active
  const isItemActive = (item) => {
    if (item.exact) {
      return pathname === item.path;
    } else {
      return pathname === item.path || pathname.startsWith(item.path + '/');
    }
  };

  // Menu items data structure
  const menuItems = [
    {
      name: "Today's Diary",
      path: '/diary',
      exact: true,
    },
    {
      name: 'My Diary',
      path: '/diary/my',
    },
    {
      name: 'Award',
      path: '/diary/award',
    },
    {
      name: 'Owned Item',
      path: '/diary/owned-item',
    },
    {
      name: 'Shared Diary',
      path: '/diary/shared',
    },
    {
      name: 'Mission Management',
      path: '/diary/mission',
      submenu: [
        { name: 'Attendance', path: '/diary/mission/attendance' },
        // { name: 'Success Rate', path: '/diary/mission/success-rate' },
        // { name: 'Milestone', path: '/diary/mission/milestone' },
      ],
    },
  ];

  const handleMenuClick = (menuName) => {
    if (activeMenu === menuName) {
      setActiveMenu(null);
    } else {
      setActiveMenu(menuName);
    }
  };

  return (
    <div className="w-64 bg-[#723F11] p-4 min-h-screen">
      <div className="space-y-4 w-full">
        {menuItems.map((item, index) => (
          <div key={index} className="relative">
            {/* Main menu item */}
            <div className="mb-3">
              <Link
                href={item.submenu ? '#' : item.path}
                onClick={item.submenu ? (e) => {
                  e.preventDefault();
                  handleMenuClick(item.name);
                } : undefined}
                className="block"
              >
                <div
                className={`
  text-black font-medium py-2 px-6 text-center rounded-full whitespace-nowrap
  border-2 border-yellow-100
  shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026] /* Combined shadow-md and your new shadow */
  transition-all duration-300
  ${isItemActive(item) ? 'bg-[#FFFAC2]' : 'bg-gradient-to-b from-yellow-300 to-yellow-500 hover:from-yellow-400 hover:to-yellow-600'}
  ${activeMenu === item.name && item.submenu && !isItemActive(item) ? 'from-yellow-400 to-yellow-600' : ''}
  relative pr-10
  ring-2 ring-[#A36105] {/* This remains for the outer border effect */}
`}
                >
                  {item.name}
                  {item.submenu && (
                    <span className="absolute right-1 top-12 transform -translate-y-1/2 flex items-center justify-center">
                      <ButtonIcon
                        icon={activeMenu === item.name ? 'mdi:arrow-up' : 'mdi:arrow-down'}
                        innerBtnCls="h-10 w-10 flex items-center justify-center"
                        btnIconCls="h-4 w-4"
                      />
                    </span>
                  )}
                </div>
              </Link>
            </div>

            {/* Submenu */}
            {item.submenu && activeMenu === item.name && (
              <div className="bg-white rounded-md overflow-hidden border border-gray-200 mb-3 ml-4 mr-2 absolute z-10 w-[calc(100%-1rem)] shadow-lg">
                {item.submenu.map((subItem, subIndex) => (
                  <React.Fragment key={subIndex}>
                    <Link
                      href={subItem.path}
                      className={`block px-6 py-2 text-gray-700 hover:bg-gray-100 hover:font-bold transition-colors duration-200 ${pathname === subItem.path ? 'bg-[#FFFAC2] font-bold' : ''}`}
                    >
                      {subItem.name}
                    </Link>
                    {subIndex < item.submenu.length - 1 && (
                      <div className="mx-2 border-b border-dashed border-gray-300"></div>
                    )}
                  </React.Fragment>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default Sidebar;