'use client';

import React, { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import api from '@/lib/api';
import NewTablePage from '@/components/form/NewTablePage';
import { useRouter } from 'next/navigation';

const MissionEssayList = () => {
  const router = useRouter();
  // State variables
  const [missions, setMissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchField, setSearchField] = useState('title');
  const [sortField, setSortField] = useState('createdAt');
  const [sortDirection, setSortDirection] = useState('ASC');
  const [activeTab, setActiveTab] = useState(0); // 0 for Weekly, 1 for Monthly
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  // Add new state for selected mission data
  const [selectedMission, setSelectedMission] = useState(null);
  const [fetchingMission, setFetchingMission] = useState(false);

  // Tabs content
  const tabs = [
    { name: 'Weekly Mission', frequency: 'weekly' },
    { name: 'Monthly Mission', frequency: 'monthly' }
  ];

  // Dynamic name filter options based on active tab
  const getNameFilterOptions = () => {
    if (activeTab === 0) { // Weekly tab
      return [
        { label: 'Title', value: 'title' },
        { label: 'Week', value: 'week' },
      ];
    } else { // Monthly tab
      return [
        { label: 'Title', value: 'title' },
        { label: 'Month', value: 'month' },
      ];
    }
  };

  // Table columns definition
  const columns = [
    { label: 'WEEK', field: 'week' },
    { label: 'MISSION TITLE', field: 'title', sortable: true },
    { label: 'WORD LIMIT', field: 'wordLimit' },
    { label: 'DEADLINE', field: 'deadline' },
  ];

  // Function to fetch a single mission by ID
  const fetchMissionById = async (missionId) => {
    try {
      setFetchingMission(true);
      
      // API call to get the specific mission
      const response = await api.get(`/admin-essay/${missionId}`);
      
      if (response && response.success) {
        console.log('Fetched mission details:', response.data);
        setSelectedMission(response.data);
        return response.data;
      } else {
        console.error('Failed to fetch mission details:', response?.message || 'Unknown error');
        return null;
      }
    } catch (error) {
      console.error('Error fetching mission details:', error);
      return null;
    } finally {
      setFetchingMission(false);
    }
  };

  // Handle edit action - navigate to the createMissionEssay page with the mission data
  const handleEditMission = async (mission) => {
    // Extract the actual mission ID from the composite ID
    const missionId = mission.missionId;
    
    console.log('Fetching mission details for editing:', missionId);
    const missionDetails = await fetchMissionById(missionId);
    
    if (missionDetails) {
      // Store mission details in session storage to pass to the edit page
      sessionStorage.setItem('editMissionData', JSON.stringify(missionDetails));
      
      // Navigate to the createMissionEssay page with edit=true parameter
      router.push(`/dashboard/module-management/hecEssay/createMissionEssay?edit=true&id=${missionId}`);
    }
  };

  // Table actions definition with updated edit handler
  const actions = [
    {
      icon: 'heroicons-outline:pencil',
      className: 'text-gray-600 hover:text-blue-600',
      onClick: handleEditMission
    },
    {
      icon: 'heroicons-outline:eye',
      className: 'text-blue-600 hover:text-blue-700',
      onClick: (mission) => console.log('View', mission)
    }
  ];

  // Fetch data from API
  const fetchMissions = async () => {
    try {
      setLoading(true);
      
      // Base params required by the API
      const params = {
        page: currentPage,
        limit: rowsPerPage,
        timeFrequency: tabs[activeTab].frequency,
        sortBy: sortField,
        sortDirection: sortDirection
      };
      
      const response = await api.get('/admin-essay', { params });

      if (response && response.success) {
        // Format mission data for table display - expanding tasks into individual rows
        const missionItems = response.data.items || [];
        
        // Sort missions by sequenceNumber if necessary
        const sortedMissions = [...missionItems].sort((a, b) => 
          (a.sequenceNumber || 0) - (b.sequenceNumber || 0)
        );
        
        let formattedMissions = [];
        
        sortedMissions.forEach((mission) => {
          // Skip missions with no tasks
          if (!mission.tasks || mission.tasks.length === 0) {
            return;
          }
          
          // Create a row for each task in the mission
          mission.tasks.forEach(task => {
            formattedMissions.push({
              id: `${mission.id}-${task.id}`, // Composite ID for uniqueness
              missionId: mission.id, // Keep reference to parent mission
              taskId: task.id,
              sequenceNumber: mission.sequenceNumber || 0,
              week: `Week ${task.metaData?.week || ''}`,
              month: `Month ${task.metaData?.month || ''}`,
              title: task.title || 'Untitled Mission',
              wordLimit: `${task.wordLimitMinimum} - ${task.wordLimitMaximum}`,
              deadline: `${task.deadline} ${task.deadline === 1 ? 'day' : 'days'}`,
              description: task.description,
              instructions: task.instructions,
              isActive: task.isActive,
              timeFrequency: mission.timeFrequency
            });
          });
        });
        
        // Apply client-side filtering if search term exists
        if (searchTerm) {
          formattedMissions = formattedMissions.filter(mission => {
            if (searchField === 'title') {
              return mission.title.toLowerCase().includes(searchTerm.toLowerCase());
            } else if (searchField === 'week') {
              return mission.week.toLowerCase().includes(searchTerm.toLowerCase());
            } else if (searchField === 'month') {
              return mission.month.toLowerCase().includes(searchTerm.toLowerCase());
            }
            return true;
          });
        }
        
        setMissions(formattedMissions);
        
        // If filtering client-side, adjust pagination metrics accordingly
        if (searchTerm) {
          setTotalItems(formattedMissions.length);
          setTotalPages(Math.ceil(formattedMissions.length / rowsPerPage));
        } else {
          // Count total number of tasks across all missions for pagination
          const totalTasks = missionItems.reduce((count, mission) => 
            count + (mission.tasks?.length || 0), 0);
          
          setTotalItems(totalTasks);
          setTotalPages(Math.ceil(totalTasks / rowsPerPage));
        }
      } else {
        console.error('Failed to fetch missions:', response?.message || 'Unknown error');
      }
    } catch (error) {
      console.error('Error fetching missions:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMissions();
  }, [activeTab, currentPage, rowsPerPage, sortField, sortDirection]);
  
  // Separate effect for search to prevent unnecessary API calls
  useEffect(() => {
    // Only re-fetch if no search term (when clearing search)
    // Otherwise filtering happens client-side in the fetchMissions function
    if (!searchTerm) {
      fetchMissions();
    }
  }, [searchTerm, searchField]);

  // Reset search field when changing tabs if current field is not available in new tab
  useEffect(() => {
    const availableFields = getNameFilterOptions().map(option => option.value);
    if (!availableFields.includes(searchField)) {
      setSearchField('title'); // Reset to title if current field not available
    }
  }, [activeTab]);

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle search
  const handleSearch = (value) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page on new search
  };

  // Handle name filter change
  const handleNameFilterChange = (field) => {
    setSearchField(field);
    setCurrentPage(1); // Reset to first page on filter change
  };

  // Handle sort change
  const handleSort = (field) => {
    if (field === sortField) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');
    } else {
      // New field, default to ascending
      setSortField(field);
      setSortDirection('ASC');
    }
    setCurrentPage(1); // Reset to first page on sort change
  };

  // Tab switching handler
  const handleTabChange = (index) => {
    setActiveTab(index);
    setCurrentPage(1); // Reset to first page on tab change
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h1 className="text-2xl font-semibold mb-6">Mission Essay List</h1>
      
      {/* Tab buttons */}
      <div className="flex border-b border-gray-200 mb-6">
        {tabs.map((tab, index) => (
          <button
            key={index}
            className={`py-3 px-6 font-medium text-sm focus:outline-none ${
              activeTab === index 
                ? 'text-black border-b-2 border-yellow-500 hover:bg-[#FEFCE8]' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => handleTabChange(index)}
          >
            {tab.name}
          </button>
        ))}
      </div>
      
      {/* Show loading indicator when fetching individual mission */}
      {fetchingMission && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg shadow-lg">
            <p className="text-gray-700">Loading mission data...</p>
          </div>
        </div>
      )}
      
      {/* Table component */}
      <NewTablePage
        title=""
        createButton="Create Mission"
        showCreateButton={false}
        columns={columns}
        actions={actions}
        data={missions}
        loading={loading}
        currentPage={currentPage}
        totalPages={totalPages}
        changePage={handlePageChange}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        // Search and filter props
        showSearch={true}
        showNameFilter={true}
        showSortFilter={true}
        onSearch={handleSearch}
        onNameFilterChange={handleNameFilterChange}
        onSort={handleSort}
        nameFilterOptions={getNameFilterOptions()}
        searchTerm={searchTerm}
        searchField={searchField}
        sortField={sortField}
        sortDirection={sortDirection}
        showCheckboxes={false}
      />
    </div>
  );
};

export default MissionEssayList;