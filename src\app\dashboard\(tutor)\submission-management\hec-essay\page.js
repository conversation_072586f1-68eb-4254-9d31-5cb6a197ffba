'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import EssaySubmissionsList from './_components/EssaySubmissionsList';
import MissionEssay from './_components/MissionEssay';
import HecSubmissionLayout from '@/components/layouts/HecSubmissionLayout';

const HecEssay = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');

  // Set default active tab or use the one from URL
  const [activeTab, setActiveTab] = useState(tabParam || 'essaySubmissions');

  // Update state when URL changes
  useEffect(() => {
    if (tabParam) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  // Define tabs for the layout
  const tabs = [
    { name: 'Essay Submissions', value: 'essaySubmissions' },
    { name: 'Mission Essay', value: 'missionEssay' }
  ];

  // Function to render the correct content based on active tab
  const renderContent = () => {
    switch(activeTab) {
      case 'essaySubmissions':
        return <EssaySubmissionsList />;
      case 'missionEssay':
        return <MissionEssay />;
      default:
        return <div>Select a tab</div>;
    }
  };

  return (
    <HecSubmissionLayout
      activeTab={activeTab}
      tabs={tabs}
      title="HEC Essay"
      basePath="/dashboard/submission-management/hec-essay"
    >
      {renderContent()}
    </HecSubmissionLayout>
  );
};

export default HecEssay;
