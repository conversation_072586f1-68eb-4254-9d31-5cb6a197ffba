'use client';

import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { QueryClientProvider } from '@tanstack/react-query';
import { store, persistor } from '@/store/store';
import { queryClient } from '@/lib/queryClient';
import { Toaster } from 'sonner';
import { NotificationProvider } from '@/components/notifications';

function LoadingState() {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
    </div>
  );
}

export function Providers({ children }) {
  return (
    <Provider store={store}>
      <PersistGate loading={<LoadingState />} persistor={persistor}>
        <QueryClientProvider client={queryClient}>
          <NotificationProvider>
            <Toaster position="top-right" closeButton richColors/>
            {children}
          </NotificationProvider>
        </QueryClientProvider>
      </PersistGate>
    </Provider>
  );
}
