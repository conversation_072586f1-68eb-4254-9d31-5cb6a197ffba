import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  // Question state
  blanks: [],
  editorContent: '',
  points: '',
  
  // App state
  activeItem: 'waterfall',
};

export const hecPlaySlice = createSlice({
  name: 'hecPlay',
  initialState,
  reducers: {
    // Question actions
    addBlank: (state, action) => {
      state.blanks.push({ id: action.payload, answer: '' });
    },
    removeBlank: (state, action) => {
      state.blanks = state.blanks.filter((blank) => blank.id !== action.payload);
    },
    updateBlankAnswer: (state, action) => {
      const { id, answer } = action.payload;
      const blankIndex = state.blanks.findIndex((blank) => blank.id === id);
      if (blankIndex !== -1) {
        state.blanks[blankIndex].answer = answer;
      }
    },
    setEditorContent: (state, action) => {
      state.editorContent = action.payload;
    },
    setPoints: (state, action) => {
      state.points = action.payload;
    },
    
    // App actions
    setActiveItem: (state, action) => {
      state.activeItem = action.payload;
    },
    
    // Reset actions
    resetQuestionState: (state) => {
      state.blanks = [];
      state.editorContent = '';
      state.points = '';
    },
  },
});

export const {
  addBlank,
  removeBlank,
  updateBlankAnswer,
  setEditorContent,
  setPoints,
  setActiveItem,
  resetQuestionState,
} = hecPlaySlice.actions;

// Selectors
export const selectBlanks = (state) => state.hecPlay.blanks;
export const selectEditorContent = (state) => state.hecPlay.editorContent;
export const selectPoints = (state) => state.hecPlay.points;
export const selectActiveItem = (state) => state.hecPlay.activeItem;

export default hecPlaySlice.reducer;
