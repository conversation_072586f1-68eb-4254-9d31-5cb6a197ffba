'use client';
import { useRef, useEffect, useState } from 'react';
import TeacherFeedbackModal from '../teacher-feedback/page';
import Image from 'next/image';


const EssayModal = ({ essay, onClose }) => {
  const modalRef = useRef(null);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);

  // Generate today's date in the format "29th May, 2025"
  const formatTodayDate = () => {
    const today = new Date();
    
    // Get day with ordinal suffix (1st, 2nd, 3rd, etc.)
    const day = today.getDate();
    const ordinalSuffix = getDayOrdinalSuffix(day);
    
    // Get month name
    const monthNames = ["January", "February", "March", "April", "May", "June", 
                        "July", "August", "September", "October", "November", "December"];
    const month = monthNames[today.getMonth()];
    
    // Get year
    const year = today.getFullYear();
    
    return `${day}${ordinalSuffix} ${month}, ${year}`;
  };
  
  // Helper function to get the ordinal suffix for a day number
  const getDayOrdinalSuffix = (day) => {
    if (day > 3 && day < 21) return 'th';
    switch (day % 10) {
      case 1: return 'st';
      case 2: return 'nd';
      case 3: return 'rd';
      default: return 'th';
    }
  };

  const todayDateFormatted = formatTodayDate();

  useEffect(() => {
    // Handle escape key press
    const handleEscapeKey = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    // Handle click outside
    const handleClickOutside = (e) => {
      if (modalRef.current && !modalRef.current.contains(e.target)) {
        onClose();
      }
    };

    // Add event listeners
    document.addEventListener('keydown', handleEscapeKey);
    document.addEventListener('mousedown', handleClickOutside);
    
    // Prevent body scrolling when modal is open
    document.body.style.overflow = 'hidden';

    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.removeEventListener('mousedown', handleClickOutside);
      // Restore body scrolling when modal is closed
      document.body.style.overflow = 'auto';
    };
  }, [onClose]);

  // User Avatar component with standard user icon
  const UserAvatar = ({ name }) => {
    return (
      <div className="flex items-center justify-center h-8 w-8 rounded-full bg-amber-100 text-amber-800">
        <svg 
          className="h-5 w-5" 
          xmlns="http://www.w3.org/2000/svg" 
          viewBox="0 0 24 24" 
          fill="currentColor"
        >
          <path 
            fillRule="evenodd" 
            d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z" 
            clipRule="evenodd" 
          />
        </svg>
      </div>
    );
  };

  if (!essay) return null;

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div 
          ref={modalRef}
          className="bg-white rounded-lg shadow-xl w-full max-w-7xl max-h-[90vh] overflow-hidden flex flex-col"
        >
          {/* Modal Header */}
          <div className="flex justify-between items-center border-b px-6 py-4">
            <div className="flex items-center">
              <h3 className="text-xl font-semibold text-gray-900 mr-4">View Essay</h3>
              
            </div>
            <button
              type="button"
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500 focus:outline-none"
            >
              <span className="sr-only">Close</span>
              <svg className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          {/* User and Date Info */}
          <div className="px-6 py-3 border-b">
            <div className="flex justify-between items-center">
              <div className="">
                <span className="text-black mr-2">Submitted by:</span>
                <div className="flex items-center space-x-2 mt-5">
                  <UserAvatar name={essay.submittedBy} />
                  <span className="font-medium">{essay.submittedBy}</span>
                </div>
              </div>
              <div className="">
                <span className="mr-2 text-black">Date:</span>
                <div className="flex items-center bg-yellow-100 rounded-full px-3 py-1 mt-5">
                  <svg 
                    className="h-4 w-4 text-yellow-500 mr-2" 
                    xmlns="http://www.w3.org/2000/svg" 
                    viewBox="0 0 24 24" 
                    fill="currentColor"
                  >
                    <path d="M6.75 2.25A.75.75 0 017.5 3v1.5h9V3A.75.75 0 0118 3v1.5h.75a3 3 0 013 3v11.25a3 3 0 01-3 3H5.25a3 3 0 01-3-3V7.5a3 3 0 013-3H6V3a.75.75 0 01.75-.75zm13.5 9a1.5 1.5 0 00-1.5-1.5H5.25a1.5 1.5 0 00-1.5 1.5v7.5a1.5 1.5 0 001.5 1.5h13.5a1.5 1.5 0 001.5-1.5v-7.5z" />
                  </svg>
                  <span className="font-medium text-sm">{todayDateFormatted}</span>
                </div>
              </div>
            </div>
          </div>
          
          {/* Side-by-Side Layout */}
          <div className="flex flex-1 overflow-hidden">
            {/* Left Side - Original Essay */}
            <div className="w-1/2 border-r p-6 overflow-y-auto">
              <div className="mb-4">
                <h4 className="text-lg font-medium">Original Essay</h4>
              </div>
              
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 h-full overflow-y-auto">
                <p className="whitespace-pre-wrap">{essay.content}</p>
              </div>
            </div>
            
            {/* Right Side - Tutor Edits */}
            <div className="w-1/2 p-6 overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <div>
                  <h4 className="text-lg font-medium text-amber-700">Tutor Edits</h4>
                  <div className="text-sm text-gray-500">Edited by tutor</div>
                </div>

                <div className="flex items-center">
                  <span className="text-gray-600 mr-2">Date:</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">{essay.submissionDate}</span>
                  </div>
                </div>
              </div>
              
              {/* Feedback button moved inside the tutor section */}
              {/* <button
                onClick={() => setShowFeedbackModal(true)}
                className="mb-4 flex items-center hover:opacity-80 transition-opacity"
              >
                <Image
                  src="/assets/images/all-img/feedback-bg.png"
                  alt="Teacher Feedback"
                  width={150}
                  height={50}
                  className="h-10 w-auto"
                  priority
                />
              </button> */}
              
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 h-full overflow-y-auto">
                <div className="whitespace-pre-wrap">
                  {essay.tutorEdits || essay.content}
                </div>
              </div>
            </div>
          </div>
          
          {/* Added Modal Footer */}
          <div className="border-t px-6 py-4 flex justify-end space-x-4 bg-gray-50">
            <button
              onClick={() => setShowFeedbackModal(true)}
              className="flex items-center justify-center px-4 py-2 bg-amber-100 text-amber-800 rounded-lg hover:bg-amber-200 transition-colors focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2"
            >
              <svg 
                className="h-5 w-5 mr-2" 
                xmlns="http://www.w3.org/2000/svg" 
                viewBox="0 0 24 24" 
                fill="currentColor"
              >
                <path d="M21.731 2.269a2.625 2.625 0 00-3.712 0l-1.157 1.157 3.712 3.712 1.157-1.157a2.625 2.625 0 000-3.712zM19.513 8.199l-3.712-3.712-8.4 8.4a5.25 5.25 0 00-1.32 2.214l-.8 2.685a.75.75 0 00.933.933l2.685-.8a5.25 5.25 0 002.214-1.32l8.4-8.4z" />
                <path d="M5.25 5.25a3 3 0 00-3 3v10.5a3 3 0 003 3h10.5a3 3 0 003-3V13.5a.75.75 0 00-1.5 0v5.25a1.5 1.5 0 01-1.5 1.5H5.25a1.5 1.5 0 01-1.5-1.5V8.25a1.5 1.5 0 011.5-1.5h5.25a.75.75 0 000-1.5H5.25z" />
              </svg>
              View Feedback
            </button>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-white border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            >
              Close
            </button>
           
          </div>
        </div>
      </div>
      
      {/* Teacher Feedback Modal */}
      {showFeedbackModal && (
        <TeacherFeedbackModal 
          essay={essay} 
          onClose={() => setShowFeedbackModal(false)} 
        />
      )}
    </>
  );
};

export default EssayModal;