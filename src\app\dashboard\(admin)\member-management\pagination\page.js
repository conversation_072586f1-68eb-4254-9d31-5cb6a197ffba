'use client';

import React from 'react';
import { Icon } from '@iconify/react';

const Pagination = ({ 
  currentPage, 
  totalPages, 
  onPageChange, 
  rowsPerPage, 
  setRowsPerPage, 
  totalItems 
}) => {
  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages = [];
    
    // For small number of pages, show all
    if (totalPages <= 5) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
      return pages;
    }
    
    // Always show first page
    pages.push(1);
    
    // Show pages around current page
    let startPage = Math.max(2, currentPage - 1);
    let endPage = Math.min(totalPages - 1, currentPage + 1);
    
    // Add ellipsis if needed before middle pages
    if (startPage > 2) {
      pages.push('...');
    }
    
    // Add middle pages
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    // Add ellipsis if needed after middle pages
    if (endPage < totalPages - 1) {
      pages.push('...');
    }
    
    // Always show last page if it's different from current range
    if (endPage < totalPages) {
      pages.push(totalPages);
    }
    
    return pages;
  };

  // Calculate start and end item numbers
  const startItem = totalItems === 0 ? 0 : (currentPage - 1) * rowsPerPage + 1;
  const endItem = Math.min(currentPage * rowsPerPage, totalItems);

  // Handle rows per page change
  const handleRowsPerPageChange = (e) => {
    const newValue = parseInt(e.target.value, 10);
    setRowsPerPage(newValue);
  };

  return (
    <div className="flex items-center justify-between py-4 px-2 border-t">
      {/* Range display */}
      <div className="text-sm text-gray-600">
        {`${startItem}-${endItem} of ${totalItems}`}
      </div>
      
      {/* Page navigation */}
      <div className="flex items-center space-x-1">
        {/* Previous page button */}
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage <= 1}
          className={`p-1 rounded ${
            currentPage <= 1 ? 'text-gray-400 cursor-not-allowed' : 'text-blue-600 hover:bg-blue-50'
          }`}
          aria-label="Previous page"
        >
          <Icon icon="material-symbols:chevron-left" className="text-xl" />
        </button>
        
        {/* Page numbers */}
        {getPageNumbers().map((page, index) => (
          page === '...' ? (
            <span key={`ellipsis-${index}`} className="px-2">...</span>
          ) : (
            <button
              key={`page-${page}`}
              onClick={() => onPageChange(page)}
              className={`w-8 h-8 flex items-center justify-center rounded-full ${
                currentPage === page 
                  ? 'bg-yellow-500 text-white' 
                  : 'hover:bg-gray-100'
              }`}
            >
              {page}
            </button>
          )
        ))}
        
        {/* Next page button */}
        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage >= totalPages || totalPages === 0}
          className={`p-1 rounded ${
            currentPage >= totalPages || totalPages === 0 ? 'text-gray-400 cursor-not-allowed' : 'text-blue-600 hover:bg-blue-50'
          }`}
          aria-label="Next page"
        >
          <Icon icon="material-symbols:chevron-right" className="text-xl" />
        </button>
      </div>
      
      {/* Rows per page selector */}
      <div className="flex items-center space-x-2">
        <span className="text-sm text-gray-600">Rows per page:</span>
        <select
          value={rowsPerPage}
          onChange={handleRowsPerPageChange}
          className="border rounded p-1 text-sm"
        >
          <option value={10}>10</option>
          <option value={25}>25</option>
          <option value={50}>50</option>
          <option value={100}>100</option>
        </select>
      </div>
    </div>
  );
};

export default Pagination;