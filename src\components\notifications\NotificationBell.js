import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Icon } from '@iconify/react';
import {
  toggleNotificationPanel,
  fetchUnreadCount,
  selectUnreadCount,
  selectNotificationPanelState
} from '@/store/features/notificationSlice';

const NotificationBell = () => {
  const dispatch = useDispatch();
  const unreadCount = useSelector(selectUnreadCount);
  const isOpen = useSelector(selectNotificationPanelState);
  const { isAuth } = useSelector((state) => state.auth);

  // Fetch unread count on component mount and set up interval only if authenticated
  useEffect(() => {
    // Only fetch notifications if user is authenticated
    if (isAuth) {
      dispatch(fetchUnreadCount());

      // Set up interval to refresh unread count every 60 seconds
      const interval = setInterval(() => {
        dispatch(fetchUnreadCount());
      }, 60000);

      return () => clearInterval(interval);
    }
  }, [dispatch, isAuth]);

  const handleTogglePanel = () => {
    dispatch(toggleNotificationPanel());
  };

  return (
    <div className="relative">
      <button
        className={`relative p-2 rounded-full transition-colors ${
          isOpen ? 'bg-yellow-100' : 'hover:bg-gray-100'
        }`}
        onClick={handleTogglePanel}
        aria-label="Notifications"
      >
        <Icon icon="lucide:bell" className="w-5 h-5 text-gray-700" />

        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-red-500 rounded-full">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>
    </div>
  );
};

export default NotificationBell;
