const skins = [
  {
    id: '60398c95-a109-4610-b366-88156f1c0dd7',
    name: '<PERSON> Frame',
    previewImage: '/assets/images/all-img/introduction/Frame 3.png',
    config: {
      title: {
        fontFamily: 'Arial',
        fontSize: '24px',
        color: '#333333',
        fontWeight: 'bold',
        top: '40px',
        left: '280px',
        position: 'absolute',
        width: '285px',
        height: '40px',
        overflow: 'auto',
      },
      content: {
        fontFamily: 'Times New Roman',
        fontSize: '16px',
        color: '#555555',
        lineHeight: '1.5',
        top: '80px',
        left: '280px',
        position: 'absolute',
        width: '270px',
        height: '150px',
        overflow: 'auto',
      },
      entryDate: {
        fontFamily: 'Courier New',
        fontSize: '14px',
        color: '#777777',
        fontStyle: 'italic',
        top: '25px',
        left: '280px',
        position: 'absolute',
        width: '400px',
        height: '30px',
      },
    },
  },
  {
    id: '7816e7a0-5eb0-406a-a463-8d13cddafe35',
    name: '<PERSON> Frame',
    previewImage: '/assets/images/all-img/introduction/Frame 1.png',
    config: {
      title: {
        fontFamily: 'Georgia',
        fontSize: '20px',
        color: '#2a5d3c',
        fontWeight: 'bold',
        bottom: '170px',
        left: '52%',
        transform: 'translateX(-50%)',
        position: 'absolute',
        width: '400px',
        height: '35px',
        overflow: 'auto',
      },
      content: {
        fontFamily: 'Verdana',
        fontSize: '15px',
        color: '#444444',
        lineHeight: '1.6',
        bottom: '90px',
        left: '52%',
        transform: 'translateX(-50%)',
        position: 'absolute',
        width: '400px',
        height: '80px',
        overflow: 'auto',
      },
      entryDate: {
        fontFamily: 'Tahoma',
        fontSize: '10px',
        color: '#666666',
        fontStyle: 'normal',
        bottom: '205px',
        left: '52%',
        transform: 'translateX(-50%)',
        position: 'absolute',
        width: '400px',
        height: '15px',
      },
    },
  },
  // {
  //   id: 'c16fc3b7-ccae-4029-b0b0-58b7cd315e23',
  //   name: 'Cat Frame',
  //   previewImage: '/assets/images/all-img/Logo.png',
  //   config: {
  //     title: {
  //       fontFamily: 'Comic Sans MS',
  //       fontSize: '22px',
  //       color: '#ff6b6b',
  //       fontWeight: 'bold',
  //       position: { top: '55px', left: '65px' },
  //       absolute: true,
  //       width: '310px',
  //       height: '42px',
  //     },
  //     content: {
  //       fontFamily: 'Helvetica',
  //       fontSize: '16px',
  //       color: '#333333',
  //       lineHeight: '1.4',
  //       position: { top: '130px', left: '65px' },
  //       absolute: true,
  //       width: '390px',
  //       height: '290px',
  //     },
  //     entryDate: {
  //       fontFamily: 'Arial',
  //       fontSize: '14px',
  //       color: '#888888',
  //       fontStyle: 'italic',
  //       position: { top: '35px', left: '340px' },
  //       absolute: true,
  //       width: '145px',
  //       height: '32px',
  //     },
  //   },
  // },
  {
    id: 'e0cb0f88-553c-49a2-994e-ffb8732be7a4',
    name: 'Panda Frame',
    previewImage: '/assets/images/all-img/introduction/Frame 2.png',
    config: {
      title: {
        fontFamily: 'Trebuchet MS',
        fontSize: '25px',
        color: '#222222',
        fontWeight: 'bold',
        top: '90px',
        left: '105px',
        position: 'absolute',
        width: '410px',
        height: '44px',
        overflow: 'auto',
      },
      content: {
        fontFamily: 'Calibri',
        fontSize: '17px',
        color: '#444444',
        lineHeight: '1.5',
        top: '120px',
        left: '105px',
        position: 'absolute',
        width: '410px',
        height: '180px',
        overflow: 'auto',
      },
      entryDate: {
        fontFamily: 'Segoe UI',
        fontSize: '12px',
        color: '#666666',
        fontStyle: 'normal',
        top: '80px',
        left: '105px',
        position: 'absolute',
        width: '155px',
        height: '20px',
      },
    },
  },
];
export const getTitleStyle = (id) => {
  const skin = skins.find((skin) => skin.id === id);
  return skin ? skin.config.title : null;
};

export const getContentStyle = (id) => {
  const skin = skins.find((skin) => skin.id === id);
  return skin ? skin.config.content : null;
};

export const getDateStyle = (id) => {
  const skin = skins.find((skin) => skin.id === id);
  return skin ? skin.config.entryDate : null;
};

export default skins;
