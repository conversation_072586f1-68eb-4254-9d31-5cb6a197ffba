'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import api from '@/lib/api';
import useDataFetch from '@/hooks/useDataFetch';
import HecSubmissionLayout from '@/components/layouts/HecSubmissionLayout';
import { Icon } from '@iconify/react';
import { formatDate } from '@/utils/dateFormatter';

const MissionEssayReviewPage = () => {
  const { id } = useParams();
  const router = useRouter();
  const [correctionText, setCorrection] = useState('');
  const [feedback, setFeedback] = useState('');
  const [score, setScore] = useState('');
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState(tabParam || 'missionEssay');
  
  useEffect(() => {
    if (tabParam) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  // Define tabs for the layout
  const tabs = [
    { name: 'Essay Submissions', value: 'essaySubmissions' },
    { name: 'Mission Essay', value: 'missionEssay' }
  ];

  // Fetch mission essay entry data
  const { data, isLoading, error } = useDataFetch({
    queryKey: 'mission-essay-entry-review',
    endPoint: `/tutor/essay/mission/entries/${id}/start-review`,
    method: 'POST',
    params: {},
    enabled: !!id,
  });

  // Initialize correction text with original content when data is loaded
  useEffect(() => {
    if (data?.content) {
      setCorrection(data.content);
    }
  }, [data?.content]);

  // Submit review function
  const submitReview = async () => {
    if (!score) {
      toast.error('Please provide a score');
      return;
    }

    if (!feedback.trim()) {
      toast.error('Please provide feedback');
      return;
    }

    try {
      const response = await api.post(`/tutor/essay/mission/entries/${id}/correction`, {
        correctionText,
        feedback,
        score: parseInt(score),
      });

      if (response.success) {
        toast.success('Review submitted successfully');
        router.push('/dashboard/submission-management/hec-essay?tab=missionEssay');
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      toast.error(err.message || 'Failed to submit review');
    }
  };

  if (isLoading) {
    return (
      <HecSubmissionLayout 
        activeTab={activeTab} 
        tabs={tabs} 
        title="HEC Essay"
        basePath="/dashboard/submission-management/hec-essay"
      >
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
        </div>
      </HecSubmissionLayout>
    );
  }

  if (error) {
    return (
      <HecSubmissionLayout 
        activeTab={activeTab} 
        tabs={tabs} 
        title="HEC Essay"
        basePath="/dashboard/submission-management/hec-essay"
      >
        <div className="bg-red-100 text-red-700 p-4 rounded-md">
          Error loading mission essay entry: {error.message}
        </div>
      </HecSubmissionLayout>
    );
  }

  return (
    <HecSubmissionLayout 
      activeTab={activeTab} 
      tabs={tabs} 
      title="HEC Essay"
      basePath="/dashboard/submission-management/hec-essay"
    >
      <div className="flex justify-between items-center mb-2">
        <div className="flex gap-4 items-center">
          <h6 className="text-sm text-black font-medium">
            Submitted by:
          </h6>
          <h2 className="text-[#464646] font-normal">
            {data?.studentName}
          </h2>
        </div>
        <div className="flex items-center gap-3 text-sm text-gray-500">
          <div className="font-medium text-sm text-black">Date:</div>
          <div className="flex items-center gap-3">
            <Icon
              icon="uil:calender"
              width="24"
              height="24"
              className="mx-auto text-gray-900 p-1 rounded-full bg-[#FFF189]"
            />
            {formatDate(data.submittedAt, 'ordinal')}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 bg-white p-6 rounded-lg shadow">
        {/* Essay Content */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Original Essay</h3>
            <div className="text-sm text-gray-500">
              Word Count: {data.wordCount || 0}
            </div>
          </div>
          <div className="mb-4">
            <div className="text-sm font-medium text-gray-700">Mission:</div>
            <div className="text-sm bg-yellow-50 p-2 rounded-md">{data.missionTitle}</div>
          </div>
          <div className="h-[350px] overflow-y-auto p-4 bg-white border border-gray-200 rounded-lg">
            <p className="whitespace-pre-wrap">{data.content}</p>
          </div>
        </div>

        {/* Review Section */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Review</h3>
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Correction
            </label>
            <textarea
              value={correctionText}
              onChange={(e) => setCorrection(e.target.value)}
              className="w-full h-[150px] p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              placeholder="Enter your corrections..."
            />
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Feedback
            </label>
            <textarea
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              className="w-full h-[150px] p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              placeholder="Enter your feedback..."
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <label className="mr-2 text-sm font-medium text-gray-700">Score:</label>
              <input
                type="number"
                value={score}
                onChange={(e) => setScore(e.target.value)}
                className="w-16 border border-gray-300 rounded px-2 py-1"
                min="0"
                max="100"
                placeholder="0-100"
              />
            </div>
            <button
              onClick={submitReview}
              disabled={!correctionText.trim() || !feedback.trim() || !score}
              className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              Submit Review
            </button>
          </div>
        </div>
      </div>
    </HecSubmissionLayout>
  );
};

export default MissionEssayReviewPage;
