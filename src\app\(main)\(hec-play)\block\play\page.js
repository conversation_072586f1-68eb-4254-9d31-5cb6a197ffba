'use client';
import GoBack from '@/components/shared/GoBack';
import Image from 'next/image';
import React, { useState } from 'react';
import WordBlock from './_components/WordBlock';

const BlockPlay = () => {
  const [activeId, setActiveId] = useState(null);
  const [activeOption, setActiveOption] = useState(null);

  const wordBlocks = [
    { id: 1, word: 'Demo Text 1' },
    { id: 2, word: 'Demo Text 2' },
    { id: 3, word: 'Demo Text 3' },
    { id: 4, word: 'Demo Text 4' },
    { id: 5, word: 'Demo Text 5' },
  ];

  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0">
      <GoBack title={'HEC Play'} linkClass="my-5 mb-8 w-full max-w-40" />

      <div className="p-5 bg-[#EDFDFD] w-full rounded-lg shadow-lg space-y-5 relative">
        <div className="space-y-5">
          <h2 className="text-[#864D0D] text-2xl font-semibold">
            Create sentence using the words in the block.
          </h2>

          <p>Other content will be added here according to the UI.</p>

          <div className='pt-8'>
            <WordBlock data={wordBlocks} activeId={activeId} setActiveId={setActiveId} activeOption={activeOption} setActiveOption={setActiveOption} />
          </div>
        </div>

        <div className="absolute right-4 top-2">
          <div className="relative">
            <Image
              src={'/assets/images/all-img/boardFrame.png'}
              alt={'block-play'}
              width={500}
              height={500}
              className="max-w-52"
            />

            <ul className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-sm min-w-32">
              <li>Total Score: 10</li>
              <li>Time (Minutes): 20</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlockPlay;
