'use client';
import React from 'react';
import { Formik, Form, FieldArray } from 'formik';
import * as Yup from 'yup';
import { Icon } from '@iconify/react';
import FormInput from '@/components/form/FormInput';
import FormDatePicker from '@/components/form/FormDatePicker';
import NumberInput from '@/components/form/NumberInput';
import { useRouter } from 'next/navigation';
import RegularGoBack from '@/components/shared/RegularGoBack';
import FormSelect from '@/components/form/FormSelect';
import api from '@/lib/api';
import { toast } from 'sonner';

// Validation schema for a single mission
const validationSchema = Yup.object().shape({
  title: Yup.string().required('Mission title is required'),
  description: Yup.string().required('Description is required'),
  publishDate: Yup.date().required('Publish date is required'),
  expiryDate: Yup.date()
    .required('Expiry date is required')
    .min(
      Yup.ref('publishDate'),
      'The expiry date must be later than the publish date'
    ),
  targetWordCount: Yup.number()
    .required('Minimum word limit is required')
    .positive('Must be a positive number')
    .integer('Must be an number'),
  targetMaxWordCount: Yup.number()
    .positive('Must be a positive number')
    .integer('Must be an number')
    .min(
      Yup.ref('targetWordCount'),
      'Maximum word limit must be greater than minimum word limit'
    ),
  score: Yup.number()
    .positive('Must be a positive number')
    .integer('Must be an number'),
});

const CreateMissionDiary = () => {
  const router = useRouter();

  // Default initial values if none provided
  const defaultInitialValues = {
    title: '',
    description: '',
    publishDate: '',
    expiryDate: '',
    targetWordCount: '',
    targetMaxWordCount: '',
    score: '',
  };

  const handleSubmit = async (values, { setFieldError }) => {
    try {
      // Submit all missions in one API call
      const response = await api.post('/diary/admin/missions', values);
      console.log(response);

      router.push('/dashboard/module-management/hec-diary/mission-diary');
    } catch (error) {
      console.error('Error creating mission diary:', error);
      const validationErros = error?.response?.data?.validationErrors;
      if (validationErros) {
        Object.keys(validationErros).forEach((field) => {
          setFieldError(`${field}`, validationErros[field][0]);
        });
      }

    }
  };

  return (
    <div className="">
      <RegularGoBack title="Create Mission Diary" />

      <div className="mt-6">
        <Formik
          initialValues={defaultInitialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ values, isSubmitting, handleSubmit }) => (
            <Form onSubmit={handleSubmit}>
              <div className="p-5 bg-white rounded-lg shadow-md border">
                <div className="bg-gray-50 rounded-lg shadow-sm p-6 mb-6 border border-dashed border-gray-300">
                  {/* Mission form fields */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                      <FormInput
                        label="Mission Title"
                        name={`title`}
                        placeholder="Write here"
                        required
                      />
                    </div>
                    <div>
                      <FormInput
                        label="Description"
                        name={`description`}
                        placeholder="Write here"
                        required
                      />
                    </div>
                    <div>
                      <FormDatePicker
                        label="Select Publish Date"
                        name={`publishDate`}
                        required
                      />
                    </div>
                    <div>
                      <NumberInput
                        label="Word Limit (Minimum)"
                        name={`targetWordCount`}
                        placeholder="Enter minimum word limit"
                        required
                      />
                    </div>
                    <div>
                      <NumberInput
                        label="Word Limit (Maximum)"
                        name={`targetMaxWordCount`}
                        placeholder="Enter Maximum word limit"
                        required
                      />
                    </div>
                    <div>
                      <FormDatePicker
                        label="Mission Deadline"
                        name={`expiryDate`}
                        placeholder="Select expiry date"
                        required
                      />
                    </div>
                    <div>
                      <NumberInput
                        label="Score"
                        name={`score`}
                        placeholder="Enter Score"
                        required
                      />
                    </div>
                  </div>
                </div>

                {/* Form actions */}
                <div className="flex justify-end space-x-2">
                  <button
                    type="button"
                    className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded"
                    onClick={() => router.back()}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-yellow-300 hover:bg-yellow-400 text-gray-800 font-medium py-2 px-4 rounded"
                  >
                    Create Mission
                  </button>
                </div>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default CreateMissionDiary;
