'use client';
import React, { useState } from 'react';
import NewTablePage from '@/components/form/NewTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import { useRouter } from 'next/navigation';

const Waterfall = ({ onChangeTab }) => {
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const router = useRouter();

  const { data, isLoading } = useDataFetch({
    queryKey: ['waterfall', currentPage, rowsPerPage],
    endPoint: '/play/waterfall/admin/sets',
    params: { page: currentPage, limit: rowsPerPage },
  });

  const waterFallItems = data?.items || [];

  // console.log(waterFallItems);

  // Pagination logic
  const totalItems = waterFallItems?.length;
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const paginatedItems = waterFallItems?.slice(startIndex, endIndex);

  // Define columns for the table
  const columns = [
    {
      label: 'Question',
      field: 'title',
    },
    {
      label: 'Points',
      field: 'total_score',
    },
    {
      label: 'Questions',
      field: 'total_questions',
    },
  ];

  // Define actions
  const actions = [
    {
      name: 'View',
      icon: 'material-symbols:visibility',
      className: 'text-blue-600 border',
      onClick: (row) => router.push(`/dashboard/module-management/hec-play/waterfall/${row.id}`),
    },
    {
      name: 'Edit',
      icon: 'material-symbols:edit-outline',
      className: 'text-black-600 border',
      onClick: (row) => console.log(row),
    },
    {
      name: 'Delete',
      icon: 'material-symbols:delete-outline',
      className: 'text-red-600 border',
      onClick: (row) => console.log('Delete', row),
    },
  ];

  // Handle page change
  const handleChangePage = (page) => {
    setCurrentPage(page);
  };

  return (
    <div>
      <NewTablePage
        title="Waterfall Question Set List"
        createButton="Create Question Set"
        createPage="/dashboard/module-management/hec-play/waterfall/add"
        createBtnLink="/dashboard/module-management/hec-play/waterfall/add"
        columns={columns}
        data={paginatedItems}
        actions={actions}
        currentPage={currentPage}
        changePage={handleChangePage}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        totalPages={Math.ceil(totalItems / rowsPerPage)}
        showCheckboxes={false}
        showSearch={true}
        showNameFilter={false}
        showSortFilter={false}
      />
    </div>
  );
};

export default Waterfall;
