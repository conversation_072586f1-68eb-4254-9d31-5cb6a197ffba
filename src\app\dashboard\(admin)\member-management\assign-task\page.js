'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

const AssignTask = () => {
  const router = useRouter();
  const [tutor, setTutor] = useState({
    id: '',
    name: '',
    email: '',
    phoneNumber: '',
    bio: ''
  });
  
  // Get tutor data from localStorage on component mount
  useEffect(() => {
    const storedTutor = localStorage.getItem('selectedTutor');
    if (storedTutor) {
      setTutor(JSON.parse(storedTutor));
    } else {
      // Handle case when no tutor data is found
      console.error("No tutor data found in localStorage");
      // Optionally redirect back to the approval list
      // router.push('/dashboard/member-management/approval_lists');
    }
  }, []);
  
  const [selectedRoles, setSelectedRoles] = useState([]);
  
  // Available roles that can be assigned
  const availableRoles = [
    { id: '1', title: 'Mathematics Tutor', description: 'Teach mathematics to high school students' },
    { id: '2', title: 'Science Teacher', description: 'Conduct science experiments and classes' },
    { id: '3', title: 'Language Instructor', description: 'Teach English language skills' },
    { id: '4', title: 'Test Preparation', description: 'Help students prepare for standardized tests' },
    { id: '5', title: 'Homework Assistant', description: 'Help with daily assignments and homework' }
  ];
  
  // Function to handle role selection
  const handleRoleToggle = (roleId) => {
    if (selectedRoles.includes(roleId)) {
      setSelectedRoles(selectedRoles.filter(id => id !== roleId));
    } else {
      setSelectedRoles([...selectedRoles, roleId]);
    }
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Assigned roles to tutor:", tutor.id, selectedRoles);
    alert(`Roles assigned successfully to ${tutor.name}`);
    // Clear the localStorage item after successful assignment
    localStorage.removeItem('selectedTutor');
    router.back();
  };
  
  return (
    <div className="w-full min-h-screen bg-gray-50 px-6 py-6">
      <div className="flex items-center mb-6">
        <button 
          onClick={() => router.back()} 
          className="mr-4 text-gray-600 hover:text-gray-900"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M19 12H5M12 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 className="text-xl font-semibold">Assign Task</h1>
      </div>
      
      <div className="bg-white rounded-lg p-6 shadow-sm mb-6">
        <h2 className="text-3xl font-bold text-center mb-8">Assign Role</h2>
        
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2">Tutor Information</h3>
          <div className="bg-gray-50 p-4 rounded-md">
            <p className="mb-1"><span className="font-medium">Name:</span> {tutor.name}</p>
            <p className="mb-1"><span className="font-medium">Email:</span> {tutor.email}</p>
            <p className="mb-1"><span className="font-medium">Phone:</span> {tutor.phoneNumber}</p>
            <p className="mb-1"><span className="font-medium">Bio:</span> {tutor.bio || 'No bio provided'}</p>
          </div>
        </div>
        
        <form onSubmit={handleSubmit}>
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">Available Roles</h3>
            <p className="text-gray-600 mb-4">Select the roles you want to assign to this tutor</p>
            
            <div className="space-y-3">
              {availableRoles.map((role) => (
                <div key={role.id} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`role-${role.id}`}
                    checked={selectedRoles.includes(role.id)}
                    onChange={() => handleRoleToggle(role.id)}
                    className="h-5 w-5 text-yellow-500 focus:ring-yellow-400"
                  />
                  <label htmlFor={`role-${role.id}`} className="ml-3 block">
                    <span className="font-medium">{role.title}</span>
                    <p className="text-sm text-gray-500">{role.description}</p>
                  </label>
                </div>
              ))}
            </div>
          </div>
          
          <div className="flex justify-end mt-8">
            <button
              type="submit"
              className="bg-[#FFDE34] hover:bg-[#e6c82e] text-black font-medium py-2 px-8 rounded-md transition duration-200"
              disabled={selectedRoles.length === 0}
            >
              Assign Selected Roles
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AssignTask;