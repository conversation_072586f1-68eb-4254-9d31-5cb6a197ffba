import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import Cookies from 'js-cookie';
import { initializeSocket, disconnectSocket } from '@/services/socketService';

/**
 * NotificationProvider component
 * Initializes the socket connection when the user is authenticated
 */
const NotificationProvider = ({ children }) => {
  const { token, isAuth } = useSelector((state) => state.auth);

  useEffect(() => {
    // Initialize socket connection when user is authenticated
    if (isAuth) {
      const authToken = token || Cookies.get('token');
      if (authToken) {
        initializeSocket(authToken);
      }
    }

    // Clean up socket connection on unmount
    return () => {
      disconnectSocket();
    };
  }, [isAuth, token]);

  return <>{children}</>;
};

export default NotificationProvider;
