import { openSans } from '../fonts';
import { Providers } from '../providers/providers';
import './globals.css';

export const metadata = {
  title: 'HEC',
  description: 'This is home page',
  icons: {
    icon: '/assets/images/all-img/Logo.png',
  },
};

export default function RootLayout({ children }) {

  return (
    <html lang="en">
      <body className={`${openSans.variable}`}>
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
