import { ButtonIcon } from '@/components/Button';
import GoBack from '@/components/shared/GoBack';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

const Block = () => {
  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0">
      <GoBack title={'HEC Play'} linkClass="my-5 mb-8 w-full max-w-40" />

      <div className='p-5 bg-[#FDE7E9] w-full rounded-lg shadow-lg '>
        <div className="max-w-5xl mx-auto mb-3 flex items-center justify-between text-center gap-5">
          <div className="space-y-3">
            <Image
              src={'/assets/images/all-img/block.png'}
              alt={'waterfall'}
              width={400}
              height={500}
              className="max-w-[450px] h-auto mx-auto"
            />
            <h1 className="text-5xl font-semibold bg-gradient-to-b from-gray-500 to-gray-900 text-transparent bg-clip-text">
              BLOCK PLAY
            </h1>
          </div>

          <Link
            href={'/block/play'}
            className="flex items-center gap-2 border border-yellow-800 text-3xl text-yellow-900 bg-gradient-to-b from-[#DCA600] to-[#FFDE5B] px-10 py-1 rounded-full mt-3"
          >
            Let's GO{' '}
            <ButtonIcon
              icon={'famicons:play'}
              innerBtnCls={'h-14 w-14'}
              btnIconCls={'h-6 w-6 text-white'}
            />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Block;
