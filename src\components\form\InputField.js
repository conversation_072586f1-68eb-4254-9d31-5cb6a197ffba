import React, { forwardRef } from "react";
import { useField } from "formik";

const InputField = forwardRef(({ label, required, type = "text", as, error = '', ...props }, ref) => {
   const [field, meta] = useField(props);
   const isError = meta.touched && meta.error;

   return (
      <div className="flex flex-col w-full">
         {/* Label */}
         {label && (
            <label
               htmlFor={props.id || props.name}
               className="text-gray-800 text-sm sm:text-base font-semibold mb-1 sm:mb-2"
            >
               {label} {required && <span className="text-red-500">*</span>}
            </label>
         )}

         {/* Input or Textarea based on 'as' prop */}
         {as === "textarea" ? (
            <textarea
               {...field}
               {...props}
               ref={ref}
               className={`border rounded-lg py-3 px-4 focus:outline-none focus:ring-2 transition-all duration-300 ease-in-out shadow-sm focus:border-blue-500 focus:ring-blue-300 resize-none h-32 ${
                  isError ? "border-red-500" : "border-gray-300"
               }`}
            />
         ) : (
            <input
               {...field}
               {...props}
               type={type}
               ref={ref}
               className={`border rounded-lg py-3 px-4 focus:outline-none focus:ring-2 transition-all duration-300 ease-in-out shadow-sm focus:border-blue-500 focus:ring-blue-300 ${
                  isError ? "border-red-500" : "border-gray-300"
               }`}
            />
         )}

         {/* Error Handling */}
         {error && <span className="text-red-500 text-xs">{error}</span>}
         {isError && <span className="text-red-500 text-xs mt-1">{meta.error}</span>}
      </div>
   );
});

export default InputField;
