'use client';

import React, { useState, useEffect } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import api from '@/lib/api';
import { toast } from 'sonner';
import { useRouter, useSearchParams } from 'next/navigation';

const MissionEssayEditor = ({ onSuccessfulSave }) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();
  
  // Check if we're in edit mode
  const isEditMode = searchParams.get('edit') === 'true';
  // Get the mission ID from either URL params or session storage
  const [missionId, setMissionId] = useState(null);
  
  // Time frequency options
  const timeFrequencyOptions = ['Weekly', 'Monthly'];
  
  // Day options
  const dayOptions = ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'];
  
  // Month options
  const monthOptions = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const [missions, setMissions] = useState([
    {
      id: 1,
      title: '',
      description: '',
      selectDay: 'Ex: 1 Day',
      wordLimitMin: '',
      wordLimitMax: '',
      missionDeadline: 'Ex: 1 Day',
      instruction: ''
    }
  ]);

  // Store tasks to remove (we'll handle deletion differently)
  const [tasksToRemove, setTasksToRemove] = useState([]);
  const [timeFrequency, setTimeFrequency] = useState('Weekly');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(isEditMode);
  const [originalMissionData, setOriginalMissionData] = useState(null);

  // Check for mission ID in session storage on component mount
  useEffect(() => {
    if (isEditMode) {
      // First check URL for backward compatibility
      const urlMissionId = searchParams.get('id');
      
      if (urlMissionId) {
        setMissionId(urlMissionId);
      } else {
        // If not in URL, check session storage
        const storedMissionId = sessionStorage.getItem('editMissionId');
        if (storedMissionId) {
          console.log('Retrieved mission ID from session storage:', storedMissionId);
          setMissionId(storedMissionId);
          
          // Check if we also have the mission data in session storage
          const storedMissionData = sessionStorage.getItem('editMissionData');
          if (storedMissionData) {
            try {
              const parsedData = JSON.parse(storedMissionData);
              console.log('Retrieved mission data from session storage:', parsedData);
              setOriginalMissionData(parsedData);
              populateForm(parsedData);
              setIsLoading(false);
            } catch (error) {
              console.error('Error parsing mission data from session storage:', error);
              // Will fall back to API fetch below
            }
          }
        } else {
          console.error('No mission ID found in URL or session storage');
          toast.error('Could not find mission to edit');
          setIsLoading(false);
        }
      }
    }
  }, [isEditMode, searchParams]);

  // Create mutation for saving missions
  const createMissionMutation = useMutation({
    mutationFn: async (formData) => {
      // If in edit mode, use update endpoint
      if (isEditMode && missionId) {
        // For updates, we need to send a PATCH request with the mission ID in the path
        console.log('Sending PATCH request to:', `/admin-essay/${missionId}`);
        console.log('With payload:', JSON.stringify(formData, null, 2));
        const response = await api.patch(`/admin-essay/${missionId}`, formData);
        return response;
      } else {
        const response = await api.post('/admin-essay/create', formData);
        return response;
      }
    },
    onSuccess: () => {
      // Clean up session storage after successful save
      sessionStorage.removeItem('editMissionId');
      sessionStorage.removeItem('editMissionData');
      
      // Invalidate queries to refetch data
      queryClient.invalidateQueries(['missionsList']);
      
      // Call onSuccessfulSave if provided
      if (typeof onSuccessfulSave === 'function') {
        onSuccessfulSave();
      } else {
        // Default behavior - navigate back to mission list
        router.push('/dashboard/module-management/hec-essay?tab=missionEssayList');
      }
    },
    onError: (error) => {
      console.error(`Error ${isEditMode ? 'updating' : 'creating'} missions:`, error);
    },
    onSettled: () => {
      setIsSubmitting(false);
    }
  });

  // Load mission data from API if in edit mode
  useEffect(() => {
    if (isEditMode && missionId && !originalMissionData) {
      fetchMissionData();
    }
  }, [isEditMode, missionId, originalMissionData]);

  // Fetch mission data
  const fetchMissionData = async () => {
    if (!missionId) {
      setIsLoading(false);
      return;
    }
    
    try {
      // Log the fetch request
      console.log(`Fetching mission data for ID: ${missionId}`);
      const response = await api.get(`/admin-essay//${missionId}`);
      
      console.log('API response:', response);
      
      if (response && response.success) {
        setOriginalMissionData(response.data);
        populateForm(response.data);
      } else {
        toast.error('Failed to fetch mission data');
      }
    } catch (error) {
      console.error('Error fetching mission data:', error);
      toast.error('Error loading mission data');
    } finally {
      setIsLoading(false);
    }
  };

  // Populate form with mission data
  const populateForm = (missionData) => {
    if (!missionData || !missionData.tasks || missionData.tasks.length === 0) {
      toast.error('No mission tasks found');
      return;
    }
    
    // Set time frequency
    setTimeFrequency(missionData.timeFrequency === 'weekly' ? 'Weekly' : 'Monthly');
    
    // Map tasks to mission form format
    const mappedMissions = missionData.tasks.map((task, index) => {
      // Determine select day/month value based on metadata
      let selectDayValue = 'Ex: 1 Day';
      if (missionData.timeFrequency === 'weekly' && task.metaData?.week) {
        selectDayValue = `Day ${task.metaData.week}`;
      } else if (missionData.timeFrequency === 'monthly' && task.metaData?.month) {
        const monthIndex = parseInt(task.metaData.month) - 1;
        if (monthIndex >= 0 && monthIndex < monthOptions.length) {
          selectDayValue = monthOptions[monthIndex];
        }
      }

      // Determine deadline value
      let deadlineValue = 'Ex: 1 Day';
      if (task.deadline) {
        deadlineValue = `Day ${task.deadline}`;
      }

      // Log the task mapping
      console.log(`Mapping task ID ${task.id} with title: ${task.title}`);

      return {
        id: index + 1, // Form UI ID
        originalId: task.id, // Store original task ID for API reference
        title: task.title || '',
        description: task.description || '',
        selectDay: selectDayValue,
        wordLimitMin: task.wordLimitMinimum?.toString() || '',
        wordLimitMax: task.wordLimitMaximum?.toString() || '',
        missionDeadline: deadlineValue,
        instruction: task.instructions || '',
        // Keep track of whether this is a new or existing task
        isExisting: true
      };
    });
    
    console.log('Mapped missions:', mappedMissions);
    setMissions(mappedMissions);
  };

  const addMoreMission = () => {
    const newId = missions.length > 0 ? Math.max(...missions.map(m => m.id)) + 1 : 1;
    setMissions([
      ...missions,
      {
        id: newId,
        title: '',
        description: '',
        selectDay: 'Ex: 1 Day',
        wordLimitMin: '',
        wordLimitMax: '',
        missionDeadline: 'Ex: 1 Day',
        instruction: '',
        isExisting: false // Mark as a new task
      }
    ]);
  };

  const removeMission = (id) => {
    // Only allow removal if there's more than one mission
    if (missions.length > 1) {
      // Find the mission to remove
      const missionToRemove = missions.find(m => m.id === id);
      
      // If this is an existing task, add it to the tasks to remove list
      if (missionToRemove && missionToRemove.originalId && missionToRemove.isExisting) {
        console.log(`Marking task ID ${missionToRemove.originalId} for removal`);
        setTasksToRemove([...tasksToRemove, missionToRemove.originalId]);
      }
      
      // Remove from state
      setMissions(missions.filter(mission => mission.id !== id));
    } else {
      toast.warning('At least one mission is required');
    }
  };

  const handleInputChange = (id, field, value) => {
    setMissions(missions.map(mission => 
      mission.id === id ? { ...mission, [field]: value } : mission
    ));
  };

  const handleCancel = () => {
    // Clean up session storage
    sessionStorage.removeItem('editMissionId');
    sessionStorage.removeItem('editMissionData');
    
    // Navigate back to the previous page
    router.push('/dashboard/module-management/hec-essay?tab=missionEssayList');
  };

  const validateForm = () => {
    for (const mission of missions) {
      if (!mission.title || !mission.description || !mission.instruction) {
        toast.error('Please fill in all required fields for each mission');
        return false;
      }
      
      // Validate word limits are numbers
      if (isNaN(Number(mission.wordLimitMin)) || (mission.wordLimitMax && isNaN(Number(mission.wordLimitMax)))) {
        toast.error('Word limits must be valid numbers');
        return false;
      }
      
      // Ensure minimum word limit is specified
      if (!mission.wordLimitMin || Number(mission.wordLimitMin) <= 0) {
        toast.error('Minimum word limit must be a positive number');
        return false;
      }
      
      // Validate that max word limit is greater than min (if specified)
      if (mission.wordLimitMax && Number(mission.wordLimitMax) <= Number(mission.wordLimitMin)) {
        toast.error('Maximum word limit must be greater than minimum word limit');
        return false;
      }
    }
    return true;
  };

  const handleSave = () => {
    if (!validateForm()) {
      return;
    }
  
    setIsSubmitting(true);
    
    // Format the data for API
    const formattedTasks = missions.map((mission) => {
      // Get current date for metadata
      const now = new Date();
      
      // Extract numeric values from day selections
      let dayNumber = '1';
      if (mission.selectDay.startsWith('Day ')) {
        dayNumber = mission.selectDay.replace('Day ', '');
      }
      
      let deadlineNumber = '7';  
      if (mission.missionDeadline.startsWith('Day ')) {
        deadlineNumber = mission.missionDeadline.replace('Day ', '');
      }
      
      // Create base task data
      const taskData = {
        title: mission.title,
        description: mission.description,
        wordLimitMinimum: Number(mission.wordLimitMin),
        wordLimitMaximum: mission.wordLimitMax ? Number(mission.wordLimitMax) : null,
        timePeriodUnit: Number(dayNumber),
        deadline: Number(deadlineNumber),
        instructions: mission.instruction,
        metaData: {
          week: dayNumber,
          month: (timeFrequency === 'Monthly' 
            ? (monthOptions.indexOf(mission.selectDay) + 1) 
            : now.getMonth() + 1).toString(),
          year: now.getFullYear().toString()
        }
      };
      
      // In edit mode, include original task ID if it exists
      if (mission.originalId) {
        taskData.id = mission.originalId;
        console.log(`Including original task ID ${mission.originalId} in payload`);
      }
      
      return taskData;
    });
  
    // Build the payload based on whether we're creating or updating
    let payload;
    
    if (isEditMode && missionId) {
      // For updates, we need a simpler structure without the deletedTaskIds property
      payload = {
        timeFrequency: timeFrequency.toLowerCase(),
        tasks: formattedTasks
      };
    } else {
      // For create, use the same structure
      payload = {
        timeFrequency: timeFrequency.toLowerCase(),
        tasks: formattedTasks
      };
    }
  
    // Debug payload before sending
    console.log('Sending payload:', JSON.stringify(payload, null, 2));
    
    createMissionMutation.mutate(payload);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="sr-only">Loading...</span>
          </div>
          <p className="mt-2">Loading mission data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white font-sans">
      <div className="container mx-auto px-4 py-6">
        <h1 className="card-title text-black text-xl">
          {isEditMode ? 'Edit Mission Essay' : 'Create Mission Essay'}
        </h1>
        <form className="bg-white space-y-6">
          {/* Time Frequency selector */}
          <div className="bg-[#FFFAC2] rounded-lg p-4 max-w-lg mx-auto text-center">
            <div className="mb-4">
              <label htmlFor="time-frequency" className="block text-sm font-semibold text-black mb-2">
                Time Frequency<span className="text-red-600">*</span>
              </label>
              <div className="relative">
                <select
                  id="time-frequency"
                  name="time-frequency"
                  value={timeFrequency}
                  onChange={(e) => setTimeFrequency(e.target.value)}
                  className="w-full text-base rounded-md border border-gray-300 px-4 py-3 appearance-none bg-white focus:outline-none focus:ring-1 focus:ring-yellow-400"
                >
                  {timeFrequencyOptions.map((option) => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <svg className="fill-current h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Map through all missions and display each one */}
          {missions.map((mission, index) => (
            <div key={mission.id} className="bg-gray-50 p-6 rounded-lg relative">
              {/* Close/Remove button */}
              {missions.length > 1 && (
                <button
                  type="button"
                  className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 focus:outline-none"
                  onClick={() => removeMission(mission.id)}
                  aria-label="Remove mission"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
              
              {/* Mission number indicator */}
              <div className="mb-4 pb-2 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-800">
                  Mission Essay {index + 1}
                  {mission.originalId && <span className="text-xs text-gray-500 ml-2">(ID: {mission.originalId})</span>}
                </h2>
              </div>
              
              {/* Mission Q & A Title, Description, Select Day/Month row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label
                    htmlFor={`mission-title-${mission.id}`}
                    className="block text-sm font-semibold text-black mb-2"
                  >
                    Mission Essay Title<span className="text-red-600">*</span>
                  </label>
                  <input
                    id={`mission-title-${mission.id}`}
                    name={`mission-title-${mission.id}`}
                    type="text"
                    value={mission.title}
                    placeholder="Write here"
                    className="w-full text-base rounded border border-gray-300 px-4 py-3 focus:outline-none focus:ring-1 focus:ring-yellow-400"
                    onChange={(e) => handleInputChange(mission.id, 'title', e.target.value)}
                  />
                </div>
                <div>
                  <label
                    htmlFor={`description-${mission.id}`}
                    className="block text-sm font-semibold text-black mb-2"
                  >
                    Description<span className="text-red-600">*</span>
                  </label>
                  <input
                    id={`description-${mission.id}`}
                    name={`description-${mission.id}`}
                    type="text"
                    value={mission.description}
                    placeholder="Write here"
                    className="w-full text-base rounded border border-gray-300 px-4 py-3 focus:outline-none focus:ring-1 focus:ring-yellow-400"
                    onChange={(e) => handleInputChange(mission.id, 'description', e.target.value)}
                  />
                </div>
                <div>
                  <label
                    htmlFor={`select-day-${mission.id}`}
                    className="block text-sm font-semibold text-black mb-2"
                  >
                    {timeFrequency === 'Weekly' ? 'Select Day' : 'Select Month'}<span className="text-red-600">*</span>
                  </label>
                  <div className="relative">
                    <select
                      id={`select-day-${mission.id}`}
                      name={`select-day-${mission.id}`}
                      value={mission.selectDay}
                      className="w-full text-base rounded border border-gray-300 px-4 py-3 appearance-none focus:outline-none focus:ring-1 focus:ring-yellow-400"
                      onChange={(e) => handleInputChange(mission.id, 'selectDay', e.target.value)}
                    >
                      <option value="" disabled>
                        {timeFrequency === 'Weekly' ? 'select days' : 'select month'}
                      </option>
                      {timeFrequency === 'Weekly' 
                        ? dayOptions.map(day => (
                            <option key={day} value={day}>{day}</option>
                          ))
                        : monthOptions.map(month => (
                            <option key={month} value={month}>{month}</option>
                          ))
                      }
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                      <svg className="fill-current h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                        <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Word Limit (Minimum), Word Limit (Maximum), Mission Deadline row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div>
                  <label
                    htmlFor={`word-limit-min-${mission.id}`}
                    className="block text-sm font-semibold text-black mb-2"
                  >
                    Word Limit (Minimum)<span className="text-red-600">*</span>
                  </label>
                  <input
                    id={`word-limit-min-${mission.id}`}
                    name={`word-limit-min-${mission.id}`}
                    type="text"
                    value={mission.wordLimitMin}
                    placeholder="Enter minimum word limit"
                    className="w-full text-base rounded border border-gray-300 px-4 py-3 focus:outline-none focus:ring-1 focus:ring-yellow-400"
                    onChange={(e) => handleInputChange(mission.id, 'wordLimitMin', e.target.value)}
                  />
                </div>
                <div>
                  <label
                    htmlFor={`word-limit-max-${mission.id}`}
                    className="block text-sm font-semibold text-black mb-2"
                  >
                    Word Limit (Maximum)
                  </label>
                  <input
                    id={`word-limit-max-${mission.id}`}
                    name={`word-limit-max-${mission.id}`}
                    type="text"
                    value={mission.wordLimitMax}
                    placeholder="Enter Maximum word limit"
                    className="w-full text-base rounded border border-gray-300 px-4 py-3 focus:outline-none focus:ring-1 focus:ring-yellow-400"
                    onChange={(e) => handleInputChange(mission.id, 'wordLimitMax', e.target.value)}
                  />
                </div>
                <div>
                  <label
                    htmlFor={`mission-deadline-${mission.id}`}
                    className="block text-sm font-semibold text-black mb-2"
                  >
                    Mission Deadline
                  </label>
                  <div className="relative">
                    <select
                      id={`mission-deadline-${mission.id}`}
                      name={`mission-deadline-${mission.id}`}
                      value={mission.missionDeadline}
                      className="w-full text-base rounded border border-gray-300 px-4 py-3 appearance-none focus:outline-none focus:ring-1 focus:ring-yellow-400"
                      onChange={(e) => handleInputChange(mission.id, 'missionDeadline', e.target.value)}
                    >
                      <option>Ex: 1 Day</option>
                      {dayOptions.map(day => (
                        <option key={`deadline-${day}`} value={day}>{day}</option>
                      ))}
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                      <svg className="fill-current h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                        <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Instruction */}
              <div className="mt-4">
                <label
                  htmlFor={`instruction-${mission.id}`}
                  className="block text-sm font-semibold text-black mb-2"
                >
                  Instruction<span className="text-red-600">*</span>
                </label>
                <textarea
                  id={`instruction-${mission.id}`}
                  name={`instruction-${mission.id}`}
                  rows="3"
                  value={mission.instruction}
                  placeholder="Write instruction here"
                  className="w-full text-base rounded border border-gray-300 px-4 py-3 resize-none focus:outline-none focus:ring-1 focus:ring-yellow-400"
                  onChange={(e) => handleInputChange(mission.id, 'instruction', e.target.value)}
                ></textarea>
              </div>
            </div>
          ))}

          <div className="flex justify-center mt-6">
            <button
              type="button"
              className="inline-flex items-center gap-2 bg-[#FFDE34] text-black text-base rounded px-6 py-3 shadow-md hover:bg-yellow-300 focus:outline-none focus:ring-2 focus:ring-yellow-400"
              onClick={addMoreMission}
              disabled={isSubmitting}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              Add More Mission
            </button>
          </div>
        </form>

        <div className="flex justify-end gap-4 mt-6 mb-8">
          <button
            type="button"
            className="bg-[#D4D4D4] text-black text-base rounded px-8 py-3 shadow-md hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-400"
            onClick={handleCancel}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="button"
            className="bg-[#FFDE34] text-black text-base rounded px-8 py-3 shadow-md hover:bg-yellow-300 focus:outline-none focus:ring-2 focus:ring-yellow-400"
            onClick={handleSave}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default MissionEssayEditor;