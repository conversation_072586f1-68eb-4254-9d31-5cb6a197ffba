'use client';

import React from 'react';
import Tooltip from './Tooltip';
import { Icon } from '@iconify/react';

/**
 * Demo component to showcase the updated Tooltip component with new props
 */
const TooltipDemo = () => {
  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-4">Tooltip Demo</h2>
      
      <div className="space-y-6">
        {/* Basic tooltip with new props */}
        <div className="flex items-center gap-4">
          <Tooltip 
            content="This is a yellow tooltip positioned to the left" 
            position="left"
            color="yellow"
            size="lg"
          >
            <button className="px-4 py-2 bg-blue-500 text-white rounded-md">
              Hover Me
            </button>
          </Tooltip>
          
          <span className="text-gray-500">← Hover to see the customized tooltip</span>
        </div>
        
        {/* Different positions */}
        <div>
          <h3 className="text-lg font-medium mb-2">Different Positions</h3>
          <div className="flex flex-wrap gap-4">
            <Tooltip content="Top tooltip" position="top" color="yellow" size="md">
              <button className="px-3 py-1 bg-gray-200 text-gray-800 rounded-md">Top</button>
            </Tooltip>
            
            <Tooltip content="Right tooltip" position="right" color="yellow" size="md">
              <button className="px-3 py-1 bg-gray-200 text-gray-800 rounded-md">Right</button>
            </Tooltip>
            
            <Tooltip content="Bottom tooltip" position="bottom" color="yellow" size="md">
              <button className="px-3 py-1 bg-gray-200 text-gray-800 rounded-md">Bottom</button>
            </Tooltip>
            
            <Tooltip content="Left tooltip" position="left" color="yellow" size="md">
              <button className="px-3 py-1 bg-gray-200 text-gray-800 rounded-md">Left</button>
            </Tooltip>
          </div>
        </div>
        
        {/* Different sizes */}
        <div>
          <h3 className="text-lg font-medium mb-2">Different Sizes</h3>
          <div className="flex flex-wrap gap-4">
            <Tooltip content="Small tooltip" color="yellow" size="sm">
              <button className="px-3 py-1 bg-gray-200 text-gray-800 rounded-md">Small</button>
            </Tooltip>
            
            <Tooltip content="Medium tooltip" color="yellow" size="md">
              <button className="px-3 py-1 bg-gray-200 text-gray-800 rounded-md">Medium</button>
            </Tooltip>
            
            <Tooltip content="Large tooltip" color="yellow" size="lg">
              <button className="px-3 py-1 bg-gray-200 text-gray-800 rounded-md">Large</button>
            </Tooltip>
          </div>
        </div>
        
        {/* With icons */}
        <div>
          <h3 className="text-lg font-medium mb-2">With Icons</h3>
          <div className="flex flex-wrap gap-4">
            <Tooltip content="Help information" position="left" color="yellow" size="lg">
              <button className="p-2 rounded-full bg-yellow-100 text-yellow-800">
                <Icon icon="lucide:help-circle" className="w-5 h-5" />
              </button>
            </Tooltip>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TooltipDemo;
