'use client'
import Image from 'next/image';
import React from 'react';

export const BeliefCard = ({ title, description, imageSrc, description2, rightAlign = false }) => {
  return (
    <div className="flex flex-col items-center space-y-3 max-w-72 mx-auto">
      <div className={`flex flex-col w-full space-y-2 ${rightAlign ? 'items-end' : 'items-start'}`}>
        <Image
          src={imageSrc}
          alt="Icon"
          width={120}
          height={120}
          className="max-w-20"
        />
        <h2 className="text-3xl font-semibold">{title}</h2>
      </div>
      <p className={`text-gray-600 ${rightAlign ? 'text-right' : 'text-left'}`}>{description}</p>
      <p className={`text-gray-900 text-xl ${rightAlign ? 'text-right' : 'text-left'}`}>{description2}</p>
    </div>
  );
};

const OurBeliefSection = () => {
  return (
    <div className="min-h-[90vh] flex items-center overflow-hidden relative">
      <Image
        src={'/assets/images/all-img/introduction/ourBeliefBg.png'}
        alt="section bg"
        width={1920}
        height={900}
        className="absolute z-10"
      />

      <div className="py-14 max-w-7xl mx-auto relative z-10">
        <h2 className="text-3xl md:text-5xl text-center mb-14 text-gray-700">Our Beliefs Regarding HEC</h2>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-3 items-center">
          <div className="space-y-5">
            <BeliefCard
              title="Community"
              rightAlign={true}
              description="Members engage in discussions, share resources, and solve problems together."
              description2="Sign in to join our collaborative community"
              imageSrc="/assets/images/all-img/introduction/headerpart6.png"
            />
            <BeliefCard
              title="Learning"
              rightAlign={true}
              description="Includes Diary writing, playing, Q&A and peer-to-peer interactions. Encourages lifelong learning through shared experiences and knowledge exchange."
              description2="Sign in is required to join in the learning process ."
              imageSrc="/assets/images/all-img/introduction/headerpart3.png"
            />
          </div>

          <div className="space-y-5">
            <div className="shadow-lg rounded-full p-4 overflow-hidden">
              <Image
                src={'/assets/images/all-img/introduction/headerpart1.png'}
                alt="section image"
                width={800}
                height={600}
                className="w-full min-w-60 md:min-w-80 mx-auto"
              />
            </div>

            <p className=" text-black flex items-center gap-2 justify-center">
              <span className='text-3xl font-semibold'>2220+</span> active users
            </p>
          </div>

          <div className="space-y-5">
            <BeliefCard
              title="Growth"
              description="Encourages lifelong learning through shared experiences and knowledge exchange."
              description2="Embracing challenges and feedback leads to  self-improvement."
              imageSrc="/assets/images/all-img/introduction/headerpart4.png"
            />
            <BeliefCard
              title="Creativity"
              description="Writing stimulates the mind to think beyond boundaries, fostering originality."
              description2="Structuring thoughts in writing enhances analytical and creative thinking."
              imageSrc="/assets/images/all-img/introduction/headerpart2.png"
            />
          </div>
        </div>
      </div>

    </div>
  );
};

export default OurBeliefSection;
