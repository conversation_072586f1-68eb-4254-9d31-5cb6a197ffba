'use client';
import React from 'react';
import Image from 'next/image';

const OwnedItemPage = () => {
  // Sample owned emoji items data
  const ownedItems = Array(12).fill().map((_, index) => ({
    id: index + 1,
    emoji: '😊',
    type: 'owned'
  }));

  // Sample recommended emoji items data
  const recommendedItems = Array(12).fill().map((_, index) => {
    // Alternate between regular emoji and heart emoji for the second item
    const isHeartEmoji = index % 8 === 1 || index % 8 === 6;
    return {
      id: 100 + index,
      emoji: isHeartEmoji ? '💛' : '😀',
      type: 'recommended'
    };
  });

  return (
    <div className="container mx-auto p-4 bg-[#e6f7f7] min-h-screen">
      {/* My owned items section */}
      <h1 className="text-2xl font-bold text-[#8B4513] mb-6 ml-2">My owned item</h1>

      <div className="flex flex-wrap gap-4 mb-12">
        {ownedItems.map((item) => (
          <div
            key={item.id}
            className="bg-white rounded-lg shadow-md p-4 flex items-center justify-center cursor-pointer hover:shadow-lg transition-all duration-300 w-[144px] h-[144px]"
          >
            <span className="text-4xl">{item.emoji}</span>
          </div>
        ))}
      </div>

      {/* Decorative floral divider */}
      <div className="flex justify-center mb-8">
        <div className="relative w-full max-w-3xl flex items-center">
          <div className="h-0.5 bg-green-500 w-full absolute"></div>
          <div className="w-full flex justify-between items-center relative px-4">
            {/* Leaf and flower pattern */}
            <div className="flex items-center w-full justify-between">
              <div className="text-green-500 text-2xl">🍃</div>
              <div className="text-pink-400 text-xl">🌸</div>
              <div className="text-green-500 text-2xl transform rotate-45">🍃</div>
              <div className="text-pink-400 text-xl">🌸</div>
              <div className="text-green-500 text-2xl transform -rotate-45">🍃</div>
              <div className="text-pink-400 text-xl">🌸</div>
              <div className="text-green-500 text-2xl">🍃</div>
            </div>
          </div>
        </div>
      </div>

      {/* You may also try section */}
      <h2 className="text-xl font-bold text-[#8B4513] text-center mb-6">You may also try</h2>

      <div className="flex flex-wrap gap-4">
        {recommendedItems.map((item) => (
          <div
            key={item.id}
            className="bg-white rounded-lg shadow-md p-4 flex items-center justify-center cursor-pointer hover:shadow-lg transition-all duration-300 w-[144px] h-[144px]"
          >
            <span className="text-4xl">{item.emoji}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default OwnedItemPage;
