'use client';
import React, { useState } from 'react';
import Button from '@/components/Button';
import Modal from '@/components/Modal';

/**
 * A button that opens a modal when clicked.
 */
const ModalButton = ({
  buttonText,
  icon,
  isSquareBtn = true,
  buttonClassName = '',
  children,
  modalPosition = 'center',
  modalWidth = 'md',
  modalTitle = '',
  scrollable = true,
  maxHeight = '80vh',
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const openModal = () => setIsOpen(true);
  const closeModal = () => setIsOpen(false);

  return (
    <>
      <Button
        icon={icon}
        buttonText={buttonText}
        isSquareBtn={isSquareBtn}
        onClick={openModal}
        className={buttonClassName}
      />

      <Modal
        isOpen={isOpen}
        onClose={closeModal}
        position={modalPosition}
        width={modalWidth}
        title={modalTitle}
        scrollable={scrollable}
        maxHeight={maxHeight}
      >
        {children}
      </Modal>
    </>
  );
};

export default ModalButton;
