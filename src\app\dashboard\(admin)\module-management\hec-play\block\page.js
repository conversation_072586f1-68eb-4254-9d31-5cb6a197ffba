'use client';
import React, { useState } from 'react';
import NewTablePage from "@/components/form/NewTablePage";

const Block = ({ onChangeTab }) => {
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  
  // Dummy data for block games
  const dummyItems = [
    {
      id: '1',
      title: 'Block Puzzle 1',
      difficulty: 'Easy',
      status: 'Active',
    },
    {
      id: '2',
      title: 'Block Builder',
      difficulty: 'Medium',
      status: 'Active',
    },
    {
      id: '3',
      title: 'Advanced Block Challenge',
      difficulty: 'Hard',
      status: 'Inactive',
    },
  ];

  // Pagination logic
  const totalItems = dummyItems.length;
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const paginatedItems = dummyItems.slice(startIndex, endIndex);

  // Define columns for the table
  const columns = [
    {
      label: 'Title',
      field: 'title',
    },
    {
      label: 'Difficulty',
      field: 'difficulty',
    },
    {
      label: 'Status',
      field: 'status',
    },
  ];

  // Define actions
  const actions = [
    {
      name: 'Edit',
      icon: 'lucide:edit',
      onClick: (row) => console.log('Edit', row),
    },
    {
      name: 'Delete',
      icon: 'lucide:trash',
      onClick: (row) => console.log('Delete', row),
    },
  ];

  // Handle page change
  const handleChangePage = (page) => {
    setCurrentPage(page);
  };

  return (
    <div className="container mx-auto p-4">
      <NewTablePage
        title="Block Games"
        createButton="Add Block Game"
        columns={columns}
        data={paginatedItems}
        actions={actions}
        currentPage={currentPage}
        changePage={handleChangePage}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        totalPages={Math.ceil(totalItems / rowsPerPage)}
        showCheckboxes={false}
        showSearch={true}
        showNameFilter={true}
        showSortFilter={true}
      />
    </div>
  );
};

export default Block;
